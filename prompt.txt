我现在要开发一款面向自动化装备制造⾏业的AI供应链服务平台，赋能研发、采购及供应三个关键环节。本项目是针对下游设备制造商/集成商的研发采购协同系统。菜单及说明如下：
1. 首页
核心功能：全局态势感知
顶部预警栏：滚动显示交期延误风险（红色高亮）
左半区：项目矩阵视图（气泡图显示项目规模/进度）
右半区：供应商效能排行榜（TOP5/N末位5）
底部：快捷操作区（拖拽上传BOM按钮）

2. 项目管理
2.1 项目列表
阶段追踪：需求确认→供应商筛选→合同签订→生产监控→验收结算
成本监控：预算水位/实际支出/紧急采购标识
风险预警：长周期件延迟标红/独家供应提示

2.2 项目启动
BOM关联：自动解析生成采购清单（标品/定制件/服务类）
智能分包：按物料类型/地域/紧急度自动拆解采购包
基因继承：复用历史项目供应商池/合同模板/质检标准

3. BOM中心
3.1 BOM列表
BOM上传/解析：上传BOM并自动解析，匹配数据库中已有商品，给出参考价格
生成采购需求：自动跳转至采购项目创建

3.2 BOM分类
自动标记：标准件（绿色）/非标件（橙色）/外购组件（紫色）
冲突检测：重复物料/矛盾的技术参数
替代建议：推荐成本更优的物料（需工程师确认）

3.3 物料详情
三维信息卡：
技术参数（带行业标准对比）
供应商图谱（当前合作+潜在供应商）
采购历史（价格趋势/质量波动）
关联入口：直达相关图纸/订单记录

4. 图纸协同
4.1 图纸上传
格式支持：AutoCAD/SolidWorks/STEP+PDF
智能命名：自动提取图号（如：ASM-2024-MC-001）
水印策略：自动添加"仅限报价使用"浮动水印

4.2 版本管理
可视化对比：高亮显示版本间差异
关联变更单：每次修改对应ECO编号
冻结机制：发布询价后自动锁定版本

4.3 在线批注
协作层系统：
采购批注层（紫色：交期问题）
技术批注层（红色：工艺冲突）
供应商批注层（绿色：加工建议）
批注追踪：未读批注闪烁提醒

5. 供应商工作台
5.1 询价管理
智能分派：根据供应商专长自动分配（如：铸造件→供应商A）
进度追踪：供应商查看状态（已读/正在报价/已提交）
自动催办：超时未响应自动发送提醒

5.2 报价比较

5.3 供应商目录
立体档案：
基础信息（产能/认证）
动态数据（当前负荷率）
合作画像（历史配合度）
地图模式：显示地理位置及物流时效

6. 订单管理
6.1 订单生成
智能合同：自动填充条款（验收标准/违约金）
风险检查：提示独家供应/预付款超限等风险
数字签名：供应商在线签署

6.2 订单跟踪
状态矩阵：
原材料准备（供应商侧）
在制品进度（工序报工）
物流在途（GPS追踪）
预测算法：动态更新预计到货日

6.3 异常处理
分级预警：
黄色（可内部解决）
橙色（需供应商介入）
红色（需高层协调）
处理看板：记录根本原因/改进措施

7. 数据分析
7.1 成本分析
分层拆解：项目成本/材料成本/加工费/物流费/管理费
标杆对比：与行业TOP10%企业对比差距
模拟器：汇率波动对总成本的影响预测

7.2 供应商评估
多维度雷达图：
质量（退货率）
交期（准时率）
成本（降价幅度）
服务（响应速度）
改进建议：生成供应商专属发展计划

7.3 项目报告
自动生成PPT：
采购效能总结
供应商绩效排名
项目经验教训
数据沙盒：自定义分析维度

8. 系统设置
8.1 权限管理
角色矩阵：
采购员（完整采购链权限）
工程师（技术数据+只读采购）
财务（成本数据+审批）
审计追踪：操作记录留存

8.2 模板配置
BOM模板库：按设备类型预设（机械臂/AGV/检测机）
合同条款库：法律审核过的标准条款
报告模板：企业VI标准化设计

8.3 数据迁移中心
历史询价：导入以往的询价数据
历史交易：导入以往的交易数据
供应商：导入已经积累的供应商


你现在是一名资深产品经理和UI交互设计师，请你规划这些界面，并思考原型设计。我需要你在一份html里输出以上所有高保真原型界面。
原型设计要求：
0. 每个页面按照1920*1080设计。
1. 使用ant-design-vue组件库里的组件，请参考https://www.antdv.com/components/overview-cn
2. 每个页面顶部预留150px的高度的dev，背景为#2a2a35，里面的内容不用你设计
3. 顶部区域和下方之间留有空隙，下方区域背景色为#eff2f7，中间为实际使用区域，总宽度为1440px，背景白色，水平居中。在这个区域内，左边是菜单，右边是具体内容区，其中菜单宽度250px，
4. 主色使用#2a2a35和白色，交互色（按钮、链接等）使用#f94c30
5. 数据可视化遵循：
红色系：风险/异常
绿色系：完成/正常
蓝色系：进行中
5. 可以使用 FontAwesome、iconfont 等开源图标库，让原型显得更精美