import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory('/bomai/'),
  routes: [
    // 独立页面路由 - 不受主应用布局影响
    { 
      path: '/workSpace/company-space/ans-price', 
      component: () => import('./views/purchase/rfq/shared.vue'),
      meta: { 
        standalone: true, // 标记为独立页面
        layout: 'blank' // 使用空白布局
      }
    },
    { 
      path: '/supplier/temp-quote', 
      component: () => import('./views/supplier/temp-quote.vue'),
      meta: { 
        standalone: true, // 标记为独立页面
        layout: 'blank' // 使用空白布局
      }
    },
    { 
      path: '/supplier/quote-success', 
      component: () => import('./views/supplier/quote-success.vue'),
      meta: { 
        standalone: true, // 标记为独立页面
        layout: 'blank' // 使用空白布局
      }
    },
    
    // 主应用内部路由
    { path: '/', redirect: '/dashboard' },
    { path: '/project/index', component: () => import('./views/project/index.vue') },
    { path: '/project/detail', component: () => import('./views/project/detail.vue') },
    { path: '/bom/index', component: () => import('./views/bom/index.vue') },
    { path: '/purchase/rfq', component: () => import('./views/purchase/rfq/index.vue') },
    { path: '/purchase/rfq/detail/:rfqNo', component: () => import('./views/purchase/rfq/rfqDetail.vue') },
    { path: '/purchase/rfq/shared', component: () => import('./views/purchase/rfq/shared.vue') },
    { path: '/purchase/po', component: () => import('./views/purchase/po/index.vue') },
    { path: '/purchase/po/apply', component: () => import('./views/purchase/po/apply.vue') },
    { path: '/purchase/poDetail', component: () => import('./views/purchase/po/poDetail.vue') },
    { path: '/purchase/dn', component: () => import('./views/purchase/dn/index.vue') },
    { path: '/purchase/dnDetail', component: () => import('./views/purchase/dn/dnDetail.vue') },
    { path: '/purchase/inv', component: () => import('./views/purchase/inv/index.vue') },
    { path: '/purchase/inv/detail/:id', component: () => import('./views/purchase/inv/invDetail.vue') },
    { path: '/purchase/pay', component: () => import('./views/purchase/pay/index.vue') },
    { path: '/purchase/payDetail', component: () => import('./views/purchase/pay/payDetail.vue') },
    { path: '/supplier/index', component: () => import('./views/supplier/index.vue') },
    { path: '/supplier/private', component: () => import('./views/supplier/private.vue') },
    { path: '/enterprise/trade', component: () => import('./views/enterprise/trade.vue') },
    { path: '/notification', component: () => import('./views/notification/index.vue') },
    { path: '/dashboard', component: () => import('./views/dashboard/index.vue') },
  ],
})

export default router