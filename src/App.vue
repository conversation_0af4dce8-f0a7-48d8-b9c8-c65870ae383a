<template>
  <a-config-provider
    :locale="locale"
    :theme="{
      token: {
        borderRadius: 2,

        colorPrimary: '#f94c30',
        colorInfo: '#f94c30',
        colorLink: '#F94C30',
        colorLinkActive: '#d4301e',
        colorLinkHover: '#ff7559',
      },
      components: {
        Slider: {
          colorPrimaryBorder: '#f94c30',
          colorPrimaryBorderHover: '#e86b52',
        },
        Menu: {
          colorItemTextHoverHorizontal: '#1677ff',
          controlItemBgActive: '#2b1513',
          colorPrimaryBorder: '#59251c',
          colorErrorBg: '#2c1618',
        },
      },
    }"
  >
    <!-- 独立页面布局 -->
    <div v-if="isStandalonePage" class="standalone-container">
      <router-view></router-view>
    </div>

    <!-- 主应用布局 -->
    <div v-else class="container">
      <!-- 顶部区域 -->
      <div class="app-header">
        <div class="logo">
          <img src="./assets/images/logo.png" style="width: 150px; height: auto; margin-right: 24px;" alt="Logo" />
          <span class="logo-text">设备商工作台</span>
        </div>
        <div class="search-bar">
          <a-input-search placeholder="搜索..." style="width: 100%" size="large" />
        </div>
        <div class="header-actions">
          
          <a-tooltip placement="bottom" title="询价器">
            <MoneyCollectOutlined style="margin-right: 36px; color: white; font-size: 24px;" />
          </a-tooltip>
          <a-tooltip placement="bottom" title="消息通知">
            <BellOutlined style="margin-right: 36px; color: white; font-size: 24px; cursor: pointer;" @click="router.push('/notification')" />
          </a-tooltip>
          <a-tooltip placement="bottom" title="AI助手">
            <RobotOutlined style="margin-right: 36px; color: white; font-size: 24px;" />
          </a-tooltip>
          <a-tooltip placement="bottom" title="品牌馆">
            <AppstoreOutlined style="margin-right: 36px; color: white; font-size: 24px;" />
          </a-tooltip>
          <a-avatar size="large" :style="{ backgroundColor: 'white', color: '#1a1a2b', marginRight: '8px' }">
            <template #icon><UserOutlined /></template>
          </a-avatar>
          <div class="user-info">
            <span class="user-name">登录人昵称</span>
            <span class="user-company">所在公司简称</span>
          </div>
        </div>
      </div>
      <a-layout>
        <!-- 主要内容区域 -->
        <a-layout class="main-layout">
          <!-- 左侧菜单 -->
          <a-layout-sider width="250" class="side-menu" collapsible v-model:collapsed="menuCollapsed" :trigger="null">
            <div class="collapse-trigger">
              <a-button type="primary" size="small" @click="menuCollapsed = !menuCollapsed">
                <menu-unfold-outlined v-if="menuCollapsed" />
                <menu-fold-outlined v-else />
              </a-button>
            </div>
            <a-menu mode="inline" :default-selected-keys="['home']" :default-open-keys="[]" style="height: 100%" @click="handleMenuClick" :inline-collapsed="menuCollapsed">
              <template v-for="menu in menus" :key="menu.key">
                <!-- 没有子菜单的项目 -->
                <a-menu-item v-if="!menu.children" :key="menu.key" :data-path="menu.path">
                  <component :is="menu.icon" />
                  <span>{{ menu.title }}</span>
                </a-menu-item>

                <!-- 有子菜单的项目 -->
                <a-sub-menu v-else :key="`sub-${menu.key}`" :data-path="menu.path">
                  <template #title>
                    <component :is="menu.icon" />
                    <span>{{ menu.title }}</span>
                  </template>
                  <a-menu-item v-for="child in menu.children" :key="child.key" :data-path="child.path">
                    {{ child.title }}
                  </a-menu-item>
                </a-sub-menu>
              </template>
            </a-menu>
          </a-layout-sider>

          <!-- 右侧内容区 -->
          <a-layout-content class="content-wrapper">
            <!-- 页面标题和面包屑区域 -->
            <div class="breadcrumb-wrapper">
              <a-breadcrumb id="breadcrumb-nav">
                <a-breadcrumb-item>
                  <router-link to="/">
                    <home-outlined />
                    <span>首页</span>
                  </router-link>
                </a-breadcrumb-item>
                <a-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="index">
                  {{ item }}
                </a-breadcrumb-item>
              </a-breadcrumb>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
              <div id="page-content"></div>
              <router-view></router-view>
            </div>
          </a-layout-content>
        </a-layout>
      </a-layout>
    </div>
  </a-config-provider>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import 'ant-design-vue/dist/reset.css';
import { useRouter, useRoute } from 'vue-router';

import { HomeOutlined, ProjectOutlined, UnorderedListOutlined, HighlightOutlined, ShopOutlined, ShoppingCartOutlined, LineChartOutlined, SettingOutlined, MenuUnfoldOutlined, MenuFoldOutlined, UserOutlined, RobotOutlined, MoneyCollectOutlined,BellOutlined, AppstoreOutlined, BankOutlined } from '@ant-design/icons-vue';

// 获取路由实例
const router = useRouter();
const route = useRoute();

// 判断是否为独立页面
const isStandalonePage = computed(() => {
  return route.meta?.standalone === true;
});

// 面包屑导航数据
const breadcrumbs = ref([]);

// 菜单数据
const menus = [
  {
    key: 'home',
    title: '首页',
    icon: HomeOutlined,
    path: '首页',
  },
  {
    key: 'project',
    title: '项目管理',
    icon: ProjectOutlined,
    path: '项目管理',
    children: [
      { key: 'project-list', title: '项目列表', path: '/project/index' },
      { key: 'project-template', title: '项目模板', path: '/project/template' },
    ],
  },
  {
    key: 'bom',
    title: 'BOM中心',
    icon: UnorderedListOutlined,
    path: 'BOM中心',
    children: [
      { key: 'bom-list', title: 'BOM列表', path: '/bom/index' },
      { key: 'bom-category', title: 'BOM分类', path: 'BOM中心,BOM分类' },
    ],
  },
  {
    key: 'drawing',
    title: '图纸协同',
    icon: HighlightOutlined,
    path: '图纸协同',
    children: [
      { key: 'drawing-upload', title: '图纸上传', path: '图纸协同,图纸上传' },
      { key: 'version-management', title: '版本管理', path: '图纸协同,版本管理' },
      { key: 'online-annotation', title: '在线批注', path: '图纸协同,在线批注' },
    ],
  },
  {
    key: 'purchase',
    title: '采购协同',
    icon: ShopOutlined,
    path: '采购协同',
    children: [
      { key: 'rfq-management', title: '询价管理', path: '/purchase/rfq' },
      { key: 'po-apply', title: '采购申请', path: '/purchase/po/apply' },
      { key: 'po-management', title: '采购管理', path: '/purchase/po' },
      { key: 'dn-management', title: '收货管理', path: '/purchase/dn' },
      { key: 'reconciliation-management', title: '对账管理', path: '/purchase/inv' },
      { key: 'payment-management', title: '付款管理', path: '/purchase/pay' },
      { key: 'refund-management', title: '退货管理', path: '/purchase/refund' },
      { key: 'exception-handling', title: '异常处理', path: '/purchase/exception' },
    ],
  },
  {
    key: 'supplier',
    title: '供应商管理',
    icon: ShopOutlined,
    path: '供应商管理',
    children: [
      { key: 'supplier-list', title: '供应商数据库', path: '/supplier/index' },
      { key: 'private-supplier', title: '私有供应商', path: '/supplier/private' },
    ],
  },
  {
    key: 'enterprise',
    title: '企业管理',
    icon: BankOutlined,
    path: '企业管理',
    children: [
      { key: 'trade-info', title: '交易信息', path: '/enterprise/trade' },
    ],
  },
  {
    key: 'order',
    title: '订单管理',
    icon: ShoppingCartOutlined,
    path: '订单管理',
    children: [
      { key: 'order-generation', title: '订单生成', path: '订单管理,订单生成' },
      { key: 'order-tracking', title: '订单跟踪', path: '订单管理,订单跟踪' },
      { key: 'exception-handling', title: '异常处理', path: '订单管理,异常处理' },
    ],
  },
  {
    key: 'analysis',
    title: '数据分析',
    icon: LineChartOutlined,
    path: '数据分析',
    children: [
      { key: 'cost-analysis', title: '成本分析', path: '数据分析,成本分析' },
      { key: 'supplier-evaluation', title: '供应商评估', path: '数据分析,供应商评估' },
      { key: 'project-report', title: '项目报告', path: '数据分析,项目报告' },
    ],
  },
  {
    key: 'settings',
    title: '系统设置',
    icon: SettingOutlined,
    path: '系统设置',
    children: [
      { key: 'permission-management', title: '权限管理', path: '系统设置,权限管理' },
      { key: 'template-config', title: '模板配置', path: '系统设置,模板配置' },
      { key: 'data-migration', title: '数据迁移中心', path: '系统设置,数据迁移中心' },
    ],
  },
];

// 获取当前页面标题
const getCurrentPageTitle = () => {
  // 根据当前路由路径查找对应的菜单项
  for (const menu of menus) {
    if (menu.children) {
      for (const child of menu.children) {
        if (child.path === route.path) {
          return child.title;
        }
      }
    } else if (menu.path === route.path) {
      return menu.title;
    }
  }

  // 如果没有找到匹配的菜单项，返回默认标题
  return '首页';
};

// 处理菜单点击事件
const handleMenuClick = (e) => {
  console.log(e);
  const menuItem = e.item;
  const path = menuItem['data-path'];
  router.push(path);
};

// 菜单折叠状态
const menuCollapsed = ref(false);
</script>

<style>
/* 独立页面容器样式 */
.standalone-container {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: auto;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

.app-header {
  display: flex;
  flex: 0 0 64px;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background-color: #1a1a2b;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 64px; /* Reduced height */
  border-bottom: 1px solid #f0f0f0;
}

.logo {
  display: flex;
  align-items: flex-end;
}

.logo img {
  display: block; /* Removes extra space below image */
  margin-right: 8px; /* Add some space between logo and text */
}

.logo-text {
  font-size: 18px; /* Adjust size as needed */
  color: #bbb;
  font-weight: bold;
}

.search-bar {
  flex-grow: 1;
  margin: 0 24px;
  max-width: 500px; /* Optional: constrain search bar width */
}

.header-actions {
  display: flex;
  align-items: center;
  color: white;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1.2;
}

.user-name {
  font-weight: 500;
  font-size: 14px;
}

.user-company {
  font-size: 12px;
  color: #ccc; /* Lighter color for company name */
}

.header {
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100px !important;
}

.main-layout {
  padding: 0;
  width: 100%;
  display: flex;
  flex: 1;
  background-color: #f5f7fa;
}

.side-menu {
  background-color: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  z-index: 1;
  position: relative;
}

.collapse-trigger {
  position: absolute;
  right: -18px;
  top: 16px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s;
}

.side-menu:hover .collapse-trigger {
  opacity: 1;
}

.collapse-trigger .ant-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.content-wrapper {
  padding: 24px;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  margin-bottom: 8px;
}

.page-title h1 {
  font-size: 24px;
  font-weight: 500;
  color: #262626;
  margin: 0;
}

.breadcrumb-wrapper {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 16px;
}

.content-area {
  background-color: #fff;
  padding: 24px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  flex: 1;
}

.container > a-layout {
  width: 100% !important;
  margin: 0;
}
</style>
