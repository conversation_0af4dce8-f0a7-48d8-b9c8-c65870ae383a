<template>
  <div class="dashboard-container">


    <!-- 待处理事项卡片 -->
    <div class="metrics-section">
      <h3 class="section-title">
        <span class="title-icon">⚠️</span>
        待处理事项
      </h3>
      <div class="metrics-grid">
        <div class="metric-card urgent-card">
          <div class="card-icon">📋</div>
          <div class="card-content">
            <div class="card-title">待采纳询价单</div>
            <div class="card-value">{{ pendingStats.quotations }}</div>
            <div class="card-trend">
              <span class="trend-up">↗</span> 较昨日 +2
            </div>
          </div>
        </div>
        
        <div class="metric-card urgent-card">
          <div class="card-icon">📝</div>
          <div class="card-content">
            <div class="card-title">待确认订单</div>
            <div class="card-value">{{ pendingStats.orders }}</div>
            <div class="card-trend">
              <span class="trend-up">↗</span> 较昨日 +5
            </div>
          </div>
        </div>
        
        <div class="metric-card urgent-card">
          <div class="card-icon">📦</div>
          <div class="card-content">
            <div class="card-title">待签收送货单</div>
            <div class="card-value">{{ pendingStats.deliveries }}</div>
            <div class="card-trend">
              <span class="trend-down">↘</span> 较昨日 -1
            </div>
          </div>
        </div>
        
        <div class="metric-card urgent-card">
          <div class="card-icon">💳</div>
          <div class="card-content">
            <div class="card-title">待完成付款</div>
            <div class="card-value">{{ pendingStats.payments }}</div>
            <div class="card-trend">
              <span class="trend-neutral">→</span> 与昨日持平
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 本月进展卡片 -->
    <div class="metrics-section">
      <h3 class="section-title">
        <span class="title-icon">📊</span>
        本月进展
      </h3>
      <div class="metrics-grid">
        <div class="metric-card progress-card">
          <div class="card-icon">🔍</div>
          <div class="card-content">
            <div class="card-title">询价次数</div>
            <div class="card-value">{{ monthlyStats.quotations }}</div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 75%"></div>
            </div>
            <div class="progress-text">75% 完成目标</div>
          </div>
        </div>
        
        <div class="metric-card progress-card">
          <div class="card-icon">🛒</div>
          <div class="card-content">
            <div class="card-title">下单次数</div>
            <div class="card-value">{{ monthlyStats.orders }}</div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 63%"></div>
            </div>
            <div class="progress-text">63% 完成目标</div>
          </div>
        </div>
        
        <div class="metric-card highlight-card">
          <div class="card-icon">💰</div>
          <div class="card-content">
            <div class="card-title">采购金额</div>
            <div class="card-value highlight-value">¥{{ formatAmount(monthlyStats.amount) }}</div>
            <div class="card-trend">
              <span class="trend-up">↗</span> 较上月 +15.6%
            </div>
          </div>
        </div>
        
        <div class="metric-card progress-card">
          <div class="card-icon">🚚</div>
          <div class="card-content">
            <div class="card-title">送货单数</div>
            <div class="card-value">{{ monthlyStats.deliveries }}</div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 84%"></div>
            </div>
            <div class="progress-text">84% 完成目标</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据可视化图表区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <!-- 采购趋势图 -->
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">
              <span class="chart-icon">📈</span>
              <h3>采购趋势分析</h3>
            </div>
            <a-select v-model:value="trendPeriod" class="period-selector" @change="updateTrendChart">
              <a-select-option value="6months">近6个月</a-select-option>
              <a-select-option value="12months">近12个月</a-select-option>
              <a-select-option value="year">本年度</a-select-option>
            </a-select>
          </div>
          <div class="chart-container">
            <v-chart :option="trendChartOption" class="chart" />
          </div>
        </div>

        <!-- 订单状态分布图 -->
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">
              <span class="chart-icon">📋</span>
              <h3>订单状态分布</h3>
            </div>
          </div>
          <div class="chart-container">
            <v-chart :option="orderDistributionOption" class="chart" />
          </div>
        </div>
      </div>

      <div class="chart-row">
        <!-- 物料状态分布图 -->
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">
              <span class="chart-icon">📦</span>
              <h3>物料状态分布</h3>
            </div>
          </div>
          <div class="chart-container">
            <v-chart :option="materialDistributionOption" class="chart" />
          </div>
        </div>

        <!-- 快捷操作区域 -->
        <div class="quick-actions-card">
          <div class="section-header">
            <h3 class="section-title">
              <span class="title-icon">⚡</span>
              快捷操作
            </h3>
            <a-button type="link" size="small" @click="configureActions">
              <a-icon type="setting" /> 配置
            </a-button>
          </div>
          <div class="action-grid">
            <div class="action-item primary-action" @click="navigateTo('/bom/index')">
              <div class="action-icon">📤</div>
              <div class="action-title">上传BOM询价</div>
              <div class="action-desc">批量上传BOM文件</div>
            </div>
            <div class="action-item" @click="navigateTo('/purchase/rfq')">
              <div class="action-icon">🔍</div>
              <div class="action-title">询价管理</div>
              <div class="action-desc">管理询价单状态</div>
            </div>
            <div class="action-item" @click="navigateTo('/purchase/po')">
              <div class="action-icon">🛒</div>
              <div class="action-title">采购管理</div>
              <div class="action-desc">订单跟踪处理</div>
            </div>
            <div class="action-item" @click="navigateTo('/purchase/dn')">
              <div class="action-icon">📦</div>
              <div class="action-title">收货管理</div>
              <div class="action-desc">送货单签收</div>
            </div>
            <div class="action-item" @click="navigateTo('/purchase/inv')">
              <div class="action-icon">📊</div>
              <div class="action-title">对账管理</div>
              <div class="action-desc">发票对账确认</div>
            </div>
            <div class="action-item" @click="navigateTo('/purchase/pay')">
              <div class="action-icon">💳</div>
              <div class="action-title">付款结算</div>
              <div class="action-desc">付款单处理</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部区域：活动动态和公告 -->
    <div class="bottom-section">
      <div class="activity-section">
        <div class="section-header">
          <h3 class="section-title">
            <span class="title-icon">🔔</span>
            近期活动动态
          </h3>
          <a-button type="link" size="small">查看全部</a-button>
        </div>
        <div class="activity-timeline">
          <div v-for="activity in recentActivities" :key="activity.id" class="activity-item" :class="activity.type">
            <div class="activity-dot"></div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
            <div class="activity-action" v-if="activity.action">
              <a-button size="small" type="link" @click="handleActivityAction(activity)">
                {{ activity.action }}
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <div class="announcement-section">
        <div class="section-header">
          <h3 class="section-title">
            <span class="title-icon">📢</span>
            系统公告
          </h3>
          <a-button type="link" size="small">更多公告</a-button>
        </div>
        <div class="announcement-list">
          <div v-for="announcement in announcements" :key="announcement.id" class="announcement-item">
            <div class="announcement-header">
              <span class="announcement-title">{{ announcement.title }}</span>
              <span class="announcement-date">{{ announcement.date }}</span>
            </div>
            <div class="announcement-content">{{ announcement.content }}</div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const pendingStats = reactive({
  quotations: 8,
  orders: 12,
  deliveries: 5,
  payments: 3
})

const monthlyStats = reactive({
  quotations: 45,
  orders: 38,
  amount: 2856000,
  deliveries: 42
})

const trendPeriod = ref('6months')

// 近期活动数据
const recentActivities = ref([
  {
    id: 1,
    type: 'urgent',
    title: '询价单 #RFQ-2024-001 即将截止',
    time: '2小时后截止',
    action: '立即处理'
  },
  {
    id: 2,
    type: 'warning',
    title: '送货单 #DN-2024-156 即将自动收货',
    time: '明天自动确认',
    action: '查看详情'
  },
  {
    id: 3,
    type: 'info',
    title: '对账单 #INV-2024-089 待确认',
    time: '3天后自动确认',
    action: '立即确认'
  },
  {
    id: 4,
    type: 'urgent',
    title: '付款单 #PAY-2024-045 即将逾期',
    time: '5天后逾期',
    action: '立即付款'
  }
])

// 系统公告数据
const announcements = ref([
  {
    id: 1,
    title: '系统维护通知',
    date: '2024-01-15',
    content: '系统将于本周六凌晨2:00-4:00进行例行维护，期间可能影响部分功能使用。'
  },
  {
    id: 2,
    title: '新功能上线',
    date: '2024-01-12',
    content: '批量询价功能已上线，支持一次性上传多个BOM文件进行询价。'
  },
  {
    id: 3,
    title: '春节放假通知',
    date: '2024-01-10',
    content: '春节期间（2月8日-2月18日）客服响应时间可能延长，请提前安排相关事务。'
  }
])

// 图表配置
const trendChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'cross' },
    backgroundColor: 'rgba(26, 26, 43, 0.9)',
    borderColor: '#f94c30',
    textStyle: { color: '#fff' }
  },
  legend: {
    data: ['询价次数', '采购金额(万元)'],
    top: 10,
    textStyle: { color: '#666' }
  },
  grid: {
    left: '3%', right: '4%', bottom: '3%', containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['8月', '9月', '10月', '11月', '12月', '1月'],
    axisLine: { lineStyle: { color: '#e0e0e0' } }
  },
  yAxis: [
    {
      type: 'value', name: '询价次数', position: 'left',
      axisLine: { lineStyle: { color: '#e0e0e0' } }
    },
    {
      type: 'value', name: '采购金额(万元)', position: 'right',
      axisLine: { lineStyle: { color: '#e0e0e0' } }
    }
  ],
  series: [
    {
      name: '询价次数', type: 'line', data: [35, 42, 38, 45, 52, 45],
      smooth: true, itemStyle: { color: '#f94c30' },
      areaStyle: {
        color: {
          type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(249, 76, 48, 0.3)' },
            { offset: 1, color: 'rgba(249, 76, 48, 0.05)' }
          ]
        }
      }
    },
    {
      name: '采购金额(万元)', type: 'line', yAxisIndex: 1,
      data: [180, 220, 195, 285, 320, 285], smooth: true,
      itemStyle: { color: '#ffca46' },
      areaStyle: {
        color: {
          type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(255, 202, 70, 0.3)' },
            { offset: 1, color: 'rgba(255, 202, 70, 0.05)' }
          ]
        }
      }
    }
  ]
}))

const orderDistributionOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)',
    backgroundColor: 'rgba(26, 26, 43, 0.9)',
    borderColor: '#f94c30',
    textStyle: { color: '#fff' }
  },
  legend: {
    orient: 'vertical', left: 'left', top: 'middle',
    textStyle: { color: '#666' }
  },
  series: [{
    name: '订单状态', type: 'pie',
    radius: ['40%', '70%'], center: ['65%', '50%'],
    data: [
      { value: 15, name: '待确认' },
      { value: 25, name: '确认中' },
      { value: 35, name: '执行中' },
      { value: 20, name: '已完成' },
      { value: 5, name: '已取消' }
    ],
    itemStyle: {
      borderRadius: 5, borderColor: '#fff', borderWidth: 2
    },
    color: ['#f94c30', '#ffca46', '#1a1a2b', '#52c41a', '#ff7875']
  }]
}))

const materialDistributionOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)',
    backgroundColor: 'rgba(26, 26, 43, 0.9)',
    borderColor: '#f94c30',
    textStyle: { color: '#fff' }
  },
  legend: {
    orient: 'vertical', left: 'left', top: 'middle',
    textStyle: { color: '#666' }
  },
  series: [{
    name: '物料状态', type: 'pie',
    radius: ['40%', '70%'], center: ['65%', '50%'],
    data: [
      { value: 12, name: '备货中' },
      { value: 18, name: '部分发货' },
      { value: 22, name: '全部发货' },
      { value: 15, name: '部分收货' },
      { value: 8, name: '已收货' },
      { value: 3, name: '退货中' },
      { value: 2, name: '已取消' }
    ],
    itemStyle: {
      borderRadius: 5, borderColor: '#fff', borderWidth: 2
    },
    color: ['#f94c30', '#ffca46', '#1a1a2b', '#2a2a35', '#52c41a', '#ff7875', '#ffc069']
  }]
}))



// 方法
const formatAmount = (amount) => {
  return (amount / 10000).toFixed(1) + '万'
}

const navigateTo = (path) => {
  router.push(path)
}

const updateTrendChart = () => {
  console.log('更新趋势图:', trendPeriod.value)
}

const handleActivityAction = (activity) => {
  console.log('处理活动:', activity)
}

const configureActions = () => {
  console.log('配置快捷操作')
}

onMounted(() => {
  console.log('Dashboard mounted')
})
</script>

<style scoped>
.dashboard-container {
  padding: 24px;
  min-height: 100vh;
  position: relative;
}

.dashboard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(249, 76, 48, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 202, 70, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(26, 26, 43, 0.03) 0%, transparent 50%);
  pointer-events: none;
}



/* 指标区域 */
.metrics-section {
  margin-bottom: 32px;
}

.metrics-section:first-child {
  margin-top: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a2b;
  margin-bottom: 16px;
}

.title-icon {
  font-size: 20px;
}

/* 指标卡片网格 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.metric-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 16px;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #f94c30, #ffca46);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.metric-card:hover::before {
  transform: scaleX(1);
}

.metric-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.urgent-card {
  background: linear-gradient(135deg, rgba(249, 76, 48, 0.05), rgba(255, 255, 255, 0.95));
}

.progress-card {
  background: linear-gradient(135deg, rgba(26, 26, 43, 0.05), rgba(255, 255, 255, 0.95));
}

.highlight-card {
  background: linear-gradient(135deg, rgba(255, 202, 70, 0.1), rgba(255, 255, 255, 0.95));
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  background: linear-gradient(135deg, #f94c30, #ff7875);
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin-bottom: 8px;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a2b;
  margin-bottom: 8px;
  line-height: 1;
}

.highlight-value {
  background: linear-gradient(45deg, #f94c30, #ffca46);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.trend-up {
  color: #52c41a;
  font-weight: bold;
}

.trend-down {
  color: #f94c30;
  font-weight: bold;
}

.trend-neutral {
  color: #999;
  font-weight: bold;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #f94c30, #ffca46);
  border-radius: 3px;
  transition: width 0.6s ease;
}

.progress-text {
  font-size: 12px;
  color: #666;
}

/* 图表区域样式 */
.charts-section {
  margin-bottom: 32px;
}

.chart-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.chart-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.chart-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f0f0;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-icon {
  font-size: 20px;
}

.chart-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a2b;
}

.period-selector {
  width: 140px;
}

.chart-container {
  height: 350px;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 底部区域样式 */
.bottom-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.activity-section,
.announcement-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f0f0;
}

.section-header .section-title {
  margin-bottom: 0;
}

/* 活动时间轴样式 */
.activity-timeline {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 16px;
  flex-shrink: 0;
}

.activity-item.urgent .activity-dot {
  background: #f94c30;
  box-shadow: 0 0 0 4px rgba(249, 76, 48, 0.2);
}

.activity-item.warning .activity-dot {
  background: #ffca46;
  box-shadow: 0 0 0 4px rgba(255, 202, 70, 0.2);
}

.activity-item.info .activity-dot {
  background: #52c41a;
  box-shadow: 0 0 0 4px rgba(82, 196, 26, 0.2);
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #1a1a2b;
  margin-bottom: 4px;
  font-weight: 500;
}

.activity-time {
  font-size: 12px;
  color: #999;
}

.activity-action {
  margin-left: 16px;
}

/* 公告列表样式 */
.announcement-list {
  max-height: 400px;
  overflow-y: auto;
}

.announcement-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.announcement-item:last-child {
  border-bottom: none;
}

.announcement-item:hover {
  background: rgba(249, 76, 48, 0.05);
  border-radius: 8px;
  padding: 16px 12px;
  margin: 0 -12px;
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.announcement-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a2b;
}

.announcement-date {
  font-size: 12px;
  color: #999;
}

.announcement-content {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

/* 快捷操作区域 */
.quick-actions-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.quick-actions-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.quick-actions-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #f94c30, #ffca46, #52c41a);
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-top: 20px;
  height: 280px;
  overflow-y: auto;
}

.action-item {
  padding: 16px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: hidden;
  height: fit-content;
}

.action-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.action-item:hover::before {
  left: 100%;
}

.action-item:hover {
  transform: translateY(-4px);
  border-color: #f94c30;
  box-shadow: 0 8px 24px rgba(249, 76, 48, 0.2);
}

.primary-action {
  background: linear-gradient(135deg, #f94c30, #ff7875);
  color: white;
}

.primary-action:hover {
  background: linear-gradient(135deg, #e63e26, #f94c30);
  border-color: #e63e26;
}

.action-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.action-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.action-desc {
  font-size: 11px;
  opacity: 0.8;
}

.primary-action .action-title,
.primary-action .action-desc {
  color: white;
}



/* 响应式设计 */
@media (max-width: 1400px) {
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 1200px) {
  .chart-row {
    grid-template-columns: 1fr;
  }
  
  .bottom-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .action-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-row {
    grid-template-columns: 1fr;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #f94c30, #ffca46);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #e63e26, #f94c30);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.metric-card {
  animation: fadeInUp 0.6s ease forwards;
}

.metric-card:nth-child(1) { animation-delay: 0.1s; }
.metric-card:nth-child(2) { animation-delay: 0.2s; }
.metric-card:nth-child(3) { animation-delay: 0.3s; }
.metric-card:nth-child(4) { animation-delay: 0.4s; }
.metric-card:nth-child(5) { animation-delay: 0.5s; }
.metric-card:nth-child(6) { animation-delay: 0.6s; }
.metric-card:nth-child(7) { animation-delay: 0.7s; }
.metric-card:nth-child(8) { animation-delay: 0.8s; }
.metric-card:nth-child(9) { animation-delay: 0.9s; }
.metric-card:nth-child(10) { animation-delay: 1.0s; }
.metric-card:nth-child(11) { animation-delay: 1.1s; }
.metric-card:nth-child(12) { animation-delay: 1.2s; }
</style>