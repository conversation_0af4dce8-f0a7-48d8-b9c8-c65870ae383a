<template>
  <div class="trade-info-container">
    <div class="wrapper">
      <!-- 付款方式卡片 -->
      <a-card title="付款方式" class="info-card" :bordered="false">
        <a-row :gutter="24">
          <!-- <a-col :span="12">
            <div class="info-item">
              <span class="label">支持的付款方式：</span>
              <a-tag v-for="method in paymentMethods" :key="method.type" :color="method.enabled ? 'green' : 'default'" class="payment-tag">
                {{ method.name }}
              </a-tag>
            </div>
          </a-col> -->
          <a-col :span="12">
            <div class="info-item">
              <span class="label">付款方式：</span>
              <span class="value">{{ defaultPaymentMethod }}</span>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 付款条件卡片 -->
      <a-card title="付款条件" class="info-card" :bordered="false">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="info-item">
              <span class="label">付款条件：</span>
              <span class="value">{{ paymentTerms }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">账期天数：</span>
              <span class="value">{{ creditDays }}天</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">账期日：</span>
              <span class="value">每月{{ paymentDate }}号</span>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 账期额度卡片 -->
      <a-card title="账期额度管理" class="info-card" :bordered="false">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-statistic title="总账期额度" :value="totalCreditLimit" suffix="元" :value-style="{ color: '#1890ff' }" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="已使用额度" :value="usedCreditLimit" suffix="元" :value-style="{ color: '#f5222d' }" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="剩余额度" :value="remainingCreditLimit" suffix="元" :value-style="{ color: '#52c41a' }" />
          </a-col>
        </a-row>

        <!-- 额度使用进度条 -->
        <div class="credit-progress">
          <span class="progress-label">额度使用情况：</span>
          <a-progress :percent="creditUsagePercent" :stroke-color="creditUsagePercent > 80 ? '#f5222d' : '#1890ff'" :show-info="true" />
        </div>
      </a-card>

      <!-- 交易统计卡片 -->
      <!-- <a-card title="交易统计" class="info-card" :bordered="false">
        <a-row :gutter="24">
          <a-col :span="6">
            <a-statistic title="本月交易笔数" :value="monthlyTransactions" suffix="笔" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="本月交易金额" :value="monthlyAmount" suffix="元" :precision="2" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="待付款金额" :value="pendingPayment" suffix="元" :precision="2" :value-style="{ color: '#fa8c16' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="逾期金额" :value="overdueAmount" suffix="元" :precision="2" :value-style="{ color: '#f5222d' }" />
          </a-col>
        </a-row>
      </a-card> -->

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <!-- <a-button type="primary" size="large" @click="editTradeInfo"> 申请提额 </a-button> -->
        <!-- <a-button size="large" @click="viewHistory" style="margin-left: 12px"> 查看交易历史 </a-button> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// 付款方式数据
const paymentMethods = ref([
  { type: 'cash', name: '现金/电汇', enabled: true },
  // { type: 'transfer', name: '电汇', enabled: true },
  { type: 'acceptance', name: '银行承兑', enabled: false },
  // { type: 'check', name: '支票', enabled: false },
]);

// 企业交易信息
const defaultPaymentMethod = ref('现金/电汇');
const paymentTerms = ref('账期结算');
const creditDays = ref(30);
const paymentDate = ref(15);

// 账期额度信息
const totalCreditLimit = ref(5000000);
const usedCreditLimit = ref(3200000);
const remainingCreditLimit = computed(() => totalCreditLimit.value - usedCreditLimit.value);
const creditUsagePercent = computed(() => Math.round((usedCreditLimit.value / totalCreditLimit.value) * 100));

// 交易统计数据
const monthlyTransactions = ref(156);
const monthlyAmount = ref(2800000);
const pendingPayment = ref(850000);
const overdueAmount = ref(120000);

// 操作方法
const editTradeInfo = () => {
  console.log('编辑交易信息');
  // 这里可以添加编辑交易信息的逻辑
};

const viewHistory = () => {
  console.log('查看交易历史');
  // 这里可以添加查看交易历史的逻辑
};
</script>

<style scoped>
.trade-info-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.page-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.info-card :deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.info-card :deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.info-item {
  margin-bottom: 16px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  display: inline-block;
  color: #8c8c8c;
  font-size: 14px;
  margin-right: 8px;
  min-width: 100px;
}

.value {
  color: #262626;
  font-size: 14px;
  font-weight: 500;
}

.payment-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.credit-progress {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.progress-label {
  display: inline-block;
  margin-bottom: 8px;
  color: #8c8c8c;
  font-size: 14px;
}

.action-buttons {
  text-align: center;
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-item {
    margin-bottom: 12px;
  }

  .label {
    display: block;
    margin-bottom: 4px;
    min-width: auto;
  }

  .action-buttons {
    text-align: left;
  }

  .action-buttons .ant-btn {
    display: block;
    width: 100%;
    margin: 0 0 12px 0 !important;
  }
}
</style>
