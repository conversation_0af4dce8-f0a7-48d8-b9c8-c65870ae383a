<template>
  <div class="quote-success-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="logo-section">
          <img src="../../assets/images/logo.png" alt="研选工场" class="logo-image">
        </div>
        <div class="header-info">
          <span class="page-title">报价提交成功</span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="success-container">
        <a-result
          status="success"
          title="报价提交成功！"
          sub-title="您的报价已成功提交给采购方，我们将及时跟进报价进展并通知您。"
        >
          <template #extra>
            <a-space direction="vertical" size="large" style="width: 100%;">
              <!-- 提交信息概览 -->
              <a-card title="提交信息概览" class="summary-card">
                <a-row :gutter="24">
                  <a-col :span="8">
                    <a-statistic 
                      title="询价单号" 
                      :value="submitInfo.rfqNo"
                    />
                  </a-col>
                  <a-col :span="8">
                    <a-statistic 
                      title="报价物料数" 
                      :value="submitInfo.quotedMaterialsCount"
                      suffix="项"
                    />
                  </a-col>
                  <a-col :span="8">
                    <a-statistic 
                      title="报价总额" 
                      :value="submitInfo.totalAmount"
                      prefix="¥"
                      :precision="2"
                    />
                  </a-col>
                </a-row>
                
                <a-divider />
                
                <div class="submit-details">
                  <p><strong>提交时间：</strong>{{ formatDateTime(submitInfo.submitTime) }}</p>
                  <p><strong>联系方式：</strong>{{ submitInfo.contactInfo }}</p>
                  <p><strong>报价编号：</strong>{{ submitInfo.quoteId }}</p>
                </div>
              </a-card>

              <!-- 后续步骤 -->
              <a-card title="后续步骤" class="steps-card">
                <a-steps direction="vertical" :current="1">
                  <a-step 
                    title="报价提交成功"
                    description="您的报价已提交，采购方将收到通知"
                    status="finish"
                  />
                  <a-step 
                    title="等待采购方评审"
                    description="采购方将对您的报价进行评审，通常在1-3个工作日内完成"
                    status="process"
                  />
                  <a-step 
                    title="获得反馈结果"
                    description="我们将通过邮件或电话通知您评审结果"
                    status="wait"
                  />
                  <a-step 
                    title="签订合同"
                    description="如报价被采纳，我们将协助您与采购方签订正式合同"
                    status="wait"
                  />
                </a-steps>
              </a-card>

              <!-- 重要提醒 -->
              <a-alert
                message="重要提醒"
                description="请保持您提供的联系方式畅通，我们会在第一时间将评审结果通知您。如有任何疑问，请联系客服热线 400-888-9999。"
                type="warning"
                show-icon
                closable
              />

              <!-- 操作按钮 -->
              <div class="action-buttons">
                <a-space size="large">
                  <a-button type="primary" size="large" @click="handleJoinPlatform">
                    加入研选工场，获得更多商机
                  </a-button>
                  <a-button size="large" @click="handleViewMoreRfq">
                    查看更多询价单
                  </a-button>
                  <a-button size="large" @click="handleBackToRfq">
                    返回询价单详情
                  </a-button>
                </a-space>
              </div>
            </a-space>
          </template>
        </a-result>
      </div>

      <!-- 研选工场平台介绍 -->
      <div class="platform-introduction">
        <a-card>
          <div class="intro-header">
            <h2>为什么选择研选工场？</h2>
            <p>专业的自动化装备制造行业AI供应链服务平台</p>
          </div>
          
          <a-row :gutter="32">
            <a-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <a-icon type="robot" style="font-size: 32px; color: #1890ff;" />
                </div>
                <h4>AI智能匹配</h4>
                <p>基于AI技术精准匹配供需双方，提高成交率</p>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <a-icon type="dollar" style="font-size: 32px; color: #52c41a;" />
                </div>
                <h4>降本增效</h4>
                <p>平均帮助企业降低5%采购成本，提升70%响应速度</p>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <a-icon type="safety" style="font-size: 32px; color: #fa8c16;" />
                </div>
                <h4>全程保障</h4>
                <p>专业团队全程服务，交易安全有保障</p>
              </div>
            </a-col>
          </a-row>
          
          <div class="cta-section">
            <h3>立即注册成为研选工场供应商</h3>
            <p>获得更多优质询价单，拓展业务机会</p>
            <a-button type="primary" size="large" @click="handleRegisterSupplier">
              立即注册
            </a-button>
          </div>
        </a-card>
      </div>
    </div>

    <!-- 页脚 -->
    <div class="page-footer">
      <div class="footer-content">
        <p>&copy; 2025 研选工场（苏州）网络有限公司 | 苏ICP备2024149956号</p>
        <p>专业的工业品采购平台 · 智能化供应链解决方案</p>
        <div class="contact-info">
          <span>客服热线：400-888-9999</span>
          <span>邮箱：<EMAIL></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

const route = useRoute();
const router = useRouter();

// 提交信息
const submitInfo = ref({
  rfqNo: 'RFQ-2023-0001',
  quotedMaterialsCount: 4,
  totalAmount: 12560.00,
  submitTime: new Date().toISOString(),
  contactInfo: '138****8888',
  quoteId: 'QT-' + Date.now()
});

// 工具方法
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  return date.toLocaleString('zh-CN');
};

// 操作方法
const handleJoinPlatform = () => {
  // 跳转到供应商注册页面
  message.info('跳转到供应商注册页面');
  window.open('https://www.yanxuan.cloud/supplier/register', '_blank');
};

const handleViewMoreRfq = () => {
  // 跳转到公开询价单列表
  message.info('跳转到公开询价单列表');
  window.open('https://www.yanxuan.cloud/rfq/public', '_blank');
};

const handleBackToRfq = () => {
  // 返回原询价单详情页面
  router.back();
};

const handleRegisterSupplier = () => {
  // 跳转到供应商注册页面
  window.open('https://www.yanxuan.cloud/supplier/register', '_blank');
};

// 生命周期
onMounted(() => {
  // 可以从路由参数或本地存储中获取提交信息
  // 这里使用模拟数据
});
</script>

<style lang="less" scoped>
.quote-success-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.page-header {
  background-color: #2a2a35;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 24px;

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;

    .logo-section {
      display: flex;
      align-items: center;
      
      .logo-image {
        width: 150px;
      }
    }

    .header-info {
      .page-title {
        color: #fff;
        font-size: 18px;
        font-weight: 500;
      }
    }
  }
}

.main-content {
  flex: 1;
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 40px 24px;
}

.success-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 40px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .summary-card {
    margin-bottom: 24px;

    .submit-details {
      p {
        margin: 8px 0;
        color: #595959;
        
        strong {
          color: #262626;
        }
      }
    }
  }

  .steps-card {
    margin-bottom: 24px;
  }

  .action-buttons {
    text-align: center;
    margin-top: 32px;
  }
}

.platform-introduction {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .intro-header {
    text-align: center;
    margin-bottom: 40px;

    h2 {
      color: #262626;
      font-size: 24px;
      margin-bottom: 8px;
    }

    p {
      color: #8c8c8c;
      font-size: 16px;
    }
  }

  .feature-item {
    text-align: center;
    padding: 24px 16px;

    .feature-icon {
      margin-bottom: 16px;
    }

    h4 {
      color: #262626;
      font-size: 18px;
      margin-bottom: 8px;
    }

    p {
      color: #8c8c8c;
      line-height: 1.6;
    }
  }

  .cta-section {
    text-align: center;
    padding: 40px 0 20px;
    border-top: 1px solid #f0f0f0;
    margin-top: 40px;

    h3 {
      color: #262626;
      font-size: 20px;
      margin-bottom: 8px;
    }

    p {
      color: #8c8c8c;
      margin-bottom: 24px;
    }
  }
}

.page-footer {
  background-color: #2a2a35;
  color: #8c8c8c;
  padding: 24px;
  margin-top: auto;

  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;

    p {
      margin: 4px 0;
    }

    .contact-info {
      margin-top: 16px;

      span {
        margin: 0 16px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .main-content {
    padding: 20px 16px;
  }

  .success-container {
    padding: 24px 16px;
  }

  .action-buttons {
    .ant-space {
      width: 100%;
      
      .ant-btn {
        display: block;
        margin: 8px 0;
        width: 100%;
      }
    }
  }

  .feature-item {
    margin-bottom: 32px;
  }
}

// 覆盖 Ant Design 样式
:deep(.ant-result) {
  .ant-result-title {
    color: #262626;
    font-size: 24px;
  }
  
  .ant-result-subtitle {
    color: #8c8c8c;
    font-size: 16px;
  }
}
</style> 