<template>
  <div class="private-supplier-container">
    <a-row :gutter="16">
      <a-col :span="24">
        <a-card title="私有供应商管理" class="dashboard-card">
          <div class="dashboard-header">
            <div class="progress-stats">
              <a-statistic title="已入驻供应商" :value="statistics.registered" suffix="家" class="stat-item" />
              <a-statistic title="未入驻供应商" :value="statistics.pending" suffix="家" class="stat-item" />
              <a-statistic title="待邀请供应商" :value="statistics.waitingForInvite" suffix="家" class="stat-item" />
              <a-statistic title="入驻率" :value="statistics.registerRate" suffix="%" class="stat-item" />
            </div>
            <div class="dashboard-actions">
              <a-button type="primary" @click="showAddSupplierModal" class="action-btn">
                <plus-outlined /> 添加供应商
              </a-button>
              <a-button @click="showInviteModal" class="action-btn" :disabled="!hasSuppliers">
                <upload-outlined /> 邀请供应商入驻
              </a-button>
              <a-button @click="showDataImportModal">
                <database-outlined /> 上传合作数据
              </a-button>
              <a-button @click="inviteAllPendingSuppliers" :disabled="!hasPendingSuppliers">
                <mail-outlined /> 批量催办
              </a-button>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 供应商迁移看板 -->
    <a-row :gutter="16" class="row-margin">
      <a-col :span="24">
        <a-card title="供应商迁移看板" class="migration-board">
          <a-tabs v-model:activeKey="activeTabKey">
            <a-tab-pane key="all" tab="全部供应商">
              <a-table
                :dataSource="supplierList"
                :columns="columns"
                :pagination="{ pageSize: 10 }"
                :loading="tableLoading"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'status'">
                    <a-tag :color="record.status === '已入驻' ? 'success' : record.status === '未入驻' ? 'warning' : 'default'">
                      {{ record.status }}
                    </a-tag>
                  </template>
                  <template v-if="column.dataIndex === 'dataStatus'">
                    <a-progress
                      :percent="record.dataCompletionRate"
                      size="small"
                      :status="record.dataCompletionRate === 100 ? 'success' : 'active'"
                    />
                  </template>
                  <template v-if="column.dataIndex === 'action'">
                    <div class="table-actions">
                      <a @click="inviteSupplier(record)" v-if="record.status === '待邀请'">邀请入驻</a>
                      <a-divider type="vertical" v-if="record.status === '待邀请'" />
                      <a @click="sendReminder(record)" v-if="record.status === '未入驻' && record.inviteTime">催办</a>
                      <a-divider type="vertical" v-if="record.status === '未入驻' && record.inviteTime" />
                      <a @click="viewSupplierDetails(record)">详情</a>
                      <a-divider type="vertical" />
                      <a @click="editSupplier(record)">编辑</a>
                    </div>
                  </template>
                </template>
              </a-table>
            </a-tab-pane>
            <a-tab-pane key="registered" tab="已入驻供应商">
              <!-- 已入驻供应商内容 -->
            </a-tab-pane>
            <a-tab-pane key="pending" tab="未入驻供应商">
              <!-- 未入驻供应商内容 -->
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </a-col>
    </a-row>

    <!-- 一键邀请模态框 -->
    <a-modal
      v-model:visible="inviteModalVisible"
      title="一键邀请供应商"
      @ok="handleInviteSubmit"
      :confirmLoading="inviteLoading"
      width="700px"
    >
      <a-tabs v-model:activeKey="inviteMethod">
        <a-tab-pane key="excel" tab="Excel导入">
          <div class="upload-container">
            <a-upload-dragger
              v-model:fileList="inviteFileList"
              :beforeUpload="beforeInviteUpload"
              :multiple="false"
              :showUploadList="true"
              accept=".xlsx,.xls"
            >
              <p class="ant-upload-drag-icon">
                <inbox-outlined />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">
                支持Excel格式(.xlsx, .xls)，请确保文件包含供应商名称、联系人、联系电话、电子邮箱等字段
              </p>
            </a-upload-dragger>
            <div class="template-download">
              <a @click="downloadTemplate">下载导入模板</a>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="api" tab="API对接">
          <div class="api-integration-form">
            <a-form :model="apiFormData" layout="vertical">
              <a-form-item label="系统类型" name="systemType">
                <a-select v-model:value="apiFormData.systemType" placeholder="请选择系统类型">
                  <a-select-option value="erp">ERP系统</a-select-option>
                  <a-select-option value="crm">CRM系统</a-select-option>
                  <a-select-option value="other">其他系统</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="API链接" name="apiUrl">
                <a-input v-model:value="apiFormData.apiUrl" placeholder="请输入API链接" />
              </a-form-item>
              <a-form-item label="认证类型" name="authType">
                <a-radio-group v-model:value="apiFormData.authType">
                  <a-radio value="token">Token</a-radio>
                  <a-radio value="oauth">OAuth</a-radio>
                  <a-radio value="basic">Basic Auth</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="认证凭证" name="authCredential">
                <a-input v-model:value="apiFormData.authCredential" placeholder="请输入认证凭证" />
              </a-form-item>
            </a-form>
          </div>
        </a-tab-pane>
      </a-tabs>
      <div class="notice-setting">
        <a-divider />
        <h4>通知设置</h4>
        <a-checkbox-group v-model:value="notificationMethods">
          <a-checkbox value="email">邮件通知</a-checkbox>
          <a-checkbox value="sms">短信通知</a-checkbox>
        </a-checkbox-group>
      </div>
    </a-modal>

    <!-- 数据导入模态框 -->
    <a-modal
      v-model:visible="dataImportModalVisible"
      title="上传供应商历史合作数据"
      @ok="handleDataImportSubmit"
      :confirmLoading="dataImportLoading"
      width="700px"
    >
      <a-form :model="dataImportForm" layout="vertical">
        <a-form-item label="选择供应商" name="supplierId">
          <a-select
            v-model:value="dataImportForm.supplierId"
            placeholder="请选择供应商"
            show-search
            :options="supplierOptions"
            :filter-option="filterOption"
          />
        </a-form-item>
        <a-form-item label="数据类型" name="dataType">
          <a-checkbox-group v-model:value="dataImportForm.dataTypes">
            <a-checkbox value="deliveryRecords">交货记录</a-checkbox>
            <a-checkbox value="contracts">合同数据</a-checkbox>
            <a-checkbox value="qualityRecords">质量记录</a-checkbox>
            <a-checkbox value="contactInfo">联系人信息</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="选择文件" name="fileList">
          <a-upload-dragger
            v-model:fileList="dataFileList"
            :beforeUpload="beforeDataUpload"
            :multiple="true"
            :showUploadList="true"
          >
            <p class="ant-upload-drag-icon">
              <inbox-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">
              支持Excel、CSV、PDF等格式文件，文件大小不超过20MB
            </p>
          </a-upload-dragger>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 供应商添加模态框 -->
    <a-modal
      v-model:visible="addSupplierModalVisible"
      title="添加供应商"
      @ok="handleAddSupplierSubmit"
      :confirmLoading="addSupplierLoading"
      width="700px"
    >
      <a-tabs v-model:activeKey="addSupplierMethod">
        <a-tab-pane key="manual" tab="手动添加">
          <a-form :model="supplierForm" layout="vertical">
            <a-form-item label="供应商名称" name="name" :rules="[{ required: true, message: '请输入供应商名称' }]">
              <a-input v-model:value="supplierForm.name" placeholder="请输入供应商名称" />
            </a-form-item>
            <a-form-item label="联系人" name="contact" :rules="[{ required: true, message: '请输入联系人姓名' }]">
              <a-input v-model:value="supplierForm.contact" placeholder="请输入联系人姓名" />
            </a-form-item>
            <a-form-item label="联系电话" name="phone" :rules="[{ required: true, message: '请输入联系电话' }]">
              <a-input v-model:value="supplierForm.phone" placeholder="请输入联系电话" />
            </a-form-item>
            <a-form-item label="电子邮箱" name="email" :rules="[{ required: true, message: '请输入电子邮箱' }]">
              <a-input v-model:value="supplierForm.email" placeholder="请输入电子邮箱" />
            </a-form-item>
            <a-form-item label="供应商类型" name="type">
              <a-select v-model:value="supplierForm.type" placeholder="请选择供应商类型">
                <a-select-option value="component">零部件供应商</a-select-option>
                <a-select-option value="material">原材料供应商</a-select-option>
                <a-select-option value="service">服务供应商</a-select-option>
                <a-select-option value="other">其他供应商</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="备注" name="remark">
              <a-textarea v-model:value="supplierForm.remark" placeholder="请输入备注信息" :rows="3" />
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="excel" tab="Excel导入">
          <div class="upload-container">
            <a-upload-dragger
              v-model:fileList="addSupplierFileList"
              :beforeUpload="beforeSupplierUpload"
              :multiple="false"
              :showUploadList="true"
              accept=".xlsx,.xls"
            >
              <p class="ant-upload-drag-icon">
                <inbox-outlined />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">
                支持Excel格式(.xlsx, .xls)，请确保文件包含供应商名称、联系人、联系电话、电子邮箱等字段
              </p>
            </a-upload-dragger>
            <div class="template-download">
              <a @click="downloadSupplierTemplate">下载导入模板</a>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-modal>

    <!-- 编辑供应商模态框 -->
    <a-modal
      v-model:visible="editSupplierModalVisible"
      title="编辑供应商"
      @ok="handleEditSupplierSubmit"
      :confirmLoading="editSupplierLoading"
    >
      <a-form :model="editSupplierForm" layout="vertical">
        <a-form-item label="供应商名称" name="name" :rules="[{ required: true, message: '请输入供应商名称' }]">
          <a-input v-model:value="editSupplierForm.name" placeholder="请输入供应商名称" />
        </a-form-item>
        <a-form-item label="联系人" name="contact" :rules="[{ required: true, message: '请输入联系人姓名' }]">
          <a-input v-model:value="editSupplierForm.contact" placeholder="请输入联系人姓名" />
        </a-form-item>
        <a-form-item label="联系电话" name="phone" :rules="[{ required: true, message: '请输入联系电话' }]">
          <a-input v-model:value="editSupplierForm.phone" placeholder="请输入联系电话" />
        </a-form-item>
        <a-form-item label="电子邮箱" name="email" :rules="[{ required: true, message: '请输入电子邮箱' }]">
          <a-input v-model:value="editSupplierForm.email" placeholder="请输入电子邮箱" />
        </a-form-item>
        <a-form-item label="供应商类型" name="type">
          <a-select v-model:value="editSupplierForm.type" placeholder="请选择供应商类型">
            <a-select-option value="component">零部件供应商</a-select-option>
            <a-select-option value="material">原材料供应商</a-select-option>
            <a-select-option value="service">服务供应商</a-select-option>
            <a-select-option value="other">其他供应商</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="editSupplierForm.remark" placeholder="请输入备注信息" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import {
  UploadOutlined,
  DatabaseOutlined,
  MailOutlined,
  InboxOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue';

// 表格数据
const supplierList = ref([
  {
    key: '1',
    name: '杭州机电有限公司',
    contact: '张三',
    phone: '13800138000',
    email: '<EMAIL>',
    status: '已入驻',
    inviteTime: '2023-08-15',
    registerTime: '2023-08-17',
    dataCompletionRate: 100,
    type: 'component',
  },
  {
    key: '2',
    name: '苏州精密机械制造厂',
    contact: '李四',
    phone: '13900139000',
    email: '<EMAIL>',
    status: '已入驻',
    inviteTime: '2023-08-15',
    registerTime: '2023-08-20',
    dataCompletionRate: 85,
    type: 'material',
  },
  {
    key: '3',
    name: '深圳电子科技有限公司',
    contact: '王五',
    phone: '13700137000',
    email: '<EMAIL>',
    status: '未入驻',
    inviteTime: '2023-08-18',
    registerTime: null,
    dataCompletionRate: 0,
    type: 'component',
  },
  {
    key: '4',
    name: '宁波自动化设备有限公司',
    contact: '赵六',
    phone: '13600136000',
    email: '<EMAIL>',
    status: '待邀请',
    inviteTime: null,
    registerTime: null,
    dataCompletionRate: 0,
    type: 'service',
  },
  {
    key: '5',
    name: '无锡工业零部件有限公司',
    contact: '钱七',
    phone: '13500135000',
    email: '<EMAIL>',
    status: '已入驻',
    inviteTime: '2023-08-10',
    registerTime: '2023-08-12',
    dataCompletionRate: 100,
    type: 'component',
  },
]);

// 表格列定义
const columns = [
  {
    title: '供应商名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '联系人',
    dataIndex: 'contact',
    key: 'contact',
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    key: 'phone',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
  },
  {
    title: '供应商类型',
    dataIndex: 'type',
    key: 'type',
    customRender: ({ text }) => {
      const typeMap = {
        component: '零部件供应商',
        material: '原材料供应商',
        service: '服务供应商',
        other: '其他供应商',
      };
      return typeMap[text] || text;
    },
  },
  {
    title: '入驻状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '邀请时间',
    dataIndex: 'inviteTime',
    key: 'inviteTime',
  },
  {
    title: '入驻时间',
    dataIndex: 'registerTime',
    key: 'registerTime',
  },
  {
    title: '数据迁移状态',
    dataIndex: 'dataStatus',
    key: 'dataStatus',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
  },
];

// 统计数据
const statistics = reactive({
  registered: 3,
  pending: 1,
  waitingForInvite: 1,
  registerRate: 60,
});

// 是否有供应商
const hasSuppliers = computed(() => supplierList.value.length > 0);

// 是否有待处理的供应商
const hasPendingSuppliers = computed(() => statistics.pending > 0);

// 表格加载状态
const tableLoading = ref(false);

// 当前激活的标签页
const activeTabKey = ref('all');

// 一键邀请模态框
const inviteModalVisible = ref(false);
const inviteLoading = ref(false);
const inviteFileList = ref([]);
const inviteMethod = ref('excel');
const selectedSupplierKeys = ref([]);
const inviteMessage = ref('');
const notificationMethods = ref(['email', 'sms']);

// API对接表单数据
const apiFormData = reactive({
  systemType: undefined,
  apiUrl: '',
  authType: 'token',
  authCredential: '',
});

// 数据导入模态框
const dataImportModalVisible = ref(false);
const dataImportLoading = ref(false);
const dataFileList = ref([]);
const dataImportForm = reactive({
  supplierId: undefined,
  dataTypes: ['deliveryRecords', 'contracts'],
});

// 供应商选项
const supplierOptions = computed(() => {
  return supplierList.value.map(supplier => ({
    value: supplier.key,
    label: supplier.name,
  }));
});

// 过滤选项
const filterOption = (input, option) => {
  return (option?.label ?? '').toLowerCase().includes(input.toLowerCase());
};

// 供应商类型映射
const getSupplierTypeName = (type) => {
  const typeMap = {
    component: '零部件供应商',
    material: '原材料供应商',
    service: '服务供应商',
    other: '其他供应商',
  };
  return typeMap[type] || type;
};

// 未邀请的供应商列表
const uninvitedSuppliers = computed(() => {
  return supplierList.value.filter(s => s.status === '待邀请');
});

// 邀请表格列定义
const inviteColumns = [
  {
    title: '供应商名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '联系人',
    dataIndex: 'contact',
    key: 'contact',
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    key: 'phone',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
  },
  {
    title: '供应商类型',
    dataIndex: 'type',
    key: 'type',
  },
];

// 选择供应商变更
const onSelectSupplierChange = (selectedRowKeys) => {
  selectedSupplierKeys.value = selectedRowKeys;
};

// 打开邀请模态框
const showInviteModal = () => {
  selectedSupplierKeys.value = [];
  inviteMessage.value = '';
  inviteModalVisible.value = true;
};

// 上传前验证邀请文件
const beforeInviteUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                 file.type === 'application/vnd.ms-excel';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isExcel) {
    return window.$message.error('只能上传Excel文件!');
  }
  if (!isLt2M) {
    return window.$message.error('文件必须小于2MB!');
  }
  return false; // 阻止自动上传
};

// 上传前验证数据文件
const beforeDataUpload = (file) => {
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    return window.$message.error('文件必须小于20MB!');
  }
  return false; // 阻止自动上传
};

// 下载模板
const downloadTemplate = () => {
  console.log('下载模板');
  // 实际项目中这里应连接到后端下载接口
};

// 提交邀请
const handleInviteSubmit = async () => {
  if (selectedSupplierKeys.value.length === 0) {
    return window.$message.warning('请至少选择一家供应商进行邀请!');
  }
  
  inviteLoading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // 获取当前日期
    const now = new Date();
    const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
    
    // 更新所选供应商状态
    selectedSupplierKeys.value.forEach(key => {
      const index = supplierList.value.findIndex(s => s.key === key);
      if (index !== -1) {
        supplierList.value[index].status = '未入驻';
        supplierList.value[index].inviteTime = formattedDate;
      }
    });
    
    // 更新统计数据
    const invitedCount = selectedSupplierKeys.value.length;
    statistics.waitingForInvite -= invitedCount;
    statistics.pending += invitedCount;
    
    window.$message.success(`成功向 ${invitedCount} 家供应商发送入驻邀请!`);
    inviteModalVisible.value = false;
  } catch (error) {
    console.error('邀请发送失败', error);
    window.$message.error('邀请发送失败，请重试!');
  } finally {
    inviteLoading.value = false;
  }
};

// 提交数据导入
const handleDataImportSubmit = async () => {
  dataImportLoading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500));
    console.log('提交数据导入', {
      supplierId: dataImportForm.supplierId,
      dataTypes: dataImportForm.dataTypes,
      fileList: dataFileList.value,
    });
    
    // 成功后重置并关闭模态框
    window.$message.success('数据上传成功!');
    dataImportModalVisible.value = false;
    
    // 更新表格数据(实际项目中应该重新请求数据)
    const supplier = supplierList.value.find(s => s.key === dataImportForm.supplierId);
    if (supplier) {
      supplier.dataCompletionRate = 100;
    }
    
  } catch (error) {
    console.error('数据上传失败', error);
    window.$message.error('数据上传失败，请重试!');
  } finally {
    dataImportLoading.value = false;
  }
};

// 发送催办提醒
const sendReminder = async (record) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800));
    console.log('发送催办提醒', record);
    window.$message.success(`已向 ${record.name} 发送入驻催办提醒!`);
  } catch (error) {
    console.error('催办发送失败', error);
    window.$message.error('催办发送失败，请重试!');
  }
};

// 查看供应商详情
const viewSupplierDetails = (record) => {
  console.log('查看供应商详情', record);
  // 实际项目中应导航到供应商详情页
};

// 批量催办未入驻供应商
const inviteAllPendingSuppliers = async () => {
  try {
    const pendingSuppliers = supplierList.value.filter(s => s.status === '未入驻');
    console.log('批量催办', pendingSuppliers);
    await new Promise(resolve => setTimeout(resolve, 1000));
    window.$message.success(`已向 ${pendingSuppliers.length} 家未入驻供应商发送入驻催办提醒!`);
  } catch (error) {
    console.error('批量催办失败', error);
    window.$message.error('批量催办失败，请重试!');
  }
};

// 添加供应商模态框
const addSupplierModalVisible = ref(false);
const addSupplierLoading = ref(false);
const addSupplierMethod = ref('manual');
const addSupplierFileList = ref([]);

// 供应商表单数据
const supplierForm = reactive({
  name: '',
  contact: '',
  phone: '',
  email: '',
  type: 'component',
  remark: '',
});

// 编辑供应商模态框
const editSupplierModalVisible = ref(false);
const editSupplierLoading = ref(false);
const editSupplierForm = reactive({
  key: '',
  name: '',
  contact: '',
  phone: '',
  email: '',
  type: 'component',
  remark: '',
});

// 打开添加供应商模态框
const showAddSupplierModal = () => {
  // 重置表单
  Object.keys(supplierForm).forEach(key => {
    if (key !== 'type') {
      supplierForm[key] = '';
    } else {
      supplierForm[key] = 'component';
    }
  });
  addSupplierMethod.value = 'manual';
  addSupplierFileList.value = [];
  addSupplierModalVisible.value = true;
};

// 上传前验证供应商文件
const beforeSupplierUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                 file.type === 'application/vnd.ms-excel';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isExcel) {
    return window.$message.error('只能上传Excel文件!');
  }
  if (!isLt2M) {
    return window.$message.error('文件必须小于2MB!');
  }
  return false; // 阻止自动上传
};

// 下载供应商导入模板
const downloadSupplierTemplate = () => {
  console.log('下载供应商导入模板');
  // 实际项目中这里应连接到后端下载接口
};

// 提交添加供应商
const handleAddSupplierSubmit = async () => {
  addSupplierLoading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (addSupplierMethod.value === 'manual') {
      // 手动添加供应商
      const newSupplier = {
        key: `${supplierList.value.length + 1}`,
        name: supplierForm.name,
        contact: supplierForm.contact,
        phone: supplierForm.phone,
        email: supplierForm.email,
        type: supplierForm.type,
        status: '待邀请',
        inviteTime: null,
        registerTime: null,
        dataCompletionRate: 0,
      };
      
      supplierList.value.push(newSupplier);
      statistics.waitingForInvite += 1;
      window.$message.success('供应商添加成功!');
    } else {
      // Excel导入供应商
      console.log('Excel导入供应商', addSupplierFileList.value);
      // 模拟导入了3个供应商
      window.$message.success('成功导入3家供应商!');
      statistics.waitingForInvite += 3;
    }
    
    addSupplierModalVisible.value = false;
  } catch (error) {
    console.error('添加供应商失败', error);
    window.$message.error('添加供应商失败，请重试!');
  } finally {
    addSupplierLoading.value = false;
  }
};

// 编辑供应商
const editSupplier = (record) => {
  editSupplierForm.key = record.key;
  editSupplierForm.name = record.name;
  editSupplierForm.contact = record.contact;
  editSupplierForm.phone = record.phone;
  editSupplierForm.email = record.email;
  editSupplierForm.type = record.type || 'component';
  editSupplierForm.remark = record.remark || '';
  
  editSupplierModalVisible.value = true;
};

// 提交编辑供应商
const handleEditSupplierSubmit = async () => {
  editSupplierLoading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const index = supplierList.value.findIndex(item => item.key === editSupplierForm.key);
    if (index !== -1) {
      // 更新供应商信息，保留原有其他字段不变
      supplierList.value[index] = {
        ...supplierList.value[index],
        name: editSupplierForm.name,
        contact: editSupplierForm.contact,
        phone: editSupplierForm.phone,
        email: editSupplierForm.email,
        type: editSupplierForm.type,
        remark: editSupplierForm.remark,
      };
      
      window.$message.success('供应商信息更新成功!');
    }
    
    editSupplierModalVisible.value = false;
  } catch (error) {
    console.error('更新供应商失败', error);
    window.$message.error('更新供应商失败，请重试!');
  } finally {
    editSupplierLoading.value = false;
  }
};

// 邀请单个供应商
const inviteSupplier = async (record) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const index = supplierList.value.findIndex(item => item.key === record.key);
    if (index !== -1) {
      const now = new Date();
      const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
      
      // 更新供应商状态
      supplierList.value[index].status = '未入驻';
      supplierList.value[index].inviteTime = formattedDate;
      
      // 更新统计数据
      statistics.waitingForInvite -= 1;
      statistics.pending += 1;
      
      window.$message.success(`已向 ${record.name} 发送入驻邀请!`);
    }
  } catch (error) {
    console.error('邀请发送失败', error);
    window.$message.error('邀请发送失败，请重试!');
  }
};

// 组件挂载
onMounted(() => {
  // 实际项目中这里应该调用API获取数据
  console.log('组件挂载');
});
</script>

<style scoped>
.private-supplier-container {
  padding: 0;
}

.row-margin {
  margin-top: 16px;
}

.dashboard-card {
  margin-bottom: 16px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.dashboard-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  margin-right: 8px;
}

.migration-board {
  min-height: 600px;
}

.table-actions {
  display: flex;
}

.upload-container {
  margin-bottom: 16px;
}

.template-download {
  text-align: right;
  margin-top: 8px;
}

.api-integration-form {
  padding: 8px 0;
}

.notice-setting {
  margin-top: 16px;
}

.invite-supplier-content {
  max-height: 600px;
  overflow-y: auto;
}

.invite-message {
  margin-top: 16px;
}
</style>
