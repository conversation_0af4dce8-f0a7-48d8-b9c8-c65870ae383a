<template>
  <div class="temp-quote-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="logo-section">
          <img src="../../assets/images/logo.png" alt="研选工场" class="logo-image">
          <span class="page-title">报价单</span>
        </div>
        <div class="header-info">
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 询价单概要信息 -->
      <div class="rfq-summary">
        <a-card>
          <div class="summary-header">
            <div class="buyer-info">
              <a-avatar size="large" style="background-color: #1890ff;">{{ buyerInfo.name.charAt(0) }}</a-avatar>
              <div class="buyer-details">
                <h3>{{ buyerInfo.fullName }}</h3>
                <p>询价单号：{{ rfqData.rfqNo }}</p>
              </div>
            </div>
            <div class="rfq-status">
              <a-tag :color="getStatusColor(rfqData.status)">{{ getStatusText(rfqData.status) }}</a-tag>
              <div class="deadline-info">
                <span class="deadline-label">截止时间：</span>
                <span class="deadline-value">{{ formatDateTime(rfqData.deadline) }}</span>
              </div>
            </div>
          </div>
          
          <a-divider />
          
          <a-row :gutter="24">
            <a-col :span="6">
              <div class="summary-item">
                <span class="label">物料种类</span>
                <span class="value">{{ rfqData.materialModelCount }} 种</span>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="summary-item">
                <span class="label">总数量</span>
                <span class="value">{{ getTotalQuantity() }} 件</span>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="summary-item">
                <span class="label">联系人</span>
                <span class="value">{{ rfqData.creator }}</span>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="summary-item">
                <span class="label">联系电话</span>
                <span class="value">{{ rfqData.contactPhone }}</span>
              </div>
            </a-col>
          </a-row>
        </a-card>
      </div>

      <!-- 报价表单 -->
      <div class="quote-form-section">
        <a-card title="供应商信息">
          <a-form
            ref="quoteFormRef"
            :model="quoteForm"
            :rules="formRules"
            layout="vertical"
            @finish="handleSubmit"
          >
            <!-- 供应商信息部分 -->
            <div class="supplier-info-section">
              <h3 style="margin-bottom: 16px; color: #262626;">供应商信息</h3>
              <a-row :gutter="24">
                <a-col :span="8">
                  <a-form-item label="供应商类型" name="supplierType" required>
                    <a-select 
                      v-model:value="quoteForm.supplierType" 
                      placeholder="请选择供应商类型"
                    >
                      <a-select-option value="brand">品牌商</a-select-option>
                      <a-select-option value="trader">贸易商</a-select-option>
                      <a-select-option value="processor">加工商</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="企业名称" name="companyName" required>
                    <a-input 
                      v-model:value="quoteForm.companyName" 
                      placeholder="请输入企业全称"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="联系人姓名" name="contactName" required>
                    <a-input 
                      v-model:value="quoteForm.contactName" 
                      placeholder="请输入联系人姓名"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="联系电话" name="contactPhone" required>
                    <a-input 
                      v-model:value="quoteForm.contactPhone" 
                      placeholder="请输入联系电话"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="主营品牌" name="mainBrands" required>
                    <a-input 
                      v-model:value="quoteForm.mainBrands" 
                      placeholder="请输入主营品牌（多个用逗号分隔）"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="主营产品分类" name="mainProducts" required>
                    <a-input 
                      v-model:value="quoteForm.mainProducts" 
                      placeholder="请输入主营产品分类（多个用逗号分隔）"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>

            <a-divider />

            <!-- 物料报价部分 -->
            <div class="materials-quote-section">
              <div class="section-header">
                <h3 style="margin-bottom: 16px; color: #262626;">物料报价</h3>
              </div>
              
              <!-- 批量操作按钮 -->
              <div class="batch-operations" style="margin-bottom: 16px;">
                <a-dropdown :disabled="selectedRowKeys.length === 0">
                  <template #overlay>
                    <a-menu>
                      <a-menu-item key="reject" @click="handleBatchReject">
                        拒绝
                      </a-menu-item>
                      <a-menu-item key="reset" @click="handleBatchReset">
                        重置
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button>
                    批量操作
                    <down-outlined />
                  </a-button>
                </a-dropdown>
                <span v-if="selectedRowKeys.length > 0" style="margin-left: 8px; color: #1890ff;">
                  已选择 {{ selectedRowKeys.length }} 项
                </span>
              </div>

              <!-- 物料报价表格 -->
              <div class="material-table">
                <a-table
                  :dataSource="rfqData.materials"
                  :columns="tableColumns"
                  :pagination="false"
                  :scroll="{ x: 1600 }"
                  bordered
                  rowKey="id"
                  size="small"
                >
                  <!-- 各列渲染 -->
                  <template #bodyCell="{ column, text, record, index }">
                    <!-- 选择框列 -->
                    <template v-if="column.key === 'selection'">
                      <a-checkbox 
                        :checked="selectedRowKeys.includes(record.id)"
                        @change="handleRowSelect(record, $event.target.checked)"
                      />
                    </template>
                    
                    <!-- 物料名称列 -->
                    <template v-else-if="column.key === 'materialName'">
                      <div class="material-name-cell">
                        <div class="main-text">{{ record.name }}</div>
                      </div>
                    </template>
                    
                    <!-- 接受平替列 -->
                    <template v-else-if="column.key === 'acceptAlternative'">
                      <a-tag :color="record.acceptAlternative ? 'green' : 'orange'">
                        {{ record.acceptAlternative ? '是' : '否' }}
                      </a-tag>
                    </template>
                    
                    <!-- 单价列 -->
                    <template v-else-if="column.key === 'price'">
                      <a-form-item :name="['materials', index, 'price']" style="margin: 0;">
                        <a-input-number 
                          v-model:value="quoteForm.materials[index].price"
                          placeholder="请输入"
                          :precision="2"
                          :min="0"
                          style="width: 100%"
                          size="small"
                          :disabled="quoteForm.materials[index].status === 'rejected'"
                          @change="calculateTotalPrice(index)"
                        />
                      </a-form-item>
                    </template>
                    
                    <!-- 总价列 -->
                    <template v-else-if="column.key === 'totalPrice'">
                      <a-form-item :name="['materials', index, 'totalPrice']" style="margin: 0;">
                        <a-input-number 
                          v-model:value="quoteForm.materials[index].totalPrice"
                          placeholder="自动计算"
                          :precision="2"
                          :min="0"
                          style="width: 100%"
                          size="small"
                          disabled
                        />
                      </a-form-item>
                    </template>
                    
                    <!-- 承诺交期列 -->
                    <template v-else-if="column.key === 'promisedDelivery'">
                      <a-form-item :name="['materials', index, 'promisedDelivery']" style="margin: 0;">
                        <a-input-number 
                          v-model:value="quoteForm.materials[index].promisedDelivery"
                          placeholder="请输入天数"
                          :min="1"
                          style="width: 100%"
                          size="small"
                          :disabled="quoteForm.materials[index].status === 'rejected'"
                        />
                      </a-form-item>
                    </template>
                    
                    <!-- 平替型号列 -->
                    <template v-else-if="column.key === 'alternativeModel'">
                      <a-form-item :name="['materials', index, 'alternativeModel']" style="margin: 0;">
                        <a-input 
                          v-model:value="quoteForm.materials[index].alternativeModel"
                          placeholder="请输入平替型号"
                          size="small"
                          :disabled="!record.acceptAlternative || quoteForm.materials[index].status === 'rejected'"
                        />
                      </a-form-item>
                    </template>
                    
                    <!-- 报价备注列 -->
                    <template v-else-if="column.key === 'quoteRemark'">
                      <a-form-item :name="['materials', index, 'remark']" style="margin: 0;">
                        <a-input 
                          v-model:value="quoteForm.materials[index].remark"
                          placeholder="可选"
                          size="small"
                          :disabled="quoteForm.materials[index].status === 'rejected'"
                        />
                      </a-form-item>
                    </template>

                    <!-- 报价状态列 -->
                    <template v-else-if="column.key === 'quoteStatus'">
                      <a-tag :color="quoteForm.materials[index].status === 'rejected' ? 'red' : 'blue'">
                        {{ quoteForm.materials[index].status === 'rejected' ? '已拒绝' : '报价中' }}
                      </a-tag>
                    </template>
                  </template>
                </a-table>
              </div>
            </div>
            
            <!-- 提交按钮 -->
            <div class="form-actions">
              <a-button 
                type="primary" 
                size="large"
                html-type="submit"
                :loading="submitLoading"
              >
                提交报价
              </a-button>
              <a-button size="large" @click="handleCancel">
                取消
              </a-button>
            </div>
          </a-form>
        </a-card>
      </div>
    </div>



    <!-- 提交确认弹框 -->
    <a-modal
      v-model:open="confirmModalVisible"
      title="确认提交"
      :footer="null"
      width="400px"
    >
             <div style="text-align: center; padding: 20px 0;">
         <div style="font-size: 48px; color: #faad14; margin-bottom: 16px;">⚠️</div>
         <p style="font-size: 16px; margin-bottom: 24px;">
           报价提交之后不可修改，是否继续？
         </p>
        <a-space>
          <a-button @click="confirmModalVisible = false">取消</a-button>
          <a-button type="primary" @click="handleConfirmSubmit" :loading="submitLoading">
            确认提交
          </a-button>
        </a-space>
      </div>
    </a-modal>

    <!-- 拒绝弹框 -->
    <a-modal
      v-model:open="rejectModalVisible"
      title="拒绝报价"
      :footer="null"
      width="500px"
    >
      <div style="padding: 20px 0;">
        <p style="margin-bottom: 16px;">请选择拒绝原因或填写备注：</p>
        
        <!-- 常见原因选择 -->
        <div style="margin-bottom: 16px;">
          <h4 style="margin-bottom: 8px;">常见原因：</h4>
          <a-space wrap>
            <a-button 
              v-for="reason in commonRejectReasons" 
              :key="reason"
              size="small"
              @click="selectRejectReason(reason)"
            >
              {{ reason }}
            </a-button>
          </a-space>
        </div>
        
        <!-- 备注输入 -->
        <div style="margin-bottom: 24px;">
          <h4 style="margin-bottom: 8px;">备注：</h4>
          <a-textarea
            v-model:value="rejectForm.remark"
            placeholder="请输入拒绝原因"
            :rows="3"
          />
        </div>
        
        <!-- 操作按钮 -->
        <div style="text-align: right;">
          <a-space>
            <a-button @click="rejectModalVisible = false">取消</a-button>
            <a-button type="primary" @click="handleConfirmReject">
              确认拒绝
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 供应商入驻引导页脚 -->
    <div class="supplier-footer">
      <div class="footer-content">
        <a-row :gutter="48">
          <a-col :span="8">
            <div class="footer-section">
              <h3>成为正式供应商</h3>
              <p>加入研选工场供应商生态圈</p>
              <p>享受AI智能匹配、数据分析、渠道拓展等专业服务</p>
              <a-button type="link" @click="handleJoinSupplier" style="padding-left: 0;">
                立即入驻 →
              </a-button>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="footer-section">
              <h4>平台优势</h4>
              <ul>
                <li>AI智能供需匹配，匹配精度85%</li>
                <li>数据智能分析，业绩提升40%</li>
                <li>多渠道销售拓展，增长300%</li>
                <li>1000+合作伙伴生态</li>
              </ul>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="footer-section">
              <h4>供应商服务</h4>
              <p>咨询热线：400-888-9999</p>
              <p>邮箱：<EMAIL></p>
              <p>快速入驻，专属客户经理服务</p>
              <a-button size="small" @click="handleLearnMore">了解更多</a-button>
            </div>
          </a-col>
        </a-row>
        <a-divider />
        <div class="footer-bottom">
          <p>&copy; 2025 研选工场（苏州）网络有限公司 | 苏ICP备2024149956号</p>
          <p>专业的工业品采购平台 · 智能化供应链解决方案</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { DownOutlined } from '@ant-design/icons-vue';

const route = useRoute();
const router = useRouter();

// 表单引用
const quoteFormRef = ref();

// 加载状态
const submitLoading = ref(false);

// 显示状态
const confirmModalVisible = ref(false);
const rejectModalVisible = ref(false);

// 选择状态
const selectedRowKeys = ref([]);
const selectedRows = ref([]);

// 拒绝弹框相关
const rejectForm = ref({
  remark: ''
});

const commonRejectReasons = [
  '暂时无货',
  '不做该产品',
  '价格不合适',
  '交期无法满足',
  '规格不匹配',
  '产能不足'
];

// 买方信息
const buyerInfo = ref({
  name: '大疆科技',
  fullName: '深圳市大疆科技有限公司'
});

// 询价单数据
const rfqData = ref({
  id: '',
  rfqNo: '',
  status: 'inProgress',
  deadline: '',
  creator: '',
  contactPhone: '',
  materialModelCount: 0,
  materials: []
});

// 报价表单数据
const quoteForm = ref({
  // 供应商信息
  supplierType: '',
  companyName: '',
  contactName: '',
  contactPhone: '',
  mainBrands: '',
  mainProducts: '',
  // 物料报价信息
  materials: []
});

// 表单验证规则
const formRules = {
  supplierType: [
    { required: true, message: '请选择供应商类型', trigger: 'change' }
  ],
  companyName: [
    { required: true, message: '请输入企业名称', trigger: 'blur' }
  ],
  contactName: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  mainBrands: [
    { required: true, message: '请输入主营品牌', trigger: 'blur' }
  ],
  mainProducts: [
    { required: true, message: '请输入主营产品分类', trigger: 'blur' }
  ]
};

// 表格列定义
const tableColumns = [
  {
    title: '',
    key: 'selection',
    width: 50,
    fixed: 'left'
  },
  {
    title: '物料名称',
    dataIndex: 'name',
    key: 'materialName',
    width: 120,
    fixed: 'left'
  },
  {
    title: '型号',
    dataIndex: 'model',
    key: 'model',
    width: 100
  },
  {
    title: '品牌',
    dataIndex: 'brand',
    key: 'brand',
    width: 100
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
    width: 80,
    align: 'center'
  },
  {
    title: '期望交期',
    dataIndex: 'expectedDelivery',
    key: 'expectedDelivery',
    width: 100,
    align: 'center'
  },
  {
    title: '接受平替',
    dataIndex: 'acceptAlternative',
    key: 'acceptAlternative',
    width: 100,
    align: 'center'
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 120,
    customRender: ({ text }) => text || '无'
  },
  {
    title: '单价 (¥)',
    key: 'price',
    width: 120,
    align: 'center'
  },
  {
    title: '总价 (¥)',
    key: 'totalPrice',
    width: 120,
    align: 'center'
  },
  {
    title: '承诺交期（天）',
    key: 'promisedDelivery',
    width: 130,
    align: 'center'
  },
  {
    title: '平替型号',
    key: 'alternativeModel',
    width: 120,
    align: 'center'
  },
  {
    title: '报价备注',
    key: 'quoteRemark',
    width: 120,
    align: 'center'
  },
  {
    title: '报价状态',
    key: 'quoteStatus',
    width: 100,
    align: 'center'
  }
];



// 工具方法
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  return dateTimeStr;
};

const getStatusText = (status) => {
  const statusMap = {
    notStarted: '未开始',
    inProgress: '询价中',
    accepted: '已采纳',
    expired: '已过期',
    invalid: '已失效',
    cancelled: '已取消',
  };
  return statusMap[status] || status;
};

const getStatusColor = (status) => {
  const colorMap = {
    notStarted: 'default',
    inProgress: 'blue',
    accepted: 'green',
    expired: 'orange',
    invalid: 'red',
    cancelled: 'red',
  };
  return colorMap[status] || 'default';
};

const getTotalQuantity = () => {
  return rfqData.value.materials.reduce((total, item) => total + item.quantity, 0);
};

// 操作方法
const calculateTotalPrice = (index) => {
  const quote = quoteForm.value.materials[index];
  const material = rfqData.value.materials[index];
  if (quote?.price && material?.quantity) {
    quote.totalPrice = quote.price * material.quantity;
  }
};

// 行选择相关方法
const handleRowSelect = (record, checked) => {
  if (checked) {
    selectedRowKeys.value.push(record.id);
    selectedRows.value.push(record);
  } else {
    const index = selectedRowKeys.value.indexOf(record.id);
    if (index > -1) {
      selectedRowKeys.value.splice(index, 1);
      selectedRows.value.splice(index, 1);
    }
  }
};

const handleCancel = () => {
  router.back();
};

// 批量操作方法
const handleBatchReject = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要拒绝的物料');
    return;
  }
  rejectForm.value.remark = '';
  rejectModalVisible.value = true;
};

const handleBatchReset = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要重置的物料');
    return;
  }
  
  selectedRowKeys.value.forEach(materialId => {
    const materialIndex = rfqData.value.materials.findIndex(m => m.id === materialId);
    if (materialIndex !== -1) {
      quoteForm.value.materials[materialIndex] = {
        price: null,
        totalPrice: null,
        promisedDelivery: null,
        remark: '',
        alternativeModel: '',
        status: 'quoting'
      };
    }
  });
  
  // 清空选择
  selectedRowKeys.value = [];
  selectedRows.value = [];
  
  message.success('已重置选中物料的报价信息');
};

const selectRejectReason = (reason) => {
  rejectForm.value.remark = reason;
};

const handleConfirmReject = () => {
  if (!rejectForm.value.remark.trim()) {
    message.warning('请填写拒绝原因');
    return;
  }
  
  selectedRowKeys.value.forEach(materialId => {
    const materialIndex = rfqData.value.materials.findIndex(m => m.id === materialId);
    if (materialIndex !== -1) {
      quoteForm.value.materials[materialIndex].status = 'rejected';
      quoteForm.value.materials[materialIndex].remark = rejectForm.value.remark;
      // 清空其他报价信息
      quoteForm.value.materials[materialIndex].price = null;
      quoteForm.value.materials[materialIndex].totalPrice = null;
      quoteForm.value.materials[materialIndex].promisedDelivery = null;
      quoteForm.value.materials[materialIndex].alternativeModel = '';
    }
  });
  
  // 清空选择和关闭弹框
  selectedRowKeys.value = [];
  selectedRows.value = [];
  rejectModalVisible.value = false;
  
  message.success('已拒绝选中的物料');
};



const handleSubmit = async () => {
  try {
    // 先进行表单验证
    await quoteFormRef.value.validateFields();
    
    // 验证至少有一个物料填写了完整的报价信息
    const hasValidQuote = quoteForm.value.materials.some(material => 
      material?.price > 0 && material?.promisedDelivery
    );
    
    if (!hasValidQuote) {
      message.warning('请至少为一个物料填写完整的报价信息（单价、承诺交期）');
      return;
    }
    
    // 显示确认弹框
    confirmModalVisible.value = true;
    
  } catch (error) {
    console.error('表单验证失败:', error);
    message.error('请检查填写的信息是否完整');
  }
};

const handleConfirmSubmit = async () => {
  try {
    submitLoading.value = true;
    confirmModalVisible.value = false;
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    message.success('报价提交成功！买方将收到您的报价信息。');
    
    // 跳转到成功页面或返回
    setTimeout(() => {
      router.push('/supplier/quote-success');
    }, 1000);
    
  } catch (error) {
    message.error('提交失败，请重试');
  } finally {
    submitLoading.value = false;
  }
};

// 解析分享参数并获取询价单详情
const parseShareParams = () => {
  try {
    const paramStr = route.query.param;
    if (!paramStr) {
      throw new Error('无效的分享链接');
    }
    
    const params = JSON.parse(atob(paramStr));
    return params;
  } catch (error) {
    message.error('分享链接格式错误');
    return null;
  }
};

// 获取询价单详情
const fetchRfqDetails = async () => {
  try {
    const params = parseShareParams();
    if (!params) return;

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 模拟询价单数据
    rfqData.value = {
      id: '1',
      rfqNo: params.rfqNo || 'RFQ-2023-0001',
      status: 'inProgress',
      deadline: '2023-10-25 18:00:00',
      creator: '张三',
      contactPhone: '13800138000',
      materialModelCount: 4,
      materials: Array.from({ length: 4 }).map((_, index) => ({
        id: `material-${index}`,
        name: `电子元件 ${index + 1}`,
        model: `MODEL-${100 + index}`,
        brand: index % 3 === 0 ? '品牌A' : index % 3 === 1 ? '品牌B' : '品牌C',
        quantity: Math.floor(Math.random() * 100 + 10),
        expectedDelivery: '30天',
        status: 'inProgress',
        remark: index % 2 === 0 ? '紧急采购' : '',
        acceptAlternative: index % 3 === 0,
      }))
    };

    // 初始化报价表单数据
    quoteForm.value.materials = rfqData.value.materials.map(() => ({
      price: null,
      totalPrice: null,
      promisedDelivery: null,
      remark: '',
      alternativeModel: '',
      status: 'quoting'  // 默认状态为报价中
    }));

  } catch (error) {
    message.error('获取询价单详情失败');
  }
};

// 供应商入驻相关方法
const handleJoinSupplier = () => {
  // 跳转到供应商入驻页面
  window.open('http://prototype.yanxuan.icu/www/supplier', '_blank');
};

const handleLearnMore = () => {
  // 跳转到供应商服务介绍页面
  window.open('http://prototype.yanxuan.icu/www/supplier', '_blank');
};

// 生命周期
onMounted(() => {
  fetchRfqDetails();
});
</script>

<style lang="less" scoped>
.temp-quote-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  background-color: #2a2a35;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 100;

  .header-content {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;

    .logo-section {
      position: relative;
      display: flex;
      align-items: center;
      
      .logo-image {
        width: 150px;
      }

      .page-title {
        position: absolute;
        bottom: 0;
        top: 14px;
        right: -64px;
        color: #bbb;
        font-size: 18px;
        font-weight: 700;
      }
    }

    .header-info {
    }
  }
}

.main-content {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
}

.rfq-summary,
.quote-form-section {
  margin-bottom: 24px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;

  .buyer-info {
    display: flex;
    align-items: center;
    gap: 16px;

    .buyer-details {
      h3 {
        margin: 0 0 4px 0;
        color: #262626;
        font-size: 18px;
      }

      p {
        margin: 0;
        color: #8c8c8c;
      }
    }
  }

  .rfq-status {
    text-align: right;

    .deadline-info {
      margin-top: 8px;
      color: #8c8c8c;
      font-size: 14px;

      .deadline-label {
        margin-right: 8px;
      }

      .deadline-value {
        color: #f5222d;
        font-weight: 500;
      }
    }
  }
}

.summary-item {
  text-align: center;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;

  .label {
    display: block;
    color: #8c8c8c;
    font-size: 14px;
    margin-bottom: 4px;
  }

  .value {
    display: block;
    color: #262626;
    font-size: 16px;
    font-weight: 500;
  }
}

.supplier-info-section {
  background-color: #fafafa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.materials-quote-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
}

.material-table {
  .material-name-cell {
    .main-text {
      font-weight: 500;
      color: #262626;
    }
  }

  .ant-table {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
      color: #262626;
    }

    .ant-table-tbody > tr > td {
      vertical-align: top;
      padding: 12px 8px;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f0f9ff;
    }

    .ant-form-item {
      margin-bottom: 0;
    }

    .ant-input-number,
    .ant-input {
      border-radius: 4px;
    }
  }
}



.form-actions {
  text-align: center;
  margin-top: 32px;
  
  .ant-btn {
    margin: 0 8px;
    min-width: 120px;
  }
}

.modal-actions {
  text-align: center;
  margin-top: 24px;

  .ant-btn {
    margin: 0 8px;
  }
}

// 供应商入驻页脚样式
.supplier-footer {
  background-color: #2a2a35;
  color: #fff;
  margin-top: 48px;

  .footer-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 48px 24px 24px;

    .footer-section {
      h3 {
        color: #fff;
        font-size: 20px;
        margin-bottom: 16px;
        font-weight: 600;
      }

      h4 {
        color: #fff;
        font-size: 16px;
        margin-bottom: 12px;
        font-weight: 500;
      }

      p {
        color: #bfbfbf;
        margin-bottom: 8px;
        line-height: 1.6;
      }

      ul {
        color: #bfbfbf;
        padding-left: 16px;
        
        li {
          margin-bottom: 6px;
          line-height: 1.6;
        }
      }
    }

    .footer-bottom {
      text-align: center;
      color: #8c8c8c;
      margin-top: 16px;

      p {
        margin: 4px 0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }

  .summary-header {
    flex-direction: column;
    gap: 16px;

    .rfq-status {
      text-align: left;
    }
  }

  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start !important;
  }

  .form-actions,
  .modal-actions {
    .ant-btn {
      display: block;
      width: 100%;
      margin: 8px 0;
    }
  }

  // 供应商footer响应式
  .supplier-footer {
    .footer-content {
      padding: 32px 16px 16px;
      
      .footer-section {
        margin-bottom: 32px;
      }
    }
  }
}
</style> 