<template>
  <div class="bom-list-container">
    <!-- Search and filter area -->
    <a-row :gutter="16" class="search-filter-area">
      <a-col :span="6">
        <a-input placeholder="搜索BOM名称/编号" allowClear />
      </a-col>
      <!-- <a-col :span="4">
        <a-select placeholder="所有状态" style="width: 100%">
          <a-select-option value="">所有状态</a-select-option>
          <a-select-option value="parsed">已解析</a-select-option>
          <a-select-option value="analyzing">分析中</a-select-option>
          <a-select-option value="completed">已完成</a-select-option>
        </a-select>
      </a-col> -->
      <a-col :span="4">
        <a-button type="primary">搜索</a-button>
        <a-button class="ml-2">重置</a-button>
      </a-col>
    </a-row>

    <!-- Action buttons -->
    <a-row class="action-buttons my-4">
      <a-space>
        <a-button type="primary" @click="showCreateModal">
          <template #icon><plus-outlined /></template>
          新建
        </a-button>
      </a-space>
    </a-row>

    <!-- BOM Table -->
    <a-table
      :dataSource="bomList"
      :columns="columns"
      rowKey="id"
      :pagination="{
        total: totalItems,
        current: currentPage,
        pageSize: pageSize,
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '50'],
        showTotal: (total) => `共 ${total} 条`,
      }"
      :expandable="{
        expandedRowKeys: expandedRowKeys,
        onExpand: onExpand,
        expandRowByClick: true,
      }"
      @change="handleTableChange"
    >
      <!-- BOM Name/Number -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          <div class="font-bold">{{ record.name }}</div>
          <div class="text-xs text-gray-500">{{ record.code }}</div>
        </template>

        <!-- Project -->
        <template v-if="column.dataIndex === 'project'">
          <div v-if="record.projects && record.projects.length > 0">
            <a-popover placement="right">
              <template #content>
                <a-table :dataSource="record.projects" :columns="projectPopoverColumns" :pagination="false" size="small" :rowKey="(record) => record.id" style="width: 350px">
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'name'">
                      <div>
                        <a @click="goToProjectDetail(record.id)" class="text-primary">{{ record.name }}</a>
                        <div class="text-xs text-gray-500">{{ record.code || `-` }}</div>
                      </div>
                    </template>
                    <template v-if="column.dataIndex === 'stage'">
                      <a-tag :class="getProjectStageClass(record.stage)">
                        {{ record.stage }}
                      </a-tag>
                    </template>
                  </template>
                </a-table>
              </template>
              <a class="text-primary">查看</a>
            </a-popover>
          </div>
          <div v-else class="text-gray-500 text-xs">未关联项目</div>
        </template>

        <!-- Status -->
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)" class="status-badge">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <!-- Material types -->
        <template v-if="column.dataIndex === 'materialTypes'">
          <div>
            <a-tag color="success" v-if="record.standard">标准件: {{ record.standard }}</a-tag>
            <a-tag color="warning" v-if="record.custom">非标件: {{ record.custom }}</a-tag>
            <a-tag color="purple" v-if="record.assembly">外购组件: {{ record.assembly }}</a-tag>
          </div>
          <div class="text-xs text-gray-500">共{{ getTotalMaterials(record) }}个物料</div>
        </template>

        <!-- Match rate -->
        <template v-if="column.dataIndex === 'matchRate'">
          <div>匹配率: {{ record.matchRate }}%</div>
          <a-progress :percent="record.matchRate" size="small" :status="getMatchStatus(record)" />
          <div v-if="record.needConfirm" class="text-error text-xs"><exclamation-circle-outlined /> {{ record.needConfirm }}个物料需确认</div>
          <div v-if="record.status === 'analyzing'" class="text-xs text-gray-500">正在分析中...</div>
        </template>

        <!-- Actions -->
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a @click="showMaterialManagement(record)" class="text-primary">更新物料</a>
            <a @click="editBom(record)" class="text-primary">编辑</a>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <!-- <a-menu-item @click="showLinkProjectModal(record)">关联项目</a-menu-item> -->
                  <a-menu-item @click="showVersionModal(record)">版本管理</a-menu-item>
                  <a-menu-item @click="deleteBom(record)">删除</a-menu-item>
                </a-menu>
              </template>
              <a class="text-primary"> 更多 <down-outlined /> </a>
            </a-dropdown>
          </a-space>
        </template>
      </template>

      <!-- Expanded row for materials -->
      <template #expandedRowRender="{ record }">
        <a-table :dataSource="record.materials || []" :columns="materialsColumns" :pagination="false" size="small" :rowKey="(item) => item.id">
          <template #bodyCell="{ column, record: material }">
            <template v-if="column.dataIndex === 'inquiryNo'">
              <span v-if="material.inquiryNo && material.inquiryNo !== '无'">{{ material.inquiryNo }}</span>
              <span v-else class="text-gray-500 text-xs">未关联询价单</span>
            </template>
          </template>
        </a-table>
      </template>
    </a-table>

    <!-- Create/Edit BOM Modal -->
    <a-modal v-model:visible="bomModalVisible" :title="modalMode === 'create' ? 'BOM新建' : 'BOM编辑'" width="600px" @cancel="bomModalVisible = false">
      <a-form layout="horizontal" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="BOM名称" required name="bomName">
          <a-input placeholder="请输入BOM名称" v-model:value="bomForm.name" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea placeholder="请输入备注信息" v-model:value="bomForm.remark" rows="3" />
        </a-form-item>
      </a-form>

      <template #footer>
        <a-button @click="bomModalVisible = false">取消</a-button>
        <a-button type="primary" @click="saveBom">确定</a-button>
      </template>
    </a-modal>

    <!-- Link Project Modal -->
    <a-modal v-model:visible="linkProjectModalVisible" title="关联项目" width="1200px" @cancel="linkProjectModalVisible = false">
      <a-input placeholder="搜索项目名称/编号" v-model:value="projectSearchValue" class="mb-4" />

      <a-table
        :dataSource="filteredInProgressProjects"
        :columns="projectColumns"
        :pagination="false"
        rowKey="id"
        :rowSelection="{
          type: 'checkbox',
          selectedRowKeys: selectedProjectKeys,
          onChange: onSelectProject,
        }"
      />

      <template #footer>
        <div class="text-xs text-gray-500 mb-2">* 只能关联状态为"进行中"的项目</div>
        <a-button @click="linkProjectModalVisible = false">取消</a-button>
        <a-button type="primary" @click="confirmLinkProject">确定</a-button>
      </template>
    </a-modal>

    <!-- Version Management Modal -->
    <a-modal v-model:visible="versionModalVisible" title="版本管理" width="900px" @cancel="versionModalVisible = false">
      <a-table :dataSource="versions" :columns="versionColumns" rowKey="id" :pagination="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'version'">
            <span>{{ record.version }}</span>
            <a-tag v-if="record.isActive" color="green" class="ml-2">当前活跃</a-tag>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a @click="viewVersion(record)" class="text-primary">查看</a>
              <a v-if="!record.isActive" @click="activateVersion(record)" class="text-primary">设为活跃</a>
            </a-space>
          </template>
        </template>
      </a-table>

      <template #footer>
        <a-button @click="versionModalVisible = false">关闭</a-button>
      </template>
    </a-modal>

    <!-- BOM Analysis Modal -->
    <a-modal v-model:visible="analysisModalVisible" title="BOM解析进度" :footer="null" :closable="false" :maskClosable="false">
      <div class="text-center mb-5">
        <loading-outlined style="font-size: 48px; color: #1890ff" spin />
      </div>

      <div class="mb-4">
        <div class="flex justify-between mb-1">
          <span>解析进度</span>
          <span>{{ analysisProgress }}%</span>
        </div>
        <a-progress :percent="analysisProgress" />
      </div>

      <div class="mb-4">
        <div>当前操作: {{ currentAnalysisOperation }}</div>
        <div class="text-xs text-gray-500 mt-1">{{ currentAnalysisDetail }}</div>
      </div>

      <div class="mt-6 text-xs text-gray-500">解析完成后将自动为您生成BOM分析报告</div>
    </a-modal>

    <!-- BOM Items Management Modal -->
    <a-modal v-model:visible="bomItemsModalVisible" :title="isViewOnly ? '版本详情' : '更新物料'" width="1200px" @cancel="bomItemsModalVisible = false">
      <a-form layout="horizontal" :label-col="{ span: 2 }" :wrapper-col="{ span: 10 }">
        <a-form-item label="版本号" required name="version">
          <a-input placeholder="请输入版本号" v-model:value="bomItemsForm.version" :disabled="true" />
        </a-form-item>
        <a-form-item label="更新内容" name="updateContent">
          <a-textarea placeholder="请输入更新内容" v-model:value="bomItemsForm.updateContent" :disabled="isViewOnly" rows="3" />
        </a-form-item>
      </a-form>

      <div class="mb-4 mt-4" v-if="!isViewOnly">
        <a-button-group>
          <a-button class="mr-8" type="primary" @click="addBomItem"> <plus-outlined /> 手动添加 </a-button>
          <a-button type="primary" @click="showImportBomItemsModal"> <upload-outlined /> 导入物料 </a-button>
        </a-button-group>
      </div>
      <div class="text-primary mb-4"> ❗️以下表格使用询价宝导入excel的UI交互，本页面仅作字段参考</div>
      <a-table :dataSource="bomItemsForm.items" :columns="isViewOnly ? materialsColumns : bomItemColumns" :pagination="false" rowKey="id">
        <template #bodyCell="{ column, record, index }" v-if="!isViewOnly">
          <template v-if="column.dataIndex === 'index'">
            {{ index + 1 }}
          </template>

          <template v-if="column.dataIndex === 'productName'">
            <a-input v-model:value="record.name" />
          </template>

          <template v-if="column.dataIndex === 'model'">
            <a-input v-model:value="record.model" />
          </template>

          <template v-if="column.dataIndex === 'brand'">
            <a-auto-complete v-model:value="record.brand" :options="brandOptions" placeholder="搜索品牌" style="width: 100%" />
          </template>

          <template v-if="column.dataIndex === 'category'">
            <a-auto-complete v-model:value="record.category" :options="categoryOptions" placeholder="搜索分类" style="width: 100%" />
          </template>

          <template v-if="column.dataIndex === 'quantity'">
            <a-input-number v-model:value="record.quantity" :min="1" style="width: 100%" />
          </template>

          <template v-if="column.dataIndex === 'remark'">
            <a-input v-model:value="record.remark" />
          </template>

          <template v-if="column.dataIndex === 'action'">
            <a-button type="link" danger @click="removeBomItem(record)">
              <delete-outlined />
            </a-button>
          </template>
        </template>
      </a-table>

      <template #footer>
        <a-button @click="bomItemsModalVisible = false">{{ isViewOnly ? '关闭' : '取消' }}</a-button>
        <a-button type="primary" @click="saveBomItems" v-if="!isViewOnly">保存</a-button>
      </template>
    </a-modal>

    <!-- Import BOM Items Modal -->
    <a-modal v-model:visible="importBomItemsModalVisible" title="导入BOM物料" @cancel="importBomItemsModalVisible = false">
      <a-upload-dragger name="file" :multiple="false" action="/api/upload" @change="handleUploadChange">
        <p class="ant-upload-drag-icon">
          <inbox-outlined />
        </p>
        <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p class="ant-upload-hint">支持Excel、CSV格式文件</p>
      </a-upload-dragger>

      <template #footer>
        <a-button @click="importBomItemsModalVisible = false">取消</a-button>
        <a-button type="primary" @click="confirmImportBomItems" :disabled="!uploadedFile">导入</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import { PlusOutlined, UploadOutlined, DownloadOutlined, DeleteOutlined, ExclamationCircleOutlined, LoadingOutlined, DownOutlined, InboxOutlined } from '@ant-design/icons-vue';
import { defineComponent, ref, computed } from 'vue';

export default defineComponent({
  name: 'BomList',

  components: {
    PlusOutlined,
    UploadOutlined,
    DownloadOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    LoadingOutlined,
    DownOutlined,
    InboxOutlined,
  },

  setup() {
    // Table data
    const bomList = ref([
      {
        id: '1',
        name: 'AGV控制系统BOM',
        code: 'BOM-2024-001',
        projects: [{ id: '1', name: '智能AGV物流系统', stage: '进行中' }],
        status: 'completed',
        standard: 42,
        custom: 18,
        assembly: 5,
        matchRate: 98,
        needConfirm: 0,
        uploadTime: '2024-05-15 14:30',
        uploader: '李工',
        version: 'v1.0',
        isLatest: true,
        remark: '',
        materials: [
          {
            id: 'm1',
            name: 'PLC控制器',
            model: 'CP1E-N40DR-A',
            brand: '欧姆龙',
            category: '电子元器件',
            quantity: 2,
            remark: '',
            inquiryNo: 'RFQ-2024-001',
          },
          {
            id: 'm2',
            name: '交流伺服电机',
            model: 'R88M-K2K030H',
            brand: '欧姆龙',
            category: '电气设备',
            quantity: 4,
            remark: '',
            inquiryNo: '无',
          },
        ],
      },
      {
        id: '2',
        name: '数控机床主轴组件BOM',
        code: 'BOM-2024-002',
        projects: [{ id: '2', name: '高精度数控机床升级', stage: '进行中' }],
        status: 'parsed',
        standard: 28,
        custom: 35,
        assembly: 2,
        matchRate: 85,
        needConfirm: 10,
        uploadTime: '2024-05-13 09:45',
        uploader: '张工',
        version: 'v2.1',
        isLatest: true,
        remark: '',
        materials: [
          {
            id: 'm3',
            name: '伺服驱动器',
            model: 'MR-J4-200A',
            brand: '三菱',
            category: '电气设备',
            quantity: 2,
            remark: '',
            inquiryNo: '无',
          },
          {
            id: 'm4',
            name: '齿轮箱',
            model: 'GR63SMT16',
            brand: 'SEW',
            category: '机械部件',
            quantity: 2,
            remark: '高精度',
            inquiryNo: '无',
          },
        ],
      },
      {
        id: '3',
        name: '包装线电气系统BOM',
        code: 'BOM-2024-003',
        projects: [],
        status: 'analyzing',
        standard: 56,
        custom: 12,
        assembly: 8,
        matchRate: 32,
        needConfirm: 0,
        uploadTime: '2024-05-12 16:20',
        uploader: '王工',
        version: 'v1.0',
        isLatest: true,
        remark: '',
        materials: [],
      },
      {
        id: '4',
        name: '测试设备传感器BOM',
        code: 'BOM-2024-004',
        projects: [{ id: '4', name: '新型材料测试设备', stage: '进行中' }],
        status: 'completed',
        standard: 24,
        custom: 5,
        assembly: 15,
        matchRate: 100,
        needConfirm: 0,
        uploadTime: '2024-05-10 11:15',
        uploader: '赵工',
        version: 'v3.0',
        isLatest: true,
        remark: '',
        materials: [
          {
            id: 'm5',
            name: '钣金外壳',
            model: 'CNC-SHT-001',
            brand: '定制',
            category: '机械部件',
            quantity: 1,
            remark: '304不锈钢',
            inquiryNo: 'RFQ-2024-003',
          },
        ],
      },
      {
        id: '5',
        name: '机械臂一期驱动系统BOM',
        code: 'BOM-2024-005',
        projects: [
          { id: '5', name: '机械臂一期项目', stage: '已关闭' },
          { id: '8', name: '机械臂二期项目', stage: '进行中' },
        ],
        status: 'completed',
        standard: 32,
        custom: 14,
        assembly: 6,
        matchRate: 95,
        needConfirm: 3,
        uploadTime: '2024-05-08 15:40',
        uploader: '张工',
        version: 'v2.0',
        isLatest: true,
        remark: '',
        materials: [
          {
            id: 'm6',
            name: '观察窗',
            model: 'GL-500x300',
            brand: '安全玻璃',
            category: '结构件',
            quantity: 2,
            remark: '钢化玻璃',
            inquiryNo: 'RFQ-2024-003',
          },
        ],
      },
      {
        id: '6',
        name: '半导体设备控制板BOM',
        code: 'BOM-2024-006',
        projects: [{ id: '6', name: '半导体自动化设备', stage: '已关闭' }],
        status: 'completed',
        standard: 65,
        custom: 23,
        assembly: 12,
        matchRate: 99,
        needConfirm: 0,
        uploadTime: '2024-05-06 10:25',
        uploader: '刘工',
        version: 'v1.2',
        isLatest: true,
        remark: '',
        materials: [],
      },
      {
        id: '7',
        name: '工业机器人控制系统BOM',
        code: 'BOM-2024-007',
        projects: [
          { id: '3', name: '工业4.0自动化生产线', stage: '未开始' },
          { id: '7', name: '智能传感器网络', stage: '进行中' },
        ],
        status: 'parsed',
        standard: 48,
        custom: 27,
        assembly: 9,
        matchRate: 78,
        needConfirm: 15,
        uploadTime: '2024-05-05 13:50',
        uploader: '李工',
        version: 'v1.1',
        isLatest: true,
        remark: '',
        materials: [],
      },
    ]);

    // Table configs
    const columns = [
      {
        title: 'BOM名称/编号',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '活跃版本',
        dataIndex: 'version',
        key: 'version',
        width: 100,
      },
      {
        title: '更新时间',
        dataIndex: 'uploadTime',
        key: 'uploadTime',
      },
      {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 250,
      },
    ];

    const totalItems = ref(15);
    const currentPage = ref(1);
    const pageSize = ref(10);

    // Helper functions for status badges
    const getStatusColor = (status) => {
      const colorMap = {
        parsed: 'blue',
        analyzing: 'orange',
        completed: 'green',
      };
      return colorMap[status] || 'default';
    };

    const getStatusText = (status) => {
      const textMap = {
        parsed: '已解析',
        analyzing: '分析中',
        completed: '已完成',
      };
      return textMap[status] || '未知';
    };

    const getTotalMaterials = (record) => {
      return (record.standard || 0) + (record.custom || 0) + (record.assembly || 0);
    };

    const getMatchStatus = (record) => {
      if (record.matchRate >= 90) return 'success';
      if (record.matchRate >= 60) return 'normal';
      return 'exception';
    };

    const getStageClass = (stage) => {
      const stageMap = {
        未开始: 'status-not-started',
        进行中: 'status-in-progress',
        已关闭: 'status-closed',
      };
      return stageMap[stage] || '';
    };

    // Project linking modal
    const linkProjectModalVisible = ref(false);
    const currentBom = ref(null);
    const projectSearchValue = ref('');
    const selectedProjectKeys = ref([]);

    const projectColumns = [
      { title: '项目名称', dataIndex: 'name', key: 'name' },
      { title: '项目编号', dataIndex: 'code', key: 'code' },
      { title: '负责人', dataIndex: 'manager', key: 'manager' },
      { title: '当前阶段', dataIndex: 'stage', key: 'stage' },
      { title: '截止日期', dataIndex: 'deadline', key: 'deadline' },
    ];

    const projects = ref([
      { id: '1', name: '智能AGV物流系统', code: 'PRJ-2024-001', manager: '李工', stage: '进行中', deadline: '2024-08-30' },
      { id: '2', name: '高精度数控机床升级', code: 'PRJ-2024-002', manager: '张工', stage: '进行中', deadline: '2024-09-15' },
      { id: '3', name: '工业4.0自动化生产线', code: 'PRJ-2024-003', manager: '李工', stage: '未开始', deadline: '2024-11-20' },
      { id: '4', name: '新型材料测试设备', code: 'PRJ-2024-004', manager: '赵工', stage: '进行中', deadline: '2024-12-30' },
      { id: '5', name: '机械臂一期项目', code: 'PRJ-2024-005', manager: '张工', stage: '已关闭', deadline: '2024-10-25' },
      { id: '6', name: '半导体自动化设备', code: 'PRJ-2024-006', manager: '刘工', stage: '已关闭', deadline: '2024-07-15' },
      { id: '7', name: '智能传感器网络', code: 'PRJ-2024-007', manager: '王工', stage: '进行中', deadline: '2025-01-15' },
      { id: '8', name: '机械臂二期项目', code: 'PRJ-2024-008', manager: '张工', stage: '进行中', deadline: '2025-03-20' },
    ]);

    const filteredInProgressProjects = computed(() => {
      if (!projectSearchValue.value) {
        return projects.value.filter((p) => p.stage === '进行中');
      }

      const search = projectSearchValue.value.toLowerCase();
      return projects.value.filter((p) => p.stage === '进行中' && (p.name.toLowerCase().includes(search) || p.code.toLowerCase().includes(search)));
    });

    // Version Management
    const versionModalVisible = ref(false);
    const versions = ref([]);

    const versionColumns = [
      { title: '版本号', dataIndex: 'version', key: 'version' },
      { title: '更新时间', dataIndex: 'createTime', key: 'createTime' },
      { title: '更新人', dataIndex: 'creator', key: 'creator' },
      { title: '操作', dataIndex: 'action', key: 'action', width: 180 },
    ];

    // BOM Create/Edit Modal
    const bomModalVisible = ref(false);
    const modalMode = ref('create');
    const bomForm = ref({
      name: '',
      version: 'v1.0',
      remark: '',
    });

    // BOM item columns for edit modal
    const bomItemColumns = [
      { title: '序号', dataIndex: 'index', width: 60, align: 'center' },
      { title: '产品名称', dataIndex: 'productName' },
      { title: '型号 *', dataIndex: 'model' },
      { title: '品牌 *', dataIndex: 'brand' },
      { title: '产品分类 *', dataIndex: 'category' },
      { title: '数量 *', dataIndex: 'quantity', width: 100 },
      { title: '备注', dataIndex: 'remark' },
      { title: '操作', dataIndex: 'action', width: 60, align: 'center' },
    ];

    const brandOptions = ref([{ value: 'Siemens' }, { value: 'ABB' }, { value: 'Schneider' }, { value: '三菱' }, { value: '欧姆龙' }]);

    const categoryOptions = ref([{ value: '电气元件' }, { value: '机械传动' }, { value: '传感器' }, { value: '气动元件' }, { value: '液压元件' }]);

    let bomItemId = 1;

    // Analysis Modal
    const analysisModalVisible = ref(false);
    const analysisProgress = ref(0);
    const currentAnalysisOperation = ref('初始化');
    const currentAnalysisDetail = ref('准备解析BOM文件');

    // Project popover columns
    const projectPopoverColumns = [
      {
        title: '项目名称/编号',
        dataIndex: 'name',
        key: 'name',
        width: '70%',
      },
      {
        title: '阶段',
        dataIndex: 'stage',
        key: 'stage',
        width: '30%',
      },
    ];

    // New or updated refs for the two-step BOM creation process
    const bomItemsModalVisible = ref(false);
    const importBomItemsModalVisible = ref(false);
    const uploadedFile = ref(null);
    const currentBomId = ref(null);
    const bomItemsForm = ref({
      items: [],
      version: '',
      updateContent: '',
    });

    // Materials columns for expanded row
    const materialsColumns = [
      {
        title: '产品名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '型号',
        dataIndex: 'model',
        key: 'model',
      },
      {
        title: '品牌',
        dataIndex: 'brand',
        key: 'brand',
      },
      {
        title: '产品分类',
        dataIndex: 'category',
        key: 'category',
      },
      {
        title: '数量',
        dataIndex: 'quantity',
        key: 'quantity',
      },
      {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
      },
    ];

    const expandedRowKeys = ref([]);

    // Handle expand/collapse of BOM rows
    const onExpand = (expanded, record) => {
      if (expanded) {
        expandedRowKeys.value = [...expandedRowKeys.value, record.id];
      } else {
        expandedRowKeys.value = expandedRowKeys.value.filter((key) => key !== record.id);
      }
    };

    // Functions

    const showCreateModal = () => {
      modalMode.value = 'create';
      bomForm.value = {
        name: '',
        version: 'v1.0',
        remark: '',
      };
      bomModalVisible.value = true;
    };

    const showUploadModal = () => {
      // Start BOM analysis simulation
      analysisModalVisible.value = true;
      analysisProgress.value = 0;
      currentAnalysisOperation.value = '初始化';
      currentAnalysisDetail.value = '准备解析BOM文件';

      // Simulate progress
      const interval = setInterval(() => {
        analysisProgress.value += 5;

        if (analysisProgress.value > 30 && analysisProgress.value <= 60) {
          currentAnalysisOperation.value = '物料解析中';
          currentAnalysisDetail.value = '正在识别BOM中的物料信息';
        } else if (analysisProgress.value > 60) {
          currentAnalysisOperation.value = '物料匹配中';
          currentAnalysisDetail.value = '正在匹配数据库中已有商品';
        }

        if (analysisProgress.value >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            analysisModalVisible.value = false;
          }, 1000);
        }
      }, 300);
    };

    const showLinkProjectModal = (record) => {
      currentBom.value = record;

      // Pre-select already linked projects
      selectedProjectKeys.value = record.projects ? record.projects.map((p) => p.id) : [];

      projectSearchValue.value = '';
      linkProjectModalVisible.value = true;
    };

    const onSelectProject = (selectedRowKeys) => {
      selectedProjectKeys.value = selectedRowKeys;
    };

    const confirmLinkProject = () => {
      if (!currentBom.value) return;

      // Update BOM with only in-progress projects
      const bomIndex = bomList.value.findIndex((b) => b.id === currentBom.value.id);
      if (bomIndex !== -1) {
        const selectedProjects = filteredInProgressProjects.value.filter((p) => selectedProjectKeys.value.includes(p.id));

        bomList.value[bomIndex].projects = selectedProjects.map((p) => ({
          id: p.id,
          name: p.name,
          stage: p.stage,
        }));
      }

      linkProjectModalVisible.value = false;
    };

    const showVersionModal = (record) => {
      currentBom.value = record;

      // Load versions for this BOM (simulated)
      const currentVersion = record.version;
      versions.value = [
        {
          id: '1',
          version: currentVersion,
          createTime: record.uploadTime,
          creator: record.uploader,
          description: '当前版本',
          isActive: true,
        },
        {
          id: '2',
          version: currentVersion,
          createTime: record.uploadTime,
          creator: record.uploader,
          description: '旧版本',
          isActive: false,
        },
      ];

      // Add previous versions if current is not v1.0
      if (currentVersion !== 'v1.0') {
        const major = parseInt(currentVersion.substring(1, 2));
        const minor = parseInt(currentVersion.substring(3));

        for (let i = minor - 1; i >= 0; i--) {
          versions.value.push({
            id: `${versions.value.length + 1}`,
            version: `v${major}.${i}`,
            createTime: '2024-05-01 10:00',
            creator: record.uploader,
            description: '历史版本',
            isActive: false,
          });
        }

        if (major > 1) {
          versions.value.push({
            id: `${versions.value.length + 1}`,
            version: 'v1.0',
            createTime: '2024-04-15 09:30',
            creator: record.uploader,
            description: '初始版本',
            isActive: false,
          });
        }
      }

      versionModalVisible.value = true;
    };

    const createNewVersion = () => {
      if (!currentBom.value) return;

      // Calculate new version number
      const currentVersion = currentBom.value.version;
      const major = parseInt(currentVersion.substring(1, 2));
      const minor = parseInt(currentVersion.substring(3));

      const newVersion = `v${major}.${minor + 1}`;

      // Add new version to list
      versions.value.forEach((v) => (v.isActive = false));

      versions.value.unshift({
        id: `${versions.value.length + 1}`,
        version: newVersion,
        createTime: new Date().toLocaleString(),
        creator: '当前用户',
        description: '新版本',
        isActive: true,
      });

      // Update BOM list
      const bomIndex = bomList.value.findIndex((b) => b.id === currentBom.value.id);
      if (bomIndex !== -1) {
        bomList.value[bomIndex].version = newVersion;
      }
    };

    const viewVersion = (version) => {
      // Populate version details for viewing
      viewingVersionDetails.value = {
        version: version.version,
        updateContent: version.description || '',
        items: currentBom.value?.materials || []
      };
      
      // Show material management modal in view-only mode
      bomItemsModalVisible.value = true;
      bomItemsForm.value = {
        items: [...viewingVersionDetails.value.items],
        version: viewingVersionDetails.value.version,
        updateContent: viewingVersionDetails.value.updateContent,
      };
      // Set flag to indicate view-only mode
      isViewOnly.value = true;
    };

    const activateVersion = (version) => {
      // Set selected version as active
      versions.value.forEach((v) => (v.isActive = false));
      version.isActive = true;

      // Update BOM list
      const bomIndex = bomList.value.findIndex((b) => b.id === currentBom.value.id);
      if (bomIndex !== -1) {
        bomList.value[bomIndex].version = version.version;
      }
    };

    const addBomItem = () => {
      const newItemId = `m${Date.now()}`;
      bomForm.value.items.push({
        id: newItemId,
        name: '',
        model: '',
        brand: '',
        category: '',
        quantity: 1,
        remark: '',
      });
    };

    const removeBomItem = (item) => {
      const index = bomForm.value.items.findIndex((i) => i.id === item.id);
      if (index !== -1) {
        bomForm.value.items.splice(index, 1);
      }
    };

    const showImportBomItemsModal = () => {
      uploadedFile.value = null;
      importBomItemsModalVisible.value = true;
    };

    const handleUploadChange = (info) => {
      if (info.file.status === 'done') {
        uploadedFile.value = info.file;
      } else {
        uploadedFile.value = null;
      }
    };

    const confirmImportBomItems = () => {
      if (!uploadedFile.value) return;

      // Simulate importing items
      // In a real implementation, this would parse the uploaded file
      const importedItems = [
        { id: `m${Date.now()}-1`, name: '伺服电机', model: 'MR-J4-10B', brand: '三菱', category: '电气元件', quantity: 2, remark: '' },
        { id: `m${Date.now()}-2`, name: '减速箱', model: 'R87F40', brand: 'SEW', category: '机械传动', quantity: 1, remark: '' },
        { id: `m${Date.now()}-3`, name: '传感器', model: 'E2E-X10M', brand: '欧姆龙', category: '传感器', quantity: 5, remark: '' },
      ];

      // Add imported items to the form
      bomForm.value.items = [...bomForm.value.items, ...importedItems];

      importBomItemsModalVisible.value = false;
    };

    const editBom = (record) => {
      modalMode.value = 'edit';
      currentBomId.value = record.id;

      bomForm.value = {
        name: record.name,
        version: record.version,
        remark: record.remark || '',
      };

      bomModalVisible.value = true;
    };

    const deleteBom = (record) => {
      // Confirm and delete BOM
      console.log('Delete BOM', record.id);
    };

    // Function to navigate to project detail
    const goToProjectDetail = (projectId) => {
      console.log('Navigate to project', projectId);
      // Implementation for navigation to project detail page
      // e.g. router.push(`/project/${projectId}`)
    };

    // Project stage styling function
    const getProjectStageClass = (stage) => {
      switch (stage) {
        case '未开始':
          return 'status-tag status-not-started';
        case '进行中':
          return 'status-tag status-in-progress';
        case '已关闭':
          return 'status-tag status-closed';
        default:
          return 'status-tag';
      }
    };

    // Material management functions
    const showMaterialManagement = (record) => {
      currentBomId.value = record.id;
      // Reset view-only flag
      isViewOnly.value = false;

      // Load existing materials from the BOM
      const bomIndex = bomList.value.findIndex((b) => b.id === record.id);
      if (bomIndex !== -1) {
        // Get current version and increment the minor version
        const currentVersion = bomList.value[bomIndex].version;
        const major = parseInt(currentVersion.substring(1, 2));
        const minor = parseInt(currentVersion.substring(3));
        const newVersion = `v${major}.${minor + 1}`;

        bomItemsForm.value = {
          items: bomList.value[bomIndex].materials ? [...bomList.value[bomIndex].materials] : [],
          version: newVersion,
          updateContent: '',
        };
      }

      bomItemsModalVisible.value = true;
    };

    // Material edit and delete functions
    const editMaterial = (material) => {
      // Find the BOM first by expanding the list
      let parentBomId = null;
      for (const bom of bomList.value) {
        if (bom.materials && bom.materials.some((m) => m.id === material.id)) {
          parentBomId = bom.id;
          break;
        }
      }

      if (parentBomId) {
        showMaterialManagement(bomList.value.find((b) => b.id === parentBomId));

        // You can also scroll to the specific material in the modal
        // or pre-select it for editing
      }
    };

    const deleteMaterial = (bomId, material) => {
      // Find the BOM
      const bomIndex = bomList.value.findIndex((b) => b.id === bomId);
      if (bomIndex !== -1 && bomList.value[bomIndex].materials) {
        // Filter out the material
        bomList.value[bomIndex].materials = bomList.value[bomIndex].materials.filter((m) => m.id !== material.id);
      }
    };

    // Save the materials in the BOM
    const saveBomItems = () => {
      // Find the BOM to update
      const bomIndex = bomList.value.findIndex((b) => b.id === currentBomId.value);
      if (bomIndex !== -1) {
        // Update the materials array
        bomList.value[bomIndex].materials = [...bomItemsForm.value.items];

        // Update the version
        bomList.value[bomIndex].version = bomItemsForm.value.version;

        // Update material counts
        const standardCount = bomItemsForm.value.items.filter((item) => item.category === '电子元器件' || item.category === '标准件').length;
        const customCount = bomItemsForm.value.items.filter((item) => item.category === '机械部件' || item.category === '非标件').length;
        const assemblyCount = bomItemsForm.value.items.filter((item) => item.category === '结构件' || item.category === '外购组件').length;

        bomList.value[bomIndex].standard = standardCount;
        bomList.value[bomIndex].custom = customCount;
        bomList.value[bomIndex].assembly = assemblyCount;
      }

      bomItemsModalVisible.value = false;
    };

    const saveBom = () => {
      // Validate basic BOM info
      if (!bomForm.value.name) {
        return; // Add validation error handling if needed
      }

      if (modalMode.value === 'create') {
        // Create a new BOM entry
        const newBomId = Date.now().toString();
        const newBom = {
          id: newBomId,
          name: bomForm.value.name,
          code: `BOM-${new Date().getFullYear()}-${String(bomList.value.length + 1).padStart(3, '0')}`,
          projects: [],
          status: 'parsed',
          standard: 0,
          custom: 0,
          assembly: 0,
          matchRate: 0,
          needConfirm: 0,
          uploadTime: new Date().toLocaleString(),
          uploader: '当前用户',
          version: bomForm.value.version,
          isLatest: true,
          remark: bomForm.value.remark,
          materials: [],
        };

        // Add to BOM list
        bomList.value.unshift(newBom);
      } else {
        // Update existing BOM - name and remark
        const bomIndex = bomList.value.findIndex((b) => b.id === currentBomId.value);
        if (bomIndex !== -1) {
          bomList.value[bomIndex].name = bomForm.value.name;
          bomList.value[bomIndex].remark = bomForm.value.remark;
        }
      }

      // Close modal
      bomModalVisible.value = false;
    };

    // Add isViewOnly flag for BOM items modal
    const isViewOnly = ref(false);
    
    // Add viewingVersionDetails for version viewing
    const viewingVersionDetails = ref({
      version: '',
      updateContent: '',
      items: []
    });

    return {
      // data
      bomList,
      columns,
      totalItems,
      currentPage,
      pageSize,
      linkProjectModalVisible,
      currentBom,
      projectSearchValue,
      projects,
      filteredInProgressProjects,
      selectedProjectKeys,
      projectColumns,
      projectPopoverColumns,
      bomModalVisible,
      modalMode,
      bomForm,
      bomItemColumns,
      materialsColumns,
      brandOptions,
      categoryOptions,
      analysisModalVisible,
      analysisProgress,
      currentAnalysisOperation,
      currentAnalysisDetail,
      versionModalVisible,
      versions,
      versionColumns,
      bomItemsModalVisible,
      importBomItemsModalVisible,
      uploadedFile,
      currentBomId,
      bomItemsForm,
      expandedRowKeys,
      isViewOnly,
      viewingVersionDetails,

      // computed
      getStatusColor,
      getStatusText,
      getTotalMaterials,
      getMatchStatus,
      getStageClass,
      getProjectStageClass,

      // methods
      showCreateModal,
      showUploadModal,
      showLinkProjectModal,
      onSelectProject,
      confirmLinkProject,
      showVersionModal,
      createNewVersion,
      viewVersion,
      activateVersion,
      addBomItem,
      removeBomItem,
      saveBom,
      showImportBomItemsModal,
      handleUploadChange,
      confirmImportBomItems,
      saveBomItems,
      editBom,
      deleteBom,
      goToProjectDetail,
      onExpand,
      showMaterialManagement,
      editMaterial,
      deleteMaterial,
    };
  },
});
</script>

<style scoped>
.search-filter-area {
  margin-bottom: 16px;
}

.action-buttons {
  margin-bottom: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.mr-8 {
  margin-right: 8px;
}

.my-4 {
  margin-top: 16px;
  margin-bottom: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-5 {
  margin-bottom: 20px;
}

.mt-6 {
  margin-top: 24px;
}

.mt-1 {
  margin-top: 4px;
}

.text-center {
  text-align: center;
}

.text-primary {
  color: #f94c30;
}

.text-error {
  color: #f5222d;
}

.text-gray-500 {
  color: #888;
}

.text-xs {
  font-size: 12px;
}

.font-bold {
  font-weight: bold;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.p-0 {
  padding: 0;
}

.status-tag {
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
}

.status-need-confirm {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-selecting {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-contracted {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-producing {
  background-color: #e6fffb;
  color: #13c2c2;
}

.status-accepting {
  background-color: #f9f0ff;
  color: #722ed1;
}

.project-item {
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-not-started {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status-in-progress {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-closed {
  background-color: #f9f0ff;
  color: #722ed1;
  border: 1px solid #d3adf7;
}
</style>
