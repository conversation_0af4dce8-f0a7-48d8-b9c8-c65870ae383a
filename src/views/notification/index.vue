<template>
  <div class="notification-container">
    <!-- 搜索筛选区域 -->
    <div class="search-area">
      <a-form  :model="filterForm">
        <a-row :gutter="16">
          <a-col :span="4">
            <a-form-item label="消息类型">
              <a-select 
                v-model:value="filterForm.module" 
                placeholder="请选择消息类型" 
                style="width: 100%"
                allowClear
                @change="handleFilter"
              >
                <a-select-option value="rfq">询价管理</a-select-option>
                <a-select-option value="purchase">采购管理</a-select-option>
                <a-select-option value="delivery">收货管理</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :span="4">
            <a-form-item label="阅读状态">
              <a-select 
                v-model:value="filterForm.readStatus" 
                placeholder="请选择阅读状态" 
                style="width: 100%"
                allowClear
                @change="handleFilter"
              >
                <a-select-option value="unread">未读</a-select-option>
                <a-select-option value="read">已读</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="10">
            <a-form-item label="时间范围">
              <a-range-picker 
                v-model:value="filterForm.dateRange" 
                format="YYYY-MM-DD"
                style="width: 100%"
                @change="handleFilter"
              />
            </a-form-item>
          </a-col>

          <a-col :span="6">
            <a-space>
              <a-button type="primary" @click="handleFilter">
                <SearchOutlined />
                查询
              </a-button>
              <a-button @click="handleReset">
                <ReloadOutlined />
                重置
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作区域 -->
    <div class="action-area">
      <div class="stats-info">
        <a-space size="large">
          <span class="stat-item">
            <BellOutlined style="color: #ff4d4f; margin-right: 4px;" />
            未读: <strong>{{ unreadCount }}</strong>
          </span>
          <span class="stat-item">
            <CheckCircleOutlined style="color: #52c41a; margin-right: 4px;" />
            已读: <strong>{{ readCount }}</strong>
          </span>
          <span class="stat-item">
            <InfoCircleOutlined style="color: #1890ff; margin-right: 4px;" />
            总计: <strong>{{ totalCount }}</strong>
          </span>
        </a-space>
      </div>
      <div class="action-buttons">
        <a-space>
          <a-button @click="markAllAsRead" type="primary" :disabled="unreadCount === 0" size="small">
            <CheckOutlined />
            全部已读
          </a-button>
          <a-button @click="clearAllRead" type="primary" :disabled="readCount === 0" size="small">
            <DeleteOutlined />
            清空已读
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="message-list">
      <a-spin :spinning="loading">
        <div class="message-wrapper">
          <div 
            v-for="message in paginatedMessages" 
            :key="message.id"
            class="message-item"
            :class="{ 'unread': !message.isRead }"
            @click="handleMessageClick(message)"
          >
            <div class="message-header">
              <div class="message-info">
                <a-tag :color="getModuleColor(message.module)" size="small">
                  {{ getModuleLabel(message.module) }}
                </a-tag>
                <span class="message-title">{{ message.title }}</span>
                <a-badge v-if="!message.isRead" status="processing" />
              </div>
              <div class="message-time">{{ formatTime(message.createTime) }}</div>
            </div>
            
            <div class="message-content-actions">
              <div class="message-content">
                <div class="message-text" v-html="message.content"></div>
              </div>
              
              <div class="message-actions" @click.stop>
                <a-space size="small">
                  <a-button 
                    v-if="message.actionUrl" 
                    type="link" 
                    size="small"
                    @click="handleViewDetail(message)"
                  >
                    <EyeOutlined />
                    查看
                  </a-button>
                  <a-button 
                    v-if="!message.isRead" 
                    type="link" 
                    size="small"
                    @click="markAsRead(message)"
                  >
                    <CheckOutlined />
                    已读
                  </a-button>
                  <a-button 
                    type="link" 
                    size="small" 
                    danger
                    @click="deleteMessage(message)"
                  >
                    <DeleteOutlined />
                    删除
                  </a-button>
                </a-space>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <a-empty 
            v-if="filteredMessages.length === 0 && !loading"
            description="暂无消息"
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          />
        </div>
      </a-spin>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="filteredMessages.length > 0">
        <a-pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
          @change="handlePageChange"
          @showSizeChange="handlePageSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal, Empty } from 'ant-design-vue';
import { 
  BellOutlined, 
  CheckOutlined, 
  DeleteOutlined, 
  SearchOutlined, 
  ReloadOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue';

const router = useRouter();

// 响应式数据
const loading = ref(false);
const messages = ref([]);

// 筛选表单
const filterForm = reactive({
  module: undefined,
  readStatus: undefined,
  dateRange: []
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 15,
  total: 0
});

// 计算属性
const unreadCount = computed(() => messages.value.filter(msg => !msg.isRead).length);
const readCount = computed(() => messages.value.filter(msg => msg.isRead).length);
const totalCount = computed(() => messages.value.length);

// 筛选后的消息
const filteredMessages = computed(() => {
  let filtered = [...messages.value];
  
  // 按模块筛选
  if (filterForm.module) {
    filtered = filtered.filter(msg => msg.module === filterForm.module);
  }
  
  // 按阅读状态筛选
  if (filterForm.readStatus) {
    const isRead = filterForm.readStatus === 'read';
    filtered = filtered.filter(msg => msg.isRead === isRead);
  }
  
  // 按时间范围筛选
  if (filterForm.dateRange && filterForm.dateRange.length === 2) {
    const [start, end] = filterForm.dateRange;
    filtered = filtered.filter(msg => {
      const msgDate = new Date(msg.createTime);
      return msgDate >= start && msgDate <= end;
    });
  }
  
  // 按时间倒序排列
  filtered.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
  
  pagination.total = filtered.length;
  return filtered;
});

// 分页后的消息
const paginatedMessages = computed(() => {
  const startIndex = (pagination.current - 1) * pagination.pageSize;
  const endIndex = startIndex + pagination.pageSize;
  return filteredMessages.value.slice(startIndex, endIndex);
});

// 模拟消息数据
const mockMessages = [
  {
    id: 1,
    module: 'rfq',
    title: '询价单已截止',
    content: '您发起的询价单<strong>RFQ-2024-001</strong>已截止。',
    createTime: '2024-01-15 14:30:00',
    isRead: false,
    actionUrl: '/purchase/rfq/detail/RFQ-2024-001',
    relatedId: 'RFQ-2024-001'
  },
  {
    id: 2,
    module: 'purchase',
    title: '采购订单合同已生成',
    content: '您发起的采购订单<strong>SO-2024-002</strong>合同已生成。',
    createTime: '2024-01-15 10:15:00',
    isRead: false,
    actionUrl: '/purchase/poDetail?soNo=SO-2024-002',
    relatedId: 'SO-2024-002'
  },
  {
    id: 3,
    module: 'purchase',
    title: '采购订单已确认',
    content: '您发起的采购订单<strong>SO-2024-003</strong>已确认。',
    createTime: '2024-01-14 16:45:00',
    isRead: true,
    actionUrl: '/purchase/poDetail?soNo=SO-2024-003',
    relatedId: 'SO-2024-003'
  },
  {
    id: 4,
    module: 'delivery',
    title: '新的送货单生成',
    content: '您有一份新的送货单<strong>DN-2024-004</strong>。',
    createTime: '2024-01-14 09:20:00',
    isRead: false,
    actionUrl: '/purchase/dnDetail?dnNo=DN-2024-004',
    relatedId: 'DN-2024-004'
  },
  {
    id: 5,
    module: 'delivery',
    title: '送货单即将自动收货',
    content: '您的送货单<strong>DN-2024-005</strong>即将自动收货，请及时处理。',
    createTime: '2024-01-13 18:30:00',
    isRead: false,
    actionUrl: '/purchase/dnDetail?dnNo=DN-2024-005',
    relatedId: 'DN-2024-005'
  },
  {
    id: 6,
    module: 'delivery',
    title: '送货单已自动收货',
    content: '您的送货单<strong>DN-2024-006</strong>已自动收货。',
    createTime: '2024-01-13 12:00:00',
    isRead: true,
    actionUrl: '/purchase/dnDetail?dnNo=DN-2024-006',
    relatedId: 'DN-2024-006'
  },
  {
    id: 7,
    module: 'purchase',
    title: '采购订单已完成',
    content: '您发起的采购订单<strong>SO-2024-007</strong>已完成。',
    createTime: '2024-01-12 15:20:00',
    isRead: true,
    actionUrl: '/purchase/poDetail?soNo=SO-2024-007',
    relatedId: 'SO-2024-007'
  },
  {
    id: 8,
    module: 'purchase',
    title: '采购订单已取消',
    content: '您发起的采购订单<strong>SO-2024-008</strong>已取消。',
    createTime: '2024-01-12 11:10:00',
    isRead: true,
    actionUrl: '/purchase/poDetail?soNo=SO-2024-008',
    relatedId: 'SO-2024-008'
  },
  {
    id: 9,
    module: 'rfq',
    title: '询价单已过期',
    content: '您发起的询价单<strong>RFQ-2024-009</strong>已过期。',
    createTime: '2024-01-11 09:30:00',
    isRead: false,
    actionUrl: '/purchase/rfq/detail/RFQ-2024-009',
    relatedId: 'RFQ-2024-009'
  },
  {
    id: 10,
    module: 'delivery',
    title: '送货异常处理',
    content: '您的送货单<strong>DN-2024-010</strong>运输异常，请联系供应商。',
    createTime: '2024-01-10 14:45:00',
    isRead: false,
    actionUrl: '/purchase/dnDetail?dnNo=DN-2024-010',
    relatedId: 'DN-2024-010'
  },
  {
    id: 11,
    module: 'purchase',
    title: '采购订单部分确认',
    content: '您发起的采购订单<strong>SO-2024-011</strong>已部分确认。',
    createTime: '2024-01-10 11:20:00',
    isRead: true,
    actionUrl: '/purchase/poDetail?soNo=SO-2024-011',
    relatedId: 'SO-2024-011'
  },
  {
    id: 12,
    module: 'delivery',
    title: '收货确认通知',
    content: '您的送货单<strong>DN-2024-012</strong>已完成收货确认。',
    createTime: '2024-01-09 16:10:00',
    isRead: true,
    actionUrl: '/purchase/dnDetail?dnNo=DN-2024-012',
    relatedId: 'DN-2024-012'
  },
  {
    id: 13,
    module: 'delivery',
    title: '系统维护通知',
    content: '系统将于今晚22:00-23:00进行维护，期间可能影响正常使用。',
    createTime: '2024-01-08 15:30:00',
    isRead: false,
    relatedId: null
  }
];

// 方法
const getModuleLabel = (module) => {
  const labels = {
    rfq: '询价管理',
    purchase: '采购管理',
    delivery: '收货管理'
  };
  return labels[module] || '未知';
};

const getModuleColor = (module) => {
  const colors = {
    rfq: 'blue',
    purchase: 'orange', 
    delivery: 'green'
  };
  return colors[module] || 'default';
};

const formatTime = (timeStr) => {
  const now = new Date();
  const time = new Date(timeStr);
  const diff = now - time;
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  
  return timeStr.split(' ')[0];
};

const handleFilter = () => {
  pagination.current = 1;
};

const handleReset = () => {
  filterForm.module = undefined;
  filterForm.readStatus = undefined;
  filterForm.dateRange = [];
  pagination.current = 1;
};

const handleMessageClick = (msg) => {
  if (!msg.isRead) {
    markAsRead(msg);
  }
};

const markAsRead = (msg) => {
  msg.isRead = true;
  message.success('已标记为已读');
};

const markAllAsRead = () => {
  Modal.confirm({
    title: '确认操作',
    content: '确定要将所有未读消息标记为已读吗？',
    onOk() {
      messages.value.forEach(msg => {
        if (!msg.isRead) {
          msg.isRead = true;
        }
      });
      message.success('所有消息已标记为已读');
    }
  });
};

const clearAllRead = () => {
  Modal.confirm({
    title: '确认操作',
    content: '确定要清空所有已读消息吗？此操作不可恢复。',
    onOk() {
      messages.value = messages.value.filter(msg => !msg.isRead);
      message.success('已清空所有已读消息');
      pagination.current = 1;
    }
  });
};

const deleteMessage = (msg) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条消息吗？',
    onOk() {
      const index = messages.value.findIndex(m => m.id === msg.id);
      if (index > -1) {
        messages.value.splice(index, 1);
        message.success('消息已删除');
      }
    }
  });
};

const handleViewDetail = (msg) => {
  if (msg.actionUrl) {
    router.push(msg.actionUrl);
  }
};

const handlePageChange = (page, pageSize) => {
  pagination.current = page;
  pagination.pageSize = pageSize;
};

const handlePageSizeChange = (current, size) => {
  pagination.current = 1;
  pagination.pageSize = size;
};

// 初始化
onMounted(() => {
  loading.value = true;
  setTimeout(() => {
    messages.value = [...mockMessages];
    loading.value = false;
  }, 500);
});
</script>

<style scoped>
.notification-container {
  min-height: 100vh;
}

.search-area {
  background: white;
  margin-bottom: 16px;
}

.action-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 12px 16px;
  border: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.stats-info {
  flex: 1;
}

.stat-item {
  color: #666;
  font-size: 14px;
}

.action-buttons {
  flex-shrink: 0;
}

.message-list {
  background: white;
  border-radius: 2px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.message-wrapper {
  min-height: 400px;
}

.message-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.message-item:last-child {
  border-bottom: none;
}

.message-item:hover {
  background-color: #fafafa;
}

.message-item.unread {
  background-color: #fff7e6;
  border-left: 3px solid #f94c30;
}

.message-item.unread:hover {
  background-color: #fff2e6;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.message-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.message-title {
  font-weight: 500;
  color: #1a1a2b;
  font-size: 14px;
}

.message-time {
  color: #8c8c8c;
  font-size: 12px;
  flex-shrink: 0;
}

.message-content-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.message-content {
  flex: 1;
}

.message-text {
  color: #4a4a4a;
  line-height: 1.5;
  font-size: 13px;
}

.message-text :deep(strong) {
  color: #f94c30;
  font-weight: 600;
}

.message-text :deep(a) {
  color: #f94c30;
  text-decoration: underline;
}

.message-actions {
  display: flex;
  justify-content: flex-end;
  flex-shrink: 0;
}

.pagination-wrapper {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .action-area {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .stats-info {
    text-align: center;
  }
  
  .action-buttons {
    text-align: center;
  }
  
  .message-content-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .message-actions {
    align-self: flex-end;
  }
}
</style>
