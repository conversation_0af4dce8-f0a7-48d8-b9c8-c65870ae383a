<template>
  <div class="payment-list-container">
    <div :bordered="false">
      <a-form layout="inline" :model="searchParams" @finish="fetchData">
        <a-form-item label="付款单号">
          <a-input v-model:value="searchParams.paymentNumber" placeholder="请输入付款单号" allow-clear />
        </a-form-item>
        <a-form-item label="状态">
          <a-select v-model:value="searchParams.status" placeholder="请选择状态" style="width: 150px" allow-clear>
            <a-select-option value="pending">待支付</a-select-option>
            <a-select-option value="partial">部分支付</a-select-option>
            <a-select-option value="paid">已支付</a-select-option>
            <a-select-option value="overdue">已逾期</a-select-option>
            <a-select-option value="cancelled">已取消</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="所属对账单">
          <a-input v-model:value="searchParams.statementNumber" placeholder="请输入对账单号" allow-clear />
        </a-form-item>
        <a-form-item label="创建时间">
          <a-range-picker v-model:value="searchParams.createTimeRange" />
        </a-form-item>
        <a-form-item label="付款逾期时间">
          <a-range-picker v-model:value="searchParams.paymentDeadlineRange" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit">查询</a-button>
          <a-button style="margin-left: 8px" @click="resetSearch">重置</a-button>
        </a-form-item>
      </a-form>

      <div style="display: flex; justify-content: space-between; align-items: center;">
        <div>
          <!-- <a-button type="primary" style="margin-right: 8px" @click="handleCreate">新建付款单</a-button> -->
          <a-button @click="handleExport">导出</a-button>
        </div>
        <div>
          <a-button @click="showColumnConfig">列配置</a-button>
        </div>
      </div>
      <a-table
        :columns="visibleColumns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
        style="margin-top: 16px"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'paymentNumber'">
            <a href="javascript:void(0)" @click="handleView(record as PaymentRecord)">
              {{ record.paymentNumber }}
            </a>
          </template>
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'statementNumber'">
            <a href="javascript:void(0)" @click="handleStatementView(record.statementNumber)" >
              {{ record.statementNumber }}
            </a>
          </template>
          <template v-if="column.key === 'totalPayable' || column.key === 'totalPaid' || column.key === 'totalPending'">
            {{ formatCurrency(record[column.key]) }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a v-if="record.status === 'pending' || record.status === 'partial'" @click="handlePayment(record as PaymentRecord)">付款</a>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- Column Configuration Drawer -->
    <a-drawer
      v-model:open="columnConfigVisible"
      title="列配置"
      placement="right"
      width="400"
      @close="handleColumnConfigCancel"
    >
      <a-checkbox-group v-model:value="selectedColumns" style="width: 100%">
        <div style="display: flex; flex-direction: column; gap: 12px;">
          <a-checkbox
            v-for="column in allColumns"
            :key="column.key"
            :value="column.key"
            :disabled="column.key === 'paymentNumber' || column.key === 'action'"
          >
            {{ column.title }}
          </a-checkbox>
        </div>
      </a-checkbox-group>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { 
  Table as ATable, 
  Button as AButton, 
  Card as ACard, 
  Form as AForm, 
  FormItem as AFormItem, 
  Input as AInput, 
  Select as ASelect, 
  SelectOption as ASelectOption, 
  DatePicker as ADatePicker, 
  Space as ASpace, 
  Tag as ATag, 
  Drawer as ADrawer,
  Checkbox as ACheckbox,
  CheckboxGroup as ACheckboxGroup,
  message, 
  PageHeader as APageHeader 
} from 'ant-design-vue';
import type { TableProps, TablePaginationConfig } from 'ant-design-vue';
import type { Dayjs } from 'dayjs'; // Import Dayjs if you're using it with date pickers

const ARangePicker = ADatePicker.RangePicker;

interface PaymentRecord {
  id: string;
  paymentNumber: string;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled' | 'partial';
  totalPayable: number;
  totalPaid: number;
  totalPending: number;
  statementNumber: string;
  createTime: string;
  paymentCompletionTime: string;
  paymentDeadline: string;
  paymentMethod: string;
  paymentTerms: string;
}

interface SearchParams {
  paymentNumber: string;
  statementNumber: string;
  status?: 'pending' | 'paid' | 'overdue' | 'cancelled' | 'partial';
  createTimeRange?: [Dayjs, Dayjs] | [string, string];
  paymentDeadlineRange?: [Dayjs, Dayjs] | [string, string];
}

const router = useRouter();
const loading = ref<boolean>(false);
const dataSource = ref<PaymentRecord[]>([]);
const searchParams = reactive<SearchParams>({
  paymentNumber: '',
  statementNumber: '',
  status: undefined,
  createTimeRange: undefined,
  paymentDeadlineRange: undefined,
});

// Mock pagination object
const pagination = reactive<TablePaginationConfig>({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共 ${total} 条`,
});

// Column configuration
const columnConfigVisible = ref<boolean>(false);
const selectedColumns = ref<string[]>([
  'paymentNumber', 'status', 'totalPayable', 'totalPaid', 'totalPending', 
  'statementNumber', 'createTime', 'paymentCompletionTime', 'paymentDeadline', 
  'paymentMethod', 'paymentTerms', 'action'
]);

const allColumns: TableProps['columns'] = [
  { title: '付款单号', dataIndex: 'paymentNumber', key: 'paymentNumber', width: 150 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '应付总额', dataIndex: 'totalPayable', key: 'totalPayable', align: 'right', width: 120 },
  { title: '实付总额', dataIndex: 'totalPaid', key: 'totalPaid', align: 'right', width: 120 },
  { title: '待付总额', dataIndex: 'totalPending', key: 'totalPending', align: 'right', width: 120 },
  { title: '所属对账单', dataIndex: 'statementNumber', key: 'statementNumber', width: 150 },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime', width: 160 },
  { title: '付款完成时间', dataIndex: 'paymentCompletionTime', key: 'paymentCompletionTime', width: 160 },
  { title: '付款逾期时间', dataIndex: 'paymentDeadline', key: 'paymentDeadline', width: 160 },
  { title: '付款方式', dataIndex: 'paymentMethod', key: 'paymentMethod', width: 120 },
  { title: '付款条件', dataIndex: 'paymentTerms', key: 'paymentTerms', width: 120 },
  { title: '操作', key: 'action', width: 100, fixed: 'right' },
];

const visibleColumns = computed(() => {
  return allColumns.filter(column => selectedColumns.value.includes(column.key as string));
});

// Generate payment number based on timestamp
const generatePaymentNumber = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const second = String(now.getSeconds()).padStart(2, '0');
  const timestamp = `${year}${month}${day}${hour}${minute}${second}`;
  return `PAY${timestamp}`;
};

// --- Mock Data & Fetching ---
const mockData: PaymentRecord[] = Array.from({ length: 55 }, (_, i) => {
  const baseDate = new Date(2024, Math.floor(i / 10), (i % 28) + 1, Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), Math.floor(Math.random() * 60));
  const paymentNumber = `PAY${baseDate.getFullYear()}${String(baseDate.getMonth() + 1).padStart(2, '0')}${String(baseDate.getDate()).padStart(2, '0')}${String(baseDate.getHours()).padStart(2, '0')}${String(baseDate.getMinutes()).padStart(2, '0')}${String(baseDate.getSeconds()).padStart(2, '0')}`;
  
  // Generate statement number with INV prefix and timestamp
  const statementDate = new Date(baseDate.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000); // 30 days before payment
  const statementNumber = `INV${statementDate.getFullYear()}${String(statementDate.getMonth() + 1).padStart(2, '0')}${String(statementDate.getDate()).padStart(2, '0')}${String(statementDate.getHours()).padStart(2, '0')}${String(statementDate.getMinutes()).padStart(2, '0')}${String(statementDate.getSeconds()).padStart(2, '0')}`;
  
  const totalPayable = Math.random() * 50000 + 10000;
  const totalPaid = Math.random() * totalPayable;
  const totalPending = totalPayable - totalPaid;
  
  const status = ['pending', 'paid', 'overdue', 'cancelled', 'partial'][i % 5] as 'pending' | 'paid' | 'overdue' | 'cancelled' | 'partial';
  
  // Updated payment methods and terms
  const paymentMethods = ['现金/电汇', '银行承兑'];
  const paymentTermsList = ['预付款', '账期结算，30天', '账期结算，60天', '账期结算，90天'];
  const paymentMethod = paymentMethods[i % 2];
  
  return {
    id: paymentNumber,
    paymentNumber,
    status,
    totalPayable,
    totalPaid: status === 'paid' ? totalPayable : totalPaid,
    totalPending: status === 'paid' ? 0 : totalPending,
    statementNumber,
    createTime: baseDate.toLocaleString('zh-CN'),
    paymentCompletionTime: status === 'paid' ? new Date(baseDate.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000).toLocaleString('zh-CN') : '',
    paymentDeadline: paymentMethod === '现金/电汇' ? '' : new Date(baseDate.getTime() + 30 * 24 * 60 * 60 * 1000).toLocaleString('zh-CN'),
    paymentMethod,
    paymentTerms: paymentTermsList[i % 4],
  };
});

const fetchData = async (params: any = {}) => {
  loading.value = true;
  console.log('Fetching data with params:', {
      page: pagination.current,
      pageSize: pagination.pageSize,
      search: searchParams,
      ...params, // Include sort/filter params from table change event
  });
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 500));

  // Simple filtering simulation (replace with actual API call)
  const filteredData = mockData.filter(item => {
    const paymentNumberMatch = !searchParams.paymentNumber || item.paymentNumber.includes(searchParams.paymentNumber);
    const statementNumberMatch = !searchParams.statementNumber || item.statementNumber.includes(searchParams.statementNumber);
    const statusMatch = !searchParams.status || item.status === searchParams.status;
    // Add date range filtering logic here if searchParams.createTimeRange or paymentDeadlineRange is set
    return paymentNumberMatch && statementNumberMatch && statusMatch;
  });

  const start = ((pagination.current ?? 1) - 1) * (pagination.pageSize ?? 10);
  const end = start + (pagination.pageSize ?? 10);
  dataSource.value = filteredData.slice(start, end);
  pagination.total = filteredData.length;
  loading.value = false;
};

const handleTableChange: TableProps['onChange'] = (
  pag,
  filters,
  sorter
) => {
  console.log('Table change:', pag, filters, sorter);
  // Update pagination state
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  // Pass filters and sorter to fetchData if your API supports it
  fetchData({
    // sortField: sorter.field,
    // sortOrder: sorter.order,
    // ...filters,
  });
};

const resetSearch = () => {
  searchParams.paymentNumber = '';
  searchParams.statementNumber = '';
  searchParams.status = undefined;
  searchParams.createTimeRange = undefined;
  searchParams.paymentDeadlineRange = undefined;
  pagination.current = 1; // Reset to first page
  fetchData();
};

const getStatusText = (status: string): string => {
  switch (status) {
    case 'pending': return '待支付';
    case 'partial': return '部分支付';
    case 'paid': return '已支付';
    case 'overdue': return '已逾期';
    case 'cancelled': return '已取消';
    default: return '未知';
  }
};

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'pending': return 'orange';
    case 'partial': return 'blue';
    case 'paid': return 'green';
    case 'overdue': return 'red';
    case 'cancelled': return 'default';
    default: return 'default';
  }
};

const formatCurrency = (value: number): string => {
  return value.toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' });
};

// --- Action Handlers ---
const handleCreate = () => {
  // Navigate to the creation page or open a modal
  // router.push('/purchase/payment/create'); // Example navigation
  message.info('跳转到新建付款单页面 (功能待实现)');
};

const handleView = (record: PaymentRecord) => {
  // Navigate to the detail page
  router.push(`/purchase/payDetail?id=${record.id}`); // Example navigation
  
};

const handleStatementView = (statementNumber: string) => {
  // Navigate to the statement detail page
  message.info(`查看对账单：${statementNumber}`);
  // router.push(`/purchase/statement?id=${statementNumber}`);
};

const handleExport = () => {
  // Implement export logic (e.g., generate CSV/Excel)
  message.info('导出功能待实现');
  console.log('Exporting data with current filters:', searchParams);
  // You might want to fetch all filtered data (not just the current page)
  // const allFilteredData = mockData.filter(...) // Apply filters as in fetchData
  // Then convert allFilteredData to CSV/Excel
};

const handlePayment = (record: PaymentRecord) => {
  // Navigate to the payment page or open payment modal
  message.info(`处理付款：${record.paymentNumber}`);
  // router.push(`/purchase/payment/pay?id=${record.id}`);
};

// --- Column Configuration ---
const showColumnConfig = () => {
  columnConfigVisible.value = true;
};

const handleColumnConfigOk = () => {
  // Ensure required columns are always selected
  if (!selectedColumns.value.includes('paymentNumber')) {
    selectedColumns.value.push('paymentNumber');
  }
  if (!selectedColumns.value.includes('action')) {
    selectedColumns.value.push('action');
  }
  columnConfigVisible.value = false;
  message.success('列配置已更新');
};

const handleColumnConfigCancel = () => {
  columnConfigVisible.value = false;
};

// --- Lifecycle ---
onMounted(() => {
  fetchData();
});
</script>

<style scoped>

/* Add custom styles if needed */
.ant-form-inline .ant-form-item {
  margin-bottom: 16px; /* Add some spacing between filter items */
}
</style>

