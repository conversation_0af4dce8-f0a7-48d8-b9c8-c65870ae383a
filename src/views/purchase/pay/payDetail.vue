<template>
  <div class="payment-detail-container">
    <a-page-header :title="'付款单详情 - ' + paymentData.paymentNo" @back="goBack">
      <template #tags>
        <a-tag :color="getStatusColor(paymentData.status)">{{ getStatusText(paymentData.status) }}</a-tag>
      </template>
      <!-- <template #extra>
        <a-space>
          <a-button type="primary" v-if="canMarkAsPaid" @click="showUploadAttachmentModal">标记为已支付</a-button>
          <a-button type="primary" v-if="canEdit">编辑</a-button>
          <a-button v-if="canCancel" danger>取消付款单</a-button>
          <a-button @click="handlePrint">打印</a-button>
          <a-button @click="handleExport">导出</a-button>
        </a-space>
      </template> -->
      <!-- Header description removed -->
    </a-page-header>

    <!-- 付款提示与选择 -->
    <a-card v-if="showPaymentPrompt" class="detail-card payment-prompt-card">
      <div class="payment-prompt">
        <a-alert type="warning" show-icon>
          <template #message>
            <div class="payment-countdown">
              <span class="countdown-label">付款提醒：</span>
              <span :class="countdownClass" v-html="formatCountdownText"></span>
            </div>
          </template>
          <template #description> 请尽快完成剩余 <span style="font-size: 20px; color: #f94c30; font-weight: bold;margin: 0 4px;">{{ formatCurrency(paymentData.dueAmount) }} </span> 的支付。逾期可能会影响您的信用记录及后续合作。 </template>
        </a-alert>

        <div class="payment-method-select">
          <span class="method-label">选择支付方式：</span>
          <a-radio-group v-model:value="selectedPaymentMethod" button-style="solid">
            <a-radio-button value="offline"> <bank-outlined /> 线下打款 </a-radio-button>
            <a-radio-button value="online"> <credit-card-outlined /> 线上网银转账 </a-radio-button>
          </a-radio-group>

          <div class="payment-info" v-if="selectedPaymentMethod === 'offline'">
            <div class="payment-info-flex">
              <div class="payment-info-section">
                <p><strong>线下打款说明：</strong></p>
                <div class="payment-detail-item">
                  <span class="label">账户名称：</span>
                  <span class="value">{{ paymentData.supplier.name }}</span>
                  <a-button type="link" size="small" @click="copyToClipboard(paymentData.supplier.name)">
                    <copy-outlined />
                  </a-button>
                </div>
                <div class="payment-detail-item">
                  <span class="label">开户银行：</span>
                  <span class="value">{{ paymentData.payeeBankName || '招商银行 深圳支行' }}</span>
                  <a-button type="link" size="small" @click="copyToClipboard(paymentData.payeeBankName || '招商银行 深圳支行')">
                    <copy-outlined />
                  </a-button>
                </div>
                <div class="payment-detail-item">
                  <span class="label">银行账号：</span>
                  <span class="value">{{ paymentData.payeeBankAccount || '6214 **** **** 5678' }}</span>
                  <a-button type="link" size="small" @click="copyToClipboard(paymentData.payeeBankAccount || '6214 **** **** 5678')">
                    <copy-outlined />
                  </a-button>
                </div>
                <div class="payment-detail-item">
                  <span class="label">税号：</span>
                  <span class="value">{{ paymentData.supplier.taxId || '91320594MA1XFAYU2Y' }}</span>
                  <a-button type="link" size="small" @click="copyToClipboard(paymentData.supplier.taxId || '91320594MA1XFAYU2Y')">
                    <copy-outlined />
                  </a-button>
                </div>
                <div class="payment-detail-item">
                  <span class="label">银联号：</span>
                  <span class="value">{{ paymentData.payeeBankCode || '************' }}</span>
                  <a-button type="link" size="small" @click="copyToClipboard(paymentData.payeeBankCode || '************')">
                    <copy-outlined />
                  </a-button>
                </div>
                <div class="payment-detail-item">
                  <span class="label">开户行地址：</span>
                  <span class="value">{{ paymentData.payeeBankAddress || '江苏省苏州市工业园区苏州大道东278号' }}</span>
                  <a-button type="link" size="small" @click="copyToClipboard(paymentData.payeeBankAddress || '江苏省苏州市工业园区苏州大道东278号')">
                    <copy-outlined />
                  </a-button>
                </div>
                <p class="note">*请使用公司对公账户进行转账</p>
              </div>
              <div class="payment-info-section payment-tip">
                <div class="tip-header"><info-circle-outlined /> <strong>注意事项</strong></div>
                <p>请确保转账时：</p>
                <ul>
                  <li>使用公司对公账户转账</li>
                  <li>转账备注中注明付款单号</li>
                  <li>保留银行转账凭证</li>
                  <li>转账完成后及时将转账凭证作为附件上传</li>
                </ul>
                <p>以确保款项能及时核销入账</p>
              </div>
            </div>
          </div>

          <div class="payment-info" v-else-if="selectedPaymentMethod === 'online'">
            <p><strong>线上转账将跳转到第三方支付平台</strong></p>
            <p class="note">*确认金额无误后，点击下方按钮前往支付</p>
            <div class="buttons">
              <a-button type="primary" @click="handleOnlinePayment">立即支付 {{ formatCurrency(paymentData.dueAmount) }}</a-button>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 基本信息 -->
    <a-card title="基本信息" class="detail-card">
      <a-descriptions :column="3" bordered size="middle">
        <a-descriptions-item label="付款单号">{{ paymentData.paymentNo }}</a-descriptions-item>
        <a-descriptions-item label="付款方式">{{ paymentData.paymentMethod }}</a-descriptions-item>
        <a-descriptions-item label="付款条款">{{ paymentData.paymentTerms }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ formatDateTime(paymentData.createTime) }}</a-descriptions-item>
        <a-descriptions-item label="所属对账单">
          <a @click="gotoStatementDetail(paymentData.statementId)">{{ paymentData.statementNo || '-' }}</a>
        </a-descriptions-item>
        <a-descriptions-item label="付款完成日期">{{ formatDate(paymentData.paymentDate) }}</a-descriptions-item>
        <a-descriptions-item label="应付总额">
          <span class="currency">{{ formatCurrency(paymentData.totalAmount) }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="实付总额">
          <span class="currency paid">{{ formatCurrency(paymentData.paidAmount) }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="待付总额">
          <span class="currency due">{{ formatCurrency(paymentData.dueAmount) }}</span>
        </a-descriptions-item>
        <!-- <a-descriptions-item label="币种">{{ paymentData.currency }}</a-descriptions-item> -->
      </a-descriptions>
    </a-card>

    <!-- 物料信息 -->
    <a-card title="物料信息" class="detail-card">
      <a-table :columns="materialColumns" :data-source="paymentData.materials || []" :pagination="false" size="middle" row-key="id">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'price' || column.dataIndex === 'totalAmount'">
            {{ formatCurrency(record[column.dataIndex]) }}
          </template>
          <template v-if="column.dataIndex === 'poNo'">
            <a @click="gotoPoDetail(record.poId)">{{ record.poNo }}</a>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 关联采购订单 -->
    <!-- <a-card title="关联采购订单" class="detail-card">
      <a-table :columns="poColumns" :data-source="paymentData.relatedPOs" :pagination="false" size="middle" row-key="poId">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'soNo'">
            <a @click="gotoPoDetail(record.poId)">{{ record.soNo }}</a>
          </template>
          <template v-if="column.dataIndex === 'poAmount' || column.dataIndex === 'appliedAmount'">
            {{ formatCurrency(record[column.dataIndex]) }}
          </template>
        </template>
      </a-table>
    </a-card> -->

    <!-- 银行与交易信息 -->
    <!-- <a-card title="银行与交易信息" class="detail-card">
      <a-descriptions :column="2" bordered size="middle">
        <a-descriptions-item label="付款方银行名称">{{ paymentData.payerBankName }}</a-descriptions-item>
        <a-descriptions-item label="付款方银行账号">{{ paymentData.payerBankAccount }}</a-descriptions-item>
        <a-descriptions-item label="收款方银行名称">{{ paymentData.payeeBankName }}</a-descriptions-item>
        <a-descriptions-item label="收款方银行账号">{{ paymentData.payeeBankAccount }}</a-descriptions-item>
        <a-descriptions-item label="支付渠道">{{ paymentData.paymentChannel }}</a-descriptions-item>
        <a-descriptions-item label="交易流水号">{{ paymentData.transactionId }}</a-descriptions-item>
      </a-descriptions>
    </a-card> -->

    <!-- 附件、历史记录等 -->
    <!-- <a-card title="其他信息" class="detail-card">
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" tab="付款历史">
          <a-timeline v-if="paymentData.paymentHistory && paymentData.paymentHistory.length > 0">
            <a-timeline-item v-for="item in paymentData.paymentHistory" :key="item.id" :color="item.success ? 'green' : 'red'">
              <p>
                <strong>{{ formatDateTime(item.time) }}</strong> - {{ item.action }}
              </p>
              <p>操作人: {{ item.operator }} | 金额: {{ formatCurrency(item.amount) }}</p>
              <p v-if="item.notes">备注: {{ item.notes }}</p>
            </a-timeline-item>
          </a-timeline>
          <a-empty v-else description="暂无付款历史记录" />
        </a-tab-pane>

        <a-tab-pane key="2" tab="附件资料">
          <a-upload-dragger v-if="canUpload" name="file" :multiple="true" action="/api/upload-payment-attachment" :data="{ paymentId: paymentId }" @change="handleUploadChange" list-type="picture">
            <p class="ant-upload-drag-icon">
              <inbox-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">支持上传付款凭证、发票等相关文件</p>
          </a-upload-dragger>

          <a-table v-if="paymentData.attachments && paymentData.attachments.length > 0" :columns="attachmentColumns" :data-source="paymentData.attachments" :pagination="false" size="small" row-key="id" style="margin-top: 16px" />
          <a-empty v-else description="暂无附件" />
        </a-tab-pane>

        <a-tab-pane key="3" tab="操作历史">
          <a-timeline v-if="paymentData.operationLogs && paymentData.operationLogs.length > 0">
            <a-timeline-item v-for="log in paymentData.operationLogs" :key="log.id">
              <div class="log-time">{{ formatDateTime(log.time) }}</div>
              <div class="log-content">
                <span class="log-user">{{ log.userName }}</span>
                <span>{{ log.action }}</span>
              </div>
              <div class="log-detail" v-if="log.detail">{{ log.detail }}</div>
            </a-timeline-item>
          </a-timeline>
          <a-empty v-else description="暂无操作历史记录" />
        </a-tab-pane>

        <a-tab-pane key="4" tab="沟通记录">
          <a-timeline v-if="paymentData.comments && paymentData.comments.length > 0">
            <a-timeline-item v-for="(comment, index) in paymentData.comments" :key="index">
              <div class="comment-header">
                <span class="comment-user">{{ comment.userName }}</span>
                <span class="comment-time">{{ formatDateTime(comment.time) }}</span>
              </div>
              <div class="comment-content">{{ comment.content }}</div>
            </a-timeline-item>
          </a-timeline>
          <a-empty v-else description="暂无沟通记录" />

          <a-form layout="inline" class="comment-form" v-if="canComment">
            <a-form-item style="flex: 1">
              <a-textarea v-model:value="newComment" placeholder="添加备注..." :rows="2" />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="addComment" :loading="commenting">发送</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </a-card> -->

    <!-- 附件与备注 -->
    <a-card title="附件与备注" class="detail-card">
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" tab="附件资料">
          <a-upload-dragger v-if="canUpload" name="file" :multiple="true" action="/api/upload" @change="handleUploadChange">
            <p class="ant-upload-drag-icon">
              <inbox-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">支持单个或批量上传。支持 PDF, Word, Excel, 图片等格式文件</p>
          </a-upload-dragger>

          <a-table v-if="paymentData.attachments && paymentData.attachments.length > 0" :columns="attachmentColumns" :data-source="paymentData.attachments" :pagination="false" size="small" />
          <a-empty v-else description="暂无附件" />
        </a-tab-pane>
        <a-tab-pane key="2" tab="沟通记录">
          <a-timeline>
            <a-timeline-item v-for="(comment, index) in paymentData.comments" :key="index">
              <div class="comment-header">
                <span class="comment-user">{{ comment.userName }}</span>
                <span class="comment-time">{{ formatDateTime(comment.time) }}</span>
              </div>
              <div class="comment-content">{{ comment.content }}</div>
            </a-timeline-item>
          </a-timeline>
          <a-form layout="inline" class="comment-form" v-if="canComment">
            <a-form-item style="flex: 1">
              <a-textarea v-model:value="newComment" placeholder="添加备注..." :rows="2" />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="addComment">发送</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="3" tab="操作历史">
          <a-timeline>
            <a-timeline-item v-for="(log, index) in paymentData.operationLogs" :key="index">
              <div class="log-time">{{ formatDateTime(log.time) }}</div>
              <div class="log-content">
                <span class="log-user">{{ log.userName }}</span>
                <span>{{ log.action }}</span>
              </div>
              <div class="log-detail" v-if="log.detail">{{ log.detail }}</div>
            </a-timeline-item>
          </a-timeline>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 上传凭证模态框 -->
    <a-modal v-model:visible="showUploadModal" title="上传付款凭证" @ok="handleUploadProof" @cancel="showUploadModal = false" :confirmLoading="uploading" width="600px">
      <div class="upload-proof-container">
        <p class="upload-description">请上传银行转账回单、电汇凭证或其他可证明已完成付款的凭证</p>

        <a-upload-dragger name="file" :multiple="true" action="/api/upload-payment-attachment" :data="{ paymentId: paymentId }" @change="handleProofUploadChange" list-type="picture" :maxCount="5" accept=".jpg,.jpeg,.png,.pdf,.doc,.docx">
          <p class="ant-upload-drag-icon">
            <inbox-outlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">支持上传银行转账凭证、付款水单、电汇凭证等 (最大5个文件)</p>
        </a-upload-dragger>

        <div class="proof-memo-container">
          <a-form-item label="付款备注" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
            <a-textarea v-model:value="proofMemo" placeholder="请输入付款备注说明，如：通过XX银行转账XX元，于XX日XX时完成" :rows="3" :maxLength="200" showCount />
          </a-form-item>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, reactive, h } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  PageHeader as APageHeader,
  Button as AButton,
  Space as ASpace,
  Descriptions as ADescriptions,
  DescriptionsItem as ADescriptionsItem,
  Card as ACard,
  Tag as ATag,
  Table as ATable,
  Tabs as ATabs,
  TabPane as ATabPane,
  Timeline as ATimeline,
  TimelineItem as ATimelineItem,
  UploadDragger as AUploadDragger,
  Empty as AEmpty,
  message,
  Form as AForm,
  FormItem as AFormItem,
  Textarea as ATextarea,
  Alert as AAlert,
  Radio as ARadio,
  RadioGroup as ARadioGroup,
  RadioButton as ARadioButton,
  Modal as AModal,
} from 'ant-design-vue';
import { InboxOutlined, BankOutlined, CreditCardOutlined, InfoCircleOutlined, CopyOutlined } from '@ant-design/icons-vue';
import type { UploadChangeParam } from 'ant-design-vue';
import type { TableColumnType } from 'ant-design-vue';

// --- TypeScript Interfaces ---

type PaymentStatus = 'pending' | 'partially_paid' | 'paid' | 'cancelled' | 'overdue';

interface SupplierInfo {
  id: string;
  name: string;
  code: string;
  contactPerson?: string;
  contactPhone?: string;
  taxId?: string;
}

interface RelatedPO {
  poId: string;
  soNo: string;
  poDate: string;
  poAmount: number;
  appliedAmount: number; // 本次付款核销的该PO金额
}

interface PaymentHistoryItem {
  id: string;
  time: string;
  action: string; // e.g., '发起支付', '支付成功', '支付失败'
  operator: string;
  amount: number;
  success: boolean;
  notes?: string;
}

interface Attachment {
  id: string;
  name: string;
  url: string; // Download/Preview URL
  size: string;
  uploadTime: string;
  uploadUser: string;
  type?: string; // e.g., 'pdf', 'jpg', 'png'
}

interface OperationLog {
  id: string;
  time: string;
  userName: string;
  action: string;
  detail?: string;
}

interface Comment {
  id: string;
  userName: string;
  time: string;
  content: string;
}

interface PaymentDetailData {
  id: string;
  paymentNo: string;
  status: PaymentStatus;
  createTime: string;
  paymentDate?: string; // Actual payment date
  dueDate?: string; // Planned/Due date
  applyDate?: string; // Date payment was applied for
  creator: string;
  processor?: string; // Person who processed the payment
  paymentMethod: string; // e.g., '银行转账', '承兑汇票'
  paymentTerms: string; // e.g., 'Net 30', '月结60天'
  currency: string;
  totalAmount: number; // 应付总额
  paidAmount: number; // 实付总额
  dueAmount: number; // 待付总额
  statementId?: string; // ID of related statement
  statementNo?: string; // Number of related statement
  supplier: SupplierInfo;
  materials?: any[]; // Materials in the payment
  relatedPOs: RelatedPO[];
  payerBankName?: string;
  payerBankAccount?: string;
  payeeBankName?: string;
  payeeBankAccount?: string;
  payeeBankCode?: string; // 银行银联号
  payeeBankAddress?: string; // 开户行地址
  paymentChannel?: string; // e.g., '招行企业银行', '手动确认'
  transactionId?: string; // 支付流水号
  paymentHistory: PaymentHistoryItem[];
  attachments: Attachment[];
  operationLogs: OperationLog[];
  comments: Comment[];
}

// --- Component Logic ---

const route = useRoute();
const router = useRouter();
const paymentId = ref((route.params.id as string) || (route.query.id as string) || 'PAY-1001'); // Get ID from route
const loading = ref(false);
const newComment = ref('');
const commenting = ref(false);
const selectedPaymentMethod = ref<'offline' | 'online'>('offline');
const showUploadModal = ref(false);
const uploading = ref(false);
const proofMemo = ref('');
const countdownClass = ref('countdown-normal');

// Mock Data
const paymentData = reactive<PaymentDetailData>({
  id: 'PAY20250522181818',
  paymentNo: 'PAY20250522181818',
  status: 'partially_paid',
  createTime: '2025-05-15 10:00:00',
  paymentDate: '',
  dueDate: '2025-05-31',
  applyDate: '2024-07-16',
  creator: '张三',
  processor: '李四',
  paymentMethod: '现金/电汇',
  paymentTerms: '账期结算，30天',
  statementId: 'INV20250522141516',
  statementNo: 'INV20250522141516',
  currency: 'CNY',
  totalAmount: 150000,
  paidAmount: 70000,
  dueAmount: 80000,
  supplier: {
    id: 'SUP-001',
    name: '研选工场（苏州）网络有限公司',
    code: 'SUP-001',
    contactPerson: '王经理',
    contactPhone: '13812345678',
    taxId: '91320594MAE5L8PD96',
  },
  materials: [
    { id: 'mat-1', materialName: '控制板', spec: 'CB-2024-V1', brand: 'ABB', unit: '个', quantity: 10, price: 5000, totalAmount: 50000, poNo: 'PO-2024-005', poId: 'PO-2024-005' },
    { id: 'mat-2', materialName: '传感器', spec: 'SN-101', brand: 'Siemens', unit: '套', quantity: 5, price: 12000, totalAmount: 60000, poNo: 'PO-2024-005', poId: 'PO-2024-005' },
    { id: 'mat-3', materialName: '连接线缆', spec: '10m', brand: '普天', unit: '根', quantity: 20, price: 2000, totalAmount: 40000, poNo: 'PO-2024-008', poId: 'PO-2024-008' },
  ],
  relatedPOs: [
    { poId: 'PO-2024-005', soNo: 'PO-2024-005', poDate: '2024-06-10', poAmount: 90000, appliedAmount: 40000 },
    { poId: 'PO-2024-008', soNo: 'PO-2024-008', poDate: '2024-06-25', poAmount: 60000, appliedAmount: 30000 },
  ],
  payerBankName: '中国工商银行 北京支行',
  payerBankAccount: '6222 **** **** 1234',
  payeeBankName: '招商银行股份有限公司苏州独墅湖支行',
  payeeBankAccount: '***************',
  payeeBankCode: '************',
  payeeBankAddress: '苏州工业园区启月街288号',
  paymentChannel: '招行企业银行',
  transactionId: 'TRANS-********-987654',
  paymentHistory: [
    { id: 'hist-1', time: '2024-07-16 11:00:00', action: '发起支付申请', operator: '张三', amount: 150000, success: true, notes: '申请支付PO-2024-005和PO-2024-008部分款项' },
    { id: 'hist-2', time: '2024-07-20 14:30:00', action: '支付成功 (部分)', operator: '李四', amount: 70000, success: true, notes: '通过招行企业银行支付70000元' },
  ],
  attachments: [
    { id: 'att-1', name: '转账凭证_********.pdf', url: '/download/att-1', size: '1.2MB', uploadTime: '2024-07-20 15:00:00', uploadUser: '李四', type: 'pdf' },
    { id: 'att-2', name: '相关发票_INV-001.jpg', url: '/download/att-2', size: '800KB', uploadTime: '2024-07-16 10:30:00', uploadUser: '张三', type: 'jpg' },
  ],
  operationLogs: [
    { id: 'log-1', time: '2024-07-15 10:00:00', userName: '张三', action: '创建付款单', detail: '关联采购单PO-2024-005, PO-2024-008' },
    { id: 'log-2', time: '2024-07-16 09:00:00', userName: '王主管', action: '审批通过付款申请' },
    { id: 'log-3', time: '2024-07-20 14:35:00', userName: '李四', action: '确认部分付款', detail: '实付金额 70000 CNY' },
    { id: 'log-4', time: '2024-07-20 15:01:00', userName: '李四', action: '上传附件', detail: '付款水单_********.pdf' },
  ],
  comments: [
    { id: 'cmt-1', userName: '张三', time: '2024-07-16 10:05:00', content: '已提交付款申请，请主管审批。' },
    { id: 'cmt-2', userName: '王主管', time: '2024-07-16 14:20:00', content: '已审批，请财务尽快安排支付。' },
    { id: 'cmt-3', userName: '李四', time: '2024-07-20 16:00:00', content: '今日已支付7万元，剩余款项待下周处理。' },
  ],
});

// --- Computed Properties for Permissions/UI Logic ---
const showPaymentPrompt = computed(() => paymentData.status === 'pending' || paymentData.status === 'partially_paid' || paymentData.status === 'overdue');

// 实时倒计时状态
const countdownTime = ref({
  days: 0,
  hours: 0,
  minutes: 0,
  seconds: 0,
  isOverdue: false
});

// 计算剩余付款时间（精确到秒）
const updateCountdown = () => {
  if (!paymentData.dueDate) {
    // 默认15天后的截止时间
    const defaultDueDate = new Date();
    defaultDueDate.setDate(defaultDueDate.getDate() + 15);
    paymentData.dueDate = defaultDueDate.toISOString();
  }

  const now = new Date();
  const dueDate = new Date(paymentData.dueDate);
  const diffTime = dueDate.getTime() - now.getTime();

  if (diffTime <= 0) {
    countdownTime.value = {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
      isOverdue: true
    };
    return;
  }

  const days = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diffTime % (1000 * 60)) / 1000);

  countdownTime.value = {
    days,
    hours,
    minutes,
    seconds,
    isOverdue: false
  };
};

// 格式化倒计时文本
const formatCountdownText = computed(() => {
  if (paymentData.status === 'overdue' || countdownTime.value.isOverdue) {
    return `付款已逾期，请立即处理`;
  }

  const { days, hours, minutes, seconds } = countdownTime.value;

  if (days === 0 && hours === 0 && minutes === 0 && seconds === 0) {
    return `付款时间已到，请立即付款`;
  }

  let timeText = '';
  if (days > 0) {
    timeText += `<span style="color: #f94c30; font-weight: bold; margin: 0 4px; font-size: 18px;">${days}</span>天`;
  }
  if (hours > 0 || days > 0) {
    timeText += `<span style="color: #f94c30; font-weight: bold; margin: 0 4px; font-size: 18px;">${hours.toString().padStart(2, '0')}</span>小时`;
  }
  if (minutes > 0 || hours > 0 || days > 0) {
    timeText += `<span style="color: #f94c30; font-weight: bold; margin: 0 4px; font-size: 18px;">${minutes.toString().padStart(2, '0')}</span>分钟`;
  }
  timeText += `<span style="color: #f94c30; font-weight: bold; margin: 0 4px; font-size: 18px;">${seconds.toString().padStart(2, '0')}</span>秒`;

  return `距离付款截止时间还有${timeText}`;
});

// 计算剩余付款天数（兼容原有逻辑）
const daysUntilDue = computed(() => {
  return countdownTime.value.days;
});

const canEdit = computed(() => paymentData.status === 'pending' || paymentData.status === 'overdue');
const canCancel = computed(() => paymentData.status === 'pending' || paymentData.status === 'overdue' || paymentData.status === 'partially_paid');
const canMarkAsPaid = computed(() => paymentData.status === 'pending' || paymentData.status === 'partially_paid');
const canUpload = computed(() => true); // Adjust based on actual permissions
const canComment = computed(() => true); // Adjust based on actual permissions

// --- Table Columns ---
const materialColumns: TableColumnType<any>[] = [
  { title: '物料名称', dataIndex: 'materialName', key: 'materialName', width: 200 },
  { title: '型号', dataIndex: 'spec', key: 'spec', width: 150 },
  { title: '品牌', dataIndex: 'brand', key: 'brand', width: 150 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100, align: 'right' },
  { title: '单价', dataIndex: 'price', key: 'price', width: 120, align: 'right' },
  { title: '总价', dataIndex: 'totalAmount', key: 'totalAmount', width: 150, align: 'right' },
  { title: '所属采购单号', dataIndex: 'poNo', key: 'poNo', width: 150 },
];

const poColumns: TableColumnType<RelatedPO>[] = [
  { title: '采购单号', dataIndex: 'soNo', key: 'soNo', width: 150 },
  { title: '采购日期', dataIndex: 'poDate', key: 'poDate', width: 120, customRender: ({ text }) => formatDate(text) },
  { title: '采购单金额', dataIndex: 'poAmount', key: 'poAmount', align: 'right', width: 150 },
  { title: '本次核销金额', dataIndex: 'appliedAmount', key: 'appliedAmount', align: 'right', width: 150 },
];

const attachmentColumns: TableColumnType<Attachment>[] = [
  { title: '文件名', dataIndex: 'name', key: 'name' },
  { title: '大小', dataIndex: 'size', key: 'size', width: 100 },
  { title: '上传时间', dataIndex: 'uploadTime', key: 'uploadTime', width: 180, customRender: ({ text }) => formatDateTime(text) },
  { title: '上传人', dataIndex: 'uploadUser', key: 'uploadUser', width: 120 },
  {
    title: '操作',
    key: 'action',
    width: 120,
    customRender: ({ record }) => {
      return h(ASpace, {}, [h('a', { href: record.url, target: '_blank', rel: 'noopener noreferrer' }, '预览'), h('a', { href: record.url, download: record.name }, '下载')]);
    },
  },
];

// --- Methods ---
const goBack = () => {
  router.go(-1);
};

// Fetch payment details from API
const fetchPaymentDetails = async () => {
  loading.value = true;
  try {
    // In a real implementation, this would be an API call
    // const response = await api.get(`/api/payments/${paymentId.value}`);
    // paymentData = response.data;

    // Mock implementation - just set loading state for UI feedback
    await new Promise((resolve) => setTimeout(resolve, 500));

    // In a real app, we would update paymentData with the response
    // For now, we're using the mock data defined above

    // Update the countdownClass based on days remaining
    countdownClass.value = daysUntilDue.value <= 3 ? 'countdown-urgent' : 'countdown-normal';
  } catch (error) {
    message.error('获取付款单详情失败');
    console.error('Error fetching payment details:', error);
  } finally {
    loading.value = false;
  }
};

const getStatusText = (status: PaymentStatus): string => {
  const map: Record<PaymentStatus, string> = {
    pending: '待支付',
    partially_paid: '部分支付',
    paid: '已支付',
    cancelled: '已取消',
    overdue: '已逾期',
  };
  return map[status] || '未知';
};

const getStatusColor = (status: PaymentStatus): string => {
  const map: Record<PaymentStatus, string> = {
    pending: 'orange',
    partially_paid: 'blue',
    paid: 'green',
    cancelled: 'red',
    overdue: 'volcano',
  };
  return map[status] || 'default';
};

const formatCurrency = (value?: number): string => {
  if (value === undefined || value === null) return '-';
  return value.toLocaleString('zh-CN', { style: 'currency', currency: paymentData.currency || 'CNY' });
};

const formatDate = (dateStr?: string): string => {
  if (!dateStr) return '-';
  try {
    return new Date(dateStr).toLocaleDateString('zh-CN');
  } catch (e) {
    return dateStr; // Return original if formatting fails
  }
};

const formatDateTime = (dateTimeStr?: string): string => {
  if (!dateTimeStr) return '-';
  try {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    };
    return new Date(dateTimeStr).toLocaleString('zh-CN', options);
  } catch (e) {
    return dateTimeStr; // Return original if formatting fails
  }
};

const handlePrint = () => {
  message.info('触发打印功能 (待实现)');
  window.print();
};

const handleExport = () => {
  message.info('触发导出功能 (待实现)');
  // Add export logic here
};

const gotoSupplierDetail = (supplierId: string) => {
  message.info(`跳转到供应商详情: ${supplierId} (待实现)`);
  // router.push(`/supplier/detail/${supplierId}`);
};

const gotoStatementDetail = (statementId?: string) => {
  if (!statementId) return;
  message.info(`跳转到对账单详情: ${statementId}`);
  router.push({ name: 'StatementDetail', params: { id: statementId } });
};

const gotoPoDetail = (poId: string) => {
  message.info(`跳转到采购单详情: ${poId}`);
  router.push({ name: 'PoDetail', params: { id: poId } }); // Assuming you have a named route 'PoDetail'
};

const handleUploadChange = (info: UploadChangeParam) => {
  if (info.file.status === 'uploading') {
    // console.log('Uploading:', info.file, info.fileList);
  }
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 上传成功`);
    // Add the file to paymentData.attachments after successful upload (response should contain file details)
    // Need to fetch details again or update locally based on API response
    fetchPaymentDetails(); // Re-fetch details to show the new attachment
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 上传失败.`);
  }
};

// 支付相关方法
const showUploadAttachmentModal = () => {
  showUploadModal.value = true;
  proofMemo.value = ''; // 清空备注
};

const handleOfflinePaymentDone = () => {
  message.info('标记为已线下付款 (待实现)');
  // 这里应该调用API来标记付款状态，同时更新本地状态
  // 添加到付款历史记录
  paymentData.paymentHistory.push({
    id: Date.now().toString(),
    time: new Date().toISOString(),
    action: '线下支付确认',
    operator: '当前用户', // 实际应该是登录用户
    amount: paymentData.dueAmount,
    success: true,
    notes: '用户确认已完成线下支付',
  });

  // 更新支付状态
  if (paymentData.dueAmount <= 0) {
    paymentData.status = 'paid';
  } else {
    paymentData.status = 'partially_paid';
  }

  message.success('已标记为线下付款完成');
};

const handleOnlinePayment = () => {
  message.info('跳转到第三方支付平台 (待实现)');
  // 实际实现应该跳转到第三方支付页面或生成支付二维码
  // window.open('https://payment-gateway.example.com?order=' + paymentData.id + '&amount=' + paymentData.dueAmount, '_blank');
};

// 处理凭证上传
const handleProofUploadChange = (info: UploadChangeParam) => {
  if (info.file.status === 'uploading') {
    console.log('凭证上传中:', info.file, info.fileList);
  }
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 上传成功`);
    // 实际实现中应该将上传成功的文件添加到付款单的附件列表中
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 上传失败: ${info.file.error}`);
  }
};

// 处理提交凭证
const handleUploadProof = async () => {
  if (!proofMemo.value.trim()) {
    message.warning('请填写付款备注信息');
    return;
  }

  uploading.value = true;
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 800));

    // 添加操作记录
    paymentData.operationLogs.push({
      id: Date.now().toString(),
      time: new Date().toISOString(),
      userName: '当前用户', // 实际应该是登录用户
      action: '上传付款凭证',
      detail: proofMemo.value,
    });

    message.success('付款凭证提交成功');
    showUploadModal.value = false;
    proofMemo.value = '';
  } catch (error) {
    message.error('提交失败，请重试');
    console.error('上传凭证错误:', error);
  } finally {
    uploading.value = false;
  }
};

const addComment = async () => {
  if (!newComment.value.trim()) {
    message.warning('请输入评论内容');
    return;
  }
  commenting.value = true;
  try {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 300));
    const comment: Comment = {
      id: Date.now().toString(),
      userName: '当前用户', // Replace with actual logged-in user
      time: new Date().toISOString(),
      content: newComment.value,
    };
    paymentData.comments.push(comment);
    newComment.value = '';
    message.success('评论添加成功');
  } catch (error) {
    message.error('添加评论失败');
  } finally {
    commenting.value = false;
  }
};

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success('已复制到剪贴板');
  } catch (error) {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
      document.execCommand('copy');
      message.success('已复制到剪贴板');
    } catch (fallbackError) {
      message.error('复制失败');
    }
    document.body.removeChild(textArea);
  }
};

// 定时器引用
let countdownTimer: NodeJS.Timeout | null = null;

// --- Lifecycle Hook ---
onMounted(() => {
  fetchPaymentDetails();
  
  // 启动倒计时定时器
  updateCountdown(); // 立即更新一次
  countdownTimer = setInterval(updateCountdown, 1000); // 每秒更新一次
});

onUnmounted(() => {
  // 清理定时器
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
});
</script>

<style lang="less" scoped>
// Steal styles from poDetail.vue for consistency
.payment-detail-container {
  min-height: 100vh;
  padding-bottom: 20px; // Ensure space at the bottom
}

.detail-card {
  margin-top: 24px; // Use margin-top instead of bottom for better spacing control with header
  border-radius: 4px;

  .ant-card-head {
    background-color: #fafafa;
    font-weight: 500;
  }
}

.ant-page-header {
  background-color: #fff;
  padding-bottom: 16px; // Add padding if descriptions are inside header
  border-bottom: 1px solid #f0f0f0;
}

// Make currency values stand out
.currency {
  font-weight: 600;
  &.paid {
    color: #52c41a; // Green for paid
  }
  &.due {
    color: #ff4d4f; // Red for due
  }
}

// Countdown styles
.countdown-urgent {
  color: #ff4d4f;
  font-weight: bold;
}

.countdown-normal {
  color: #fa8c16;
  font-weight: 500;
}

// Styles for logs and comments copied from poDetail.vue
.log-time,
.comment-time {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  margin-bottom: 4px;
}

.log-content {
  margin-bottom: 4px;
  .log-user {
    font-weight: 500;
    margin-right: 8px;
  }
}

.log-detail {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  margin-top: 4px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;

  .comment-user {
    font-weight: 500;
  }
}

.comment-content {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  white-space: pre-wrap; // Preserve whitespace and wrap lines
}

.comment-form {
  display: flex;
  align-items: flex-start;
  margin-top: 16px;
}

// Ensure upload dragger looks right
:deep(.ant-upload-list-picture-card-container),
:deep(.ant-upload-list-picture .ant-upload-list-item),
:deep(.ant-upload-select-picture-card) {
  width: 100px;
  height: 100px;
}
:deep(.ant-upload-list-item-info) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
:deep(.ant-upload-list-item-thumbnail img) {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

// Ensure tables look good
.ant-table-wrapper {
  margin-top: 0; // Remove default margin if card provides spacing
}

// Align description labels and content properly
:deep(.ant-descriptions-item-label) {
  width: 150px; // Adjust as needed
  text-align: right;
  // white-space: nowrap;
}
:deep(.ant-descriptions-item-content) {
  font-weight: 500;
}

// Payment prompt card styles
.payment-prompt-card {
  border-left: 4px solid #faad14;

  .payment-prompt {
    padding: 8px 0;

    .payment-method-select {
      margin-top: 20px;

      .method-label {
        font-weight: 500;
        margin-right: 12px;
        display: inline-block;
        margin-bottom: 12px;
      }

      .payment-info {
        margin-top: 16px;
        padding: 16px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fafafa;

        .payment-info-flex {
          display: flex;
          gap: 16px;

          .payment-info-section {
            flex: 1;

            &.payment-tip {
              // background-color: #fffbe6;
              // border: 1px solid #ffe58f;
              border-radius: 4px;
              padding: 12px;

              .tip-header {
                color: #faad14;
                margin-bottom: 8px;
                font-size: 14px;

                .anticon {
                  margin-right: 6px;
                }
              }

              ul {
                padding-left: 18px;
                margin: 6px 0;

                li {
                  margin-bottom: 4px;
                }
              }
            }
          }
        }

        p {
          margin: 6px 0;
          line-height: 1.5;
        }

        .payment-detail-item {
          display: flex;
          align-items: center;
          margin: 8px 0;
          
          .label {
            font-weight: 500;
            min-width: 80px;
          }
          
          .value {
            margin-right: 4px;
          }
        }

        .note {
          color: #ff4d4f;
          margin-top: 12px;
          font-weight: bold;
        }

        .buttons {
          margin-top: 16px;
          display: flex;
          gap: 12px;
        }
      }
    }
  }
}

// Upload proof modal styles
.upload-proof-container {
  .upload-description {
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.85);
  }

  .proof-memo-container {
    margin-top: 16px;
  }
}
</style>
