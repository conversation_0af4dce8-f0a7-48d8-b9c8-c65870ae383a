<template>
  <div class="table-area">
    <!-- 表格操作按钮 -->
    <div class="table-operations">
      <a-space>
        <a-dropdown>
          <a-button type="primary"> 批量操作 <down-outlined /> </a-button>
          <template #overlay>
            <a-menu @click="handleBatchOperation">
              <a-menu-item key="invoice">开票</a-menu-item>
              <a-menu-item key="pay">付款</a-menu-item>
              <a-menu-item key="archive">归档</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button type="primary" @click="handleExport"> <export-outlined /> 导出 </a-button>
        <a-button type="primary" @click="handlePrint"> <printer-outlined /> 打印 </a-button>
      </a-space>
      <a-button @click="toggleTableConfig"> <setting-outlined /> 列设置 </a-button>
    </div>
    <!-- 选中订单汇总信息 -->
    <div class="selection-summary">
      <div>
        <a-tooltip placement="top">
          <template #title>
              <div>1. 本表中的价格若未做特殊说明，均为含税价格。</div>
              <div>2. 在询价单转为订单后，将根据订单总价加收运费，具体规则如下：<br />订单总金额¥0.00 - ¥499.99，运费¥15.00<br />订单总金额¥500.00 - ¥999.99，运费¥8.00<br />订单总金额¥1000以上，免运费</div>
            </template>
          <span style="color: #666"><InfoCircleFilled style="margin-right: 4px" />价格与运费说明</span>
        </a-tooltip>
      </div>
      <div class="summary-content">
        <span
          >已选择：<a-tag color="red">{{ selectedRowKeys.length }}</a-tag> 个订单</span
        >
        <span
          >总金额：<a-tag color="red">¥{{ selectedTotalPrice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</a-tag></span
        >
      </div>
    </div>

    <!-- 主表格 -->
    <a-table
      :columns="orderColumns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
      size="small"
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      bordered
      :scroll="{ x: 1500 }"
      :expandable="{
        expandedRowKeys: expandedRowKeys,
        onExpand: onExpand,
        expandRowByClick: true,
      }"
    >
      <template #expandedRowRender="{ record }">
        <div style="margin: 12px">
          <a-table :columns="visibleMaterialColumns" :data-source="record.materialItems" :pagination="false" row-key="id" bordered size="small">
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'unitPrice' || column.dataIndex === 'totalPrice'">
                {{ parseFloat(text).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
              </template>
              <template v-if="column.dataIndex === 'shippedQuantity' || column.dataIndex === 'receivedQuantity' || column.dataIndex === 'cancelledQuantity'">
                {{ text || 0 }}
              </template>
              <template v-if="column.dataIndex === 'expectedArrivalTime'">
                {{ record.expectedArrivalTime ? record.expectedArrivalTime : '-' }}
              </template>
              <template v-if="column.dataIndex === 'logisticsStatus'">
                <a-tag :color="getLogisticsStatusColor(record.logisticsStatus)">{{ record.logisticsStatus || '-' }}</a-tag>
              </template>
              <template v-if="column.dataIndex === 'financialStatus'">
                <a-tag :color="getFinancialStatusColor(record.financialStatus)">{{ record.financialStatus || '-' }}</a-tag>
              </template>
            </template>
          </a-table>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'totalAmount'">
          {{ parseFloat(record.totalAmount).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a @click="handleViewDetail(record)">详情</a>
            <a v-if="['pending_confirmation', 'in_progress'].includes(record.status)" @click="handleCancel(record)" class="danger-link">取消</a>
            <a-popconfirm v-if="record.status === 'not_submitted'" title="确定要删除此订单吗?" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record)">
              <a class="danger-link">删除</a>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 表格列配置抽屉 -->
    <a-drawer title="配置表格列" placement="right" :visible="showTableConfig" @close="toggleTableConfig" width="400px">
      <h4 style="margin-bottom: 12px">订单列配置</h4>
      <a-checkbox-group v-model:value="selectedColumns" @change="handleOrderColumnsChange">
        <a-row>
          <a-col :span="12" v-for="col in allOrderColumns" :key="col.dataIndex">
            <a-checkbox :value="col.dataIndex" :disabled="col.fixed">{{ col.title }}</a-checkbox>
          </a-col>
        </a-row>
      </a-checkbox-group>

      <a-divider />

      <h4 style="margin-top: 24px; margin-bottom: 12px">物料列配置 (展开行内)</h4>
      <a-checkbox-group v-model:value="selectedMaterialColumns" @change="handleMaterialColumnsChange">
        <a-row>
          <a-col :span="12" v-for="col in defaultMaterialColumns" :key="col.dataIndex">
            <a-checkbox :value="col.dataIndex" :disabled="col.fixed">{{ col.title }}</a-checkbox>
          </a-col>
        </a-row>
      </a-checkbox-group>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue';
import { SettingOutlined, ExportOutlined, PrinterOutlined, DownOutlined, InfoCircleFilled } from '@ant-design/icons-vue';
import { useRouter, useRoute } from 'vue-router';
// Import dropdown components if not already globally registered
// import { ADropdown, AMenu, AMenuItem } from 'ant-design-vue';

const router = useRouter();
const route = useRoute();
// 表格配置
const showTableConfig = ref(false);
const toggleTableConfig = () => {
  showTableConfig.value = !showTableConfig.value;
};

// 所有可能的订单表格列
const allOrderColumns = [
  // { title: '采购单号', dataIndex: 'soNo', key: 'soNo', width: 150, fixed: 'left' },
  { title: '订单号', dataIndex: 'soNo', key: 'soNo', width: 180, fixed: 'left' }, // 格式: PO250501142903001, PO为固定前缀，后面为年月日时分秒毫秒
  { title: '订单状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '来源询价单', dataIndex: 'rfqNo', key: 'rfqNo', width: 180 },
  // { title: '付款进度', dataIndex: 'paymentStatus', key: 'paymentStatus', width: 100 },
  { title: '物料数量', dataIndex: 'materialCount', key: 'materialCount', width: 90 },
  { title: '总金额（¥）', dataIndex: 'totalAmount', key: 'totalAmount', width: 120 },
  { title: '采购员', dataIndex: 'creator', key: 'creator', width: 100 },
  // { title: '下单时间', dataIndex: 'orderTime', key: 'orderTime', width: 150 },
  // { title: '确认时间', dataIndex: 'confirmTime', key: 'confirmTime', width: 150 },
  // { title: '完成时间', dataIndex: 'completeTime', key: 'completeTime', width: 150 },
  // { title: '归档时间', dataIndex: 'archiveTime', key: 'archiveTime', width: 150 },
  { title: '下单时间', dataIndex: 'createTime', key: 'createTime', width: 150 },
  { title: '结束时间', dataIndex: 'endTime', key: 'endTime', width: 150 },
  { title: '操作（⚠️看需求）', dataIndex: 'action', key: 'action', width: 180, fixed: 'right' },
];

// 当前选中的订单表格列
const selectedColumns = ref(['soNo', 'status', 'materialCount', 'totalAmount', 'creator', 'createTime', 'endTime', 'action']);

// 可见的订单表格列
const orderColumns = computed(() => {
  return allOrderColumns.filter((col) => selectedColumns.value.includes(col.dataIndex) || col.fixed);
});

// 默认的物料表格列 (renamed from materialColumns)
const defaultMaterialColumns = [
  { title: '物料名称', dataIndex: 'name', key: 'name', width: 180 },
  { title: '型号', dataIndex: 'model', key: 'model', width: 150 },
  { title: '品牌', dataIndex: 'brand', key: 'brand', width: 100 },
  { title: '分类', dataIndex: 'category', key: 'category', width: 120 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 80 },
  // { title: '供应商', dataIndex: 'supplierName', key: 'supplierName', width: 180 },
  // { title: '备货中数量', dataIndex: 'preparingQuantity', key: 'preparingQuantity', width: 110 },
  // { title: '在途数量', dataIndex: 'inTransitQuantity', key: 'inTransitQuantity', width: 100 },
  { title: '已发货数量', dataIndex: 'shippedQuantity', key: 'shippedQuantity', width: 110 },
  { title: '已收货数量', dataIndex: 'receivedQuantity', key: 'receivedQuantity', width: 110 },
  { title: '已取消数量', dataIndex: 'cancelledQuantity', key: 'cancelledQuantity', width: 110 },
  { title: '物流状态', dataIndex: 'logisticsStatus', key: 'logisticsStatus', width: 100 },
  { title: '财务状态', dataIndex: 'financialStatus', key: 'financialStatus', width: 100 },
  { title: '单价（¥）', dataIndex: 'unitPrice', key: 'unitPrice', width: 100 },
  { title: '总价（¥）', dataIndex: 'totalPrice', key: 'totalPrice', width: 120 },
  { title: '预计到货时间', dataIndex: 'expectedArrivalTime', key: 'expectedArrivalTime', width: 150 },
  { title: '操作（⚠️看需求）', dataIndex: 'action', key: 'action', width: 180, fixed: 'right' },
];

// 当前选中的物料表格列
const selectedMaterialColumns = ref(defaultMaterialColumns.map((col) => col.dataIndex));

// 可见的物料表格列
const visibleMaterialColumns = computed(() => {
  return defaultMaterialColumns.filter((col) => selectedMaterialColumns.value.includes(col.dataIndex) || col.fixed);
});

// 订单列变更处理
const handleOrderColumnsChange = (checkedValues) => {
  selectedColumns.value = checkedValues;
  // emit('columnsChange', checkedValues); // Consider if a more specific event is needed
};

// 物料列变更处理
const handleMaterialColumnsChange = (checkedValues) => {
  selectedMaterialColumns.value = checkedValues;
  // emit('materialColumnsChange', checkedValues); // Consider if a specific event is needed
};

// 表格数据
const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  pagination: {
    type: Object,
    default: () => ({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
    }),
  },
});

const emit = defineEmits(['tableChange', 'selectChange', 'export', 'print', 'viewDetail', 'edit', 'cancel', 'delete', 'columnsChange']);

// 选中行状态
const selectedRowKeys = ref([]);
// 展开的行
const expandedRowKeys = ref([]);

// 处理展开/折叠行的方法
const onExpand = (expanded, record) => {
  if (expanded) {
    expandedRowKeys.value = [...expandedRowKeys.value, record.id];
  } else {
    expandedRowKeys.value = expandedRowKeys.value.filter((key) => key !== record.id);
  }
};

// 计算选中订单的总价
const selectedTotalPrice = computed(() => {
  return parseFloat(
    props.tableData
      .filter((item) => selectedRowKeys.value.includes(item.id))
      .reduce((sum, item) => sum + (item.totalAmount || 0), 0)
      .toFixed(2)
  );
});

// 选中行变化处理
const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys;
  emit('selectChange', newSelectedRowKeys);
};

// 批量操作处理
const handleBatchOperation = ({ key }) => {
  // Placeholder for batch operations based on key: 'invoice', 'pay', 'archive'
  console.log(`Batch operation triggered: ${key}`, selectedRowKeys.value);
  // TODO: Implement actual batch logic based on the key and selectedRowKeys
  // Example: if (key === 'invoice') { handleBatchInvoice(selectedRowKeys.value); }
};

// 表格变化处理
const handleTableChange = (pag) => {
  emit('tableChange', pag);
};

// 状态颜色和文本
const getStatusColor = (status) => {
  const map = {
    not_submitted: 'default',
    pending_confirmation: 'orange',
    in_progress: 'blue',
    completed: 'green',
    cancelling: 'purple',
    cancelled: 'red',
  };
  return map[status] || 'default';
};

const getStatusText = (status) => {
  const map = {
    not_submitted: '草稿',
    pending_confirmation: '待确认',
    in_progress: '执行中',
    completed: '已完成',
    cancelling: '取消中',
    cancelled: '已取消',
  };
  return map[status] || '未知';
};

// Logistics Status color for materials in expanded view
const getLogisticsStatusColor = (status) => {
  const map = {
    备货中: 'cyan',
    待发货: 'blue',
    部分发货: 'purple',
    部分收货: 'orange',
    退货中: 'magenta',
    已收货: 'green',
  };
  return map[status] || 'default';
};

// Financial Status color for materials in expanded view
const getFinancialStatusColor = (status) => {
  const map = {
    未对账: 'red',
    部分对账: 'yellow',
    部分付款: 'lime',
    已支付: 'green',
  };
  return map[status] || 'default';
};

// 付款状态颜色和文本
const getPaymentStatusColor = (status) => {
  const map = {
    not_applicable: 'default', // 空
    pending_payment: 'red', // 待付款
    partially_paid: 'orange', // 部分付款
    paid: 'green', // 已付款
    refunding: 'purple', // 退款中
    partially_refunded: 'cyan', // 部分退款
    refunded: 'blue', // 已退款
  };
  return map[status] || 'default';
};

// const getPaymentStatusText = (status) => {
//   const map = {
//     not_applicable: '未付款', // 空，显示横杠
//     pending_payment: '待付款'
//   };
//   return map[status] || '未知';
// };

// 付款进度相关方法
// const getPaymentPercent = (status) => {
//   const map = {
//     not_applicable: 0,
//     pending_payment: 0,
//     partially_paid: 50,
//     paid: 100,
//     refunding: 100,
//     partially_refunded: 50,
//     refunded: 0
//   };
//   return map[status] || 0;
// };

// const getPaymentProgressStatus = (status) => {
//   if (status === 'paid') return 'success';
//   if (status === 'refunding' || status === 'partially_refunded') return 'exception';
//   if (status === 'partially_paid') return 'active';
//   return 'normal';
// };

// const getPaymentProgressColor = (status) => {
//   const map = {
//     not_applicable: '#d9d9d9',
//     pending_payment: '#d9d9d9',
//     partially_paid: '#faad14',
//     paid: '#52c41a',
//     refunding: '#722ed1',
//     partially_refunded: '#1890ff',
//     refunded: '#d9d9d9'
//   };
//   return map[status] || '#d9d9d9';
// };

// 操作方法
const handleViewDetail = (record) => {
  router.push({ path: '/purchase/poDetail', query: { id: record.id } });
};

const handleEdit = (record) => {
  emit('edit', record);
};

const handleCancel = (record) => {
  emit('cancel', record);
};

const handleDelete = (record) => {
  emit('delete', record);
};

const handleExport = () => {
  emit('export');
};

const handlePrint = () => {
  emit('print');
};
</script>

<style scoped>
.table-area {
  position: relative;
}

.table-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
}

.danger-link {
  color: #ff4d4f;
}

.expanded-row {
  background-color: #f0f8ff;
}

.selection-summary {
  position: sticky;
  bottom: 0;
  margin-bottom: 16px;
  background-color: #fff4f0;
  padding: 12px 16px;
  border-radius: 4px;
  border: 1px solid #ffa39e;
  display: flex;
  justify-content: space-between;
  z-index: 1;
}

.summary-content {
  display: flex;
  gap: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .table-operations {
    flex-direction: column;
    gap: 8px;
  }

  .selection-summary {
    flex-direction: column;
  }
}
</style>
