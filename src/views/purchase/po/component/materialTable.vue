<template>
  <div class="table-area">
    <!-- 表格操作按钮 -->
    <div class="table-operations">
      <a-space>
        <a-button type="primary" @click="handleExport"> <export-outlined /> 导出 </a-button>
        <a-button type="primary" @click="handlePrint"> <printer-outlined /> 打印 </a-button>
      </a-space>
      <a-button @click="toggleTableConfig"> <setting-outlined /> 列设置 </a-button>
    </div>

    <!-- 选中物料汇总信息 -->
    <div class="selection-summary">
      <div>
        <a-tooltip placement="top">
          <template #title>
              <div>1. 本表中的价格若未做特殊说明，均为含税价格。</div>
              <div>2. 在询价单转为订单后，将根据订单总价加收运费，具体规则如下：<br />订单总金额¥0.00 - ¥499.99，运费¥15.00<br />订单总金额¥500.00 - ¥999.99，运费¥8.00<br />订单总金额¥1000以上，免运费</div>
            </template>
          <span style="color: #666"><InfoCircleFilled style="margin-right: 4px" />价格与运费说明</span>
        </a-tooltip>
      </div>
      <div class="summary-content">
        <span
          >已选择：<a-tag color="red">{{ selectedRowKeys.length }}</a-tag> 个物料</span
        >
        <span
          >总金额：<a-tag color="red">¥{{ selectedTotalPrice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</a-tag></span
        >
      </div>
    </div>

    <!-- 主表格 -->
    <a-table :columns="visibleColumns" :data-source="tableData" :loading="loading" :pagination="pagination" @change="handleTableChange" row-key="id" :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" bordered :scroll="{ x: 1500 }">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'unitPrice' || column.dataIndex === 'totalPrice' || column.dataIndex === 'totalAmount'">
          {{ parseFloat(record[column.dataIndex]).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
        </template>
        <template v-if="column.dataIndex === 'preparingQuantity' || column.dataIndex === 'inTransitQuantity' || column.dataIndex === 'acceptedQuantity' || column.dataIndex === 'cancelledQuantity'">
          {{ record[column.dataIndex] || 0 }}
        </template>
        <template v-if="column.dataIndex === 'expectedArrivalTime'">
          {{ record.expectedArrivalTime ? record.expectedArrivalTime : '-' }}
        </template>
        <template v-if="column.dataIndex === 'logisticsStatus'">
          <a-tag :color="getLogisticsStatusColor(record.logisticsStatus)">{{ record.logisticsStatus || '-' }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'financialStatus'">
          <a-tag :color="getFinancialStatusColor(record.financialStatus)">{{ record.financialStatus || '-' }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a @click="handlePoDetail(record)">订单详情</a>
            <a v-if="['not_submitted'].includes(record.status)" @click="handleEdit(record)">编辑</a>
            <a v-if="['pending_confirmation', 'in_progress'].includes(record.status)" @click="handleCancel(record)" class="danger-link">取消</a>
            <a-popconfirm v-if="record.status === 'not_submitted'" title="确定要删除此物料吗?" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record)">
              <a class="danger-link">删除</a>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 表格列配置抽屉 -->
    <a-drawer title="配置表格列" placement="right" :visible="showTableConfig" @close="toggleTableConfig" width="400px">
      <a-checkbox-group v-model:value="selectedColumns" @change="handleColumnsChange">
        <a-row>
          <a-col :span="12" v-for="col in allColumns" :key="col.dataIndex">
            <a-checkbox :value="col.dataIndex" :disabled="col.fixed">{{ col.title }}</a-checkbox>
          </a-col>
        </a-row>
      </a-checkbox-group>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { SettingOutlined, ExportOutlined, PrinterOutlined, InfoCircleFilled } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
const router = useRouter();
// 表格配置
const showTableConfig = ref(false);
const toggleTableConfig = () => {
  showTableConfig.value = !showTableConfig.value;
};

// 所有可能的表格列
const allColumns = [
  { title: '物料名称', dataIndex: 'name', key: 'name', width: 180, fixed: 'left' },
  { title: '型号', dataIndex: 'model', key: 'model', width: 150, fixed: 'left' },
  { title: '品牌', dataIndex: 'brand', key: 'brand', width: 100 },
  { title: '分类', dataIndex: 'category', key: 'category', width: 120 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 80 },
  { title: '备货中数量', dataIndex: 'preparingQuantity', key: 'preparingQuantity', width: 110 },
  { title: '在途数量', dataIndex: 'inTransitQuantity', key: 'inTransitQuantity', width: 100 },
  { title: '已验收数量', dataIndex: 'acceptedQuantity', key: 'acceptedQuantity', width: 110 },
  { title: '已取消数量', dataIndex: 'cancelledQuantity', key: 'cancelledQuantity', width: 110 },
  { title: '物流状态', dataIndex: 'logisticsStatus', key: 'logisticsStatus', width: 100 },
  { title: '财务状态', dataIndex: 'financialStatus', key: 'financialStatus', width: 100 },
  { title: '单价（¥）', dataIndex: 'unitPrice', key: 'unitPrice', width: 100 },
  { title: '总价（¥）', dataIndex: 'totalPrice', key: 'totalPrice', width: 120 },
  { title: '来源询价单', dataIndex: 'rfqNo', key: 'rfqNo', width: 150 },
  { title: '订单号', dataIndex: 'soNo', key: 'soNo', width: 200 },
  { title: '订单状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '采购员', dataIndex: 'creator', key: 'creator', width: 100 },
  { title: '下单时间', dataIndex: 'orderTime', key: 'orderTime', width: 150 },
  { title: '预计到货日期', dataIndex: 'expectedArrivalTime', key: 'expectedArrivalTime', width: 150 },
  { title: '备注', dataIndex: 'remark', key: 'remark', width: 150 },
  { title: '操作（⚠️详见需求）', dataIndex: 'action', key: 'action', width: 180, fixed: 'right' },
];

// 当前选中的表格列
const selectedColumns = ref(['soNo', 'name', 'model', 'quantity', 'preparingQuantity', 'inTransitQuantity', 'acceptedQuantity', 'cancelledQuantity', 'logisticsStatus', 'financialStatus', 'unitPrice', 'totalPrice', 'expectedArrivalTime', 'action']);

// 可见的表格列
const visibleColumns = computed(() => {
  return allColumns.filter((col) => selectedColumns.value.includes(col.dataIndex) || col.fixed);
});

// 列变更处理
const handleColumnsChange = (checkedValues) => {
  selectedColumns.value = checkedValues;
  emit('columnsChange', checkedValues);
};

// 表格数据
const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  pagination: {
    type: Object,
    default: () => ({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
    }),
  },
});

const emit = defineEmits(['tableChange', 'selectChange', 'export', 'print', 'viewDetail', 'edit', 'cancel', 'delete', 'columnsChange']);

// 选中行状态
const selectedRowKeys = ref([]);

// 计算选中物料的总价
const selectedTotalPrice = computed(() => {
  return parseFloat(
    props.tableData
      .filter((item) => selectedRowKeys.value.includes(item.id))
      .reduce((sum, item) => sum + (item.totalPrice || 0), 0)
      .toFixed(2)
  );
});

// 选中行变化处理
const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys;
  emit('selectChange', newSelectedRowKeys);
};

// 表格变化处理
const handleTableChange = (pag) => {
  emit('tableChange', pag);
};

// 状态颜色和文本
const getStatusColor = (status) => {
  const map = {
    not_submitted: 'default',
    pending_confirmation: 'orange',
    in_progress: 'blue',
    completed: 'green',
    cancelling: 'purple',
    cancelled: 'red',
  };
  return map[status] || 'default';
};

const getStatusText = (status) => {
  const map = {
    not_submitted: '草稿',
    pending_confirmation: '待确认',
    in_progress: '执行中',
    completed: '已完成',
    cancelling: '取消中',
    cancelled: '已取消',
  };
  return map[status] || '未知';
};

// Logistics Status color
const getLogisticsStatusColor = (status) => {
  const map = {
    备货中: 'cyan',
    待发货: 'blue',
    部分发货: 'purple',
    部分收货: 'orange',
    退货中: 'magenta',
    已收货: 'green',
  };
  return map[status] || 'default';
};

// Financial Status color
const getFinancialStatusColor = (status) => {
  const map = {
    未对账: 'red',
    部分对账: 'yellow',
    部分付款: 'lime',
    已支付: 'green',
  };
  return map[status] || 'default';
};

// 操作方法
const handlePoDetail = (record) => {
  router.push({ path: '/purchase/poDetail', query: { id: record.id } });
};

const handleEdit = (record) => {
  emit('edit', record);
};

const handleCancel = (record) => {
  emit('cancel', record);
};

const handleDelete = (record) => {
  emit('delete', record);
};

const handleExport = () => {
  emit('export');
};

const handlePrint = () => {
  emit('print');
};
</script>

<style scoped>
.table-area {
  position: relative;
}

.table-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
}

.danger-link {
  color: #ff4d4f;
}

.selection-summary {
  position: sticky;
  bottom: 0;
  margin-bottom: 16px;
  background-color: #fff4f0;
  padding: 12px 16px;
  border-radius: 4px;
  border: 1px solid #ffa39e;
  display: flex;
  justify-content: space-between;
  z-index: 1;
}

.summary-content {
  display: flex;
  gap: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .table-operations {
    flex-direction: column;
    gap: 8px;
  }

  .selection-summary {
    flex-direction: column;
  }
}
</style>
