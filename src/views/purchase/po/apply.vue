<template>
  <div class="apply-container">

    <!-- 搜索区域 -->
    <div class="search-area">
      <a-form style="display: block;" layout="inline" :model="searchForm">
        <a-row>
          <template v-for="field in visibleSearchFields" :key="field.key">
            <a-col :span="4">
              <a-form-item :label="field.label">
                <!-- 输入框 -->
                <a-input v-if="field.type === 'input'" v-model:value="searchForm[field.key]" :placeholder="`请输入${field.label}`" />

                <!-- 下拉选择框 -->
                <a-select v-else-if="field.type === 'select'" v-model:value="searchForm[field.key]" :placeholder="`请选择${field.label}`" style="width: 100%" allowClear>
                  <a-select-option v-for="item in field.options" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>

                <!-- 日期范围选择器 -->
                <a-range-picker v-else-if="field.type === 'dateRange'" v-model:value="searchForm[field.key]" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
          </template>

          <a-col :span="4" class="search-buttons">
            <a-space>
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button @click="handleReset">重置</a-button>
              <a-button type="link" @click="toggleSearchConfig">
                <setting-outlined />
                配置搜索项
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>

      <!-- 搜索字段配置抽屉 -->
      <a-drawer title="配置搜索项" placement="right" :visible="showSearchConfig" @close="toggleSearchConfig" width="400px">
        <a-checkbox-group v-model:value="selectedSearchFields" @change="handleSearchFieldsChange">
          <a-row>
            <a-col :span="12" v-for="field in allSearchFields" :key="field.key">
              <a-checkbox :value="field.key">{{ field.label }}</a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </a-drawer>
    </div>

    <!-- 表格区域 -->
    <div class="view-selector">
      <a-button-group>
        <a-button :type="viewMode === 'order' ? 'primary' : 'default'" @click="switchViewMode('order')">
          <ordered-list-outlined /> 申请单视图
        </a-button>
        <a-button :type="viewMode === 'product' ? 'primary' : 'default'" @click="switchViewMode('product')">
          <appstore-outlined /> 物料视图
        </a-button>
      </a-button-group>
    </div>

    <!-- 物料视图表格 -->
    <apply-material-table
      v-if="viewMode === 'product'"
      :tableData="tableData"
      :loading="loading"
      :pagination="pagination"
      :selectedRowKeys="selectedRowKeys"
      @tableChange="handleTableChange"
      @selectChange="onSelectChange"
      @export="handleExport"
      @print="handlePrint"
      @edit="handleEdit"
      @delete="handleDelete"
      @submit="handleSubmit"
      @review="handleReview"
      @columnsChange="handleMaterialColumnsChange"
    />

    <!-- 申请单视图表格 -->
    <apply-order-table
      v-else
      :tableData="tableData"
      :loading="loading"
      :pagination="pagination"
      :selectedRowKeys="selectedRowKeys"
      @tableChange="handleTableChange"
      @selectChange="onSelectChange"
      @export="handleExport"
      @print="handlePrint"
      @edit="handleEdit"
      @delete="handleDelete"
      @submit="handleSubmit"
      @review="handleReview"
      @batchSubmit="handleBatchSubmit"
      @columnsChange="handleOrderColumnsChange"
    />

    <!-- 提交确认模态框 -->
    <a-modal
      v-model:visible="submitModalVisible"
      title="提交采购申请"
      :confirm-loading="submitLoading"
      @ok="confirmSubmit"
      @cancel="cancelSubmit"
      width="600px"
    >
      <div class="submit-form">
        <a-form :model="submitForm" layout="vertical">
          <a-form-item label="提交说明" required>
            <a-textarea
              v-model:value="submitForm.submitNote"
              placeholder="请输入提交说明，描述此次采购申请的目的和要求"
              :rows="4"
              showCount
              :maxlength="500"
            />
          </a-form-item>
          <a-form-item label="指定审批人">
            <a-select
              v-model:value="submitForm.approver"
              placeholder="请选择审批人（可选，不选择将使用默认审批流程）"
              allowClear
            >
              <a-select-option v-for="user in approverList" :key="user.id" :value="user.id">
                {{ user.name }} - {{ user.department }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="预期交付时间">
            <a-date-picker
              v-model:value="submitForm.expectedDeliveryDate"
              style="width: 100%"
              placeholder="请选择预期交付时间"
              format="YYYY-MM-DD"
            />
          </a-form-item>
        </a-form>
        
        <div class="submit-summary">
          <a-alert
            :message="`即将提交 ${selectedSubmitItems.length} 个采购申请`"
            type="info"
            show-icon
          >
            <template #description>
              <div v-if="selectedSubmitItems.length > 0">
                <p><strong>申请单号：</strong></p>
                <ul>
                  <li v-for="item in selectedSubmitItems.slice(0, 5)" :key="item.id">
                    {{ item.soNo }} - 总金额: ¥{{ item.totalAmount?.toLocaleString() }}
                  </li>
                  <li v-if="selectedSubmitItems.length > 5">
                    ...还有 {{ selectedSubmitItems.length - 5 }} 个申请单
                  </li>
                </ul>
                <p><strong>总计金额：¥{{ totalSubmitAmount.toLocaleString() }}</strong></p>
              </div>
            </template>
          </a-alert>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { 
  SettingOutlined, 
  PlusOutlined, 
  UploadOutlined, 
  DeleteOutlined,
  AppstoreOutlined, 
  OrderedListOutlined 
} from '@ant-design/icons-vue';
import ApplyMaterialTable from './component/applyMaterialTable.vue';
import ApplyOrderTable from './component/applyOrderTable.vue';

// 搜索配置
const showSearchConfig = ref(false);
const toggleSearchConfig = () => {
  showSearchConfig.value = !showSearchConfig.value;
};

// 采购申请专用搜索字段（去除了订单状态、下单时间、结束时间）
const allSearchFields = [
  { key: 'soNo', label: '申请单号', type: 'input' },
  { key: 'materialName', label: '物料名称', type: 'input' },
  { key: 'materialModel', label: '物料型号', type: 'input' },
  { key: 'createTimeRange', label: '创建时间', type: 'dateRange' },
  { key: 'creator', label: '创建人', type: 'input' },
  {
    key: 'approvalStatus',
    label: '审核状态',
    type: 'select',
    options: [
      { label: '未审核', value: 'pending' },
      { label: '审核中', value: 'reviewing' },
      { label: '审核通过', value: 'approved' },
      { label: '审核不通过', value: 'rejected' },
    ],
  },
  {
    key: 'category',
    label: '物料分类',
    type: 'select',
    options: [
      { label: '电子元件', value: 'electronics' },
      { label: '机械零件', value: 'mechanical' },
      { label: '原材料', value: 'raw' },
    ],
  },
  { key: 'brand', label: '品牌', type: 'input' },
];

// 当前选中的搜索字段
const selectedSearchFields = ref(['soNo', 'createTimeRange', 'creator', 'approvalStatus']);

// 可见的搜索字段
const visibleSearchFields = computed(() => {
  return allSearchFields.filter((field) => selectedSearchFields.value.includes(field.key));
});

// 搜索字段变更处理
const handleSearchFieldsChange = (checkedValues) => {
  selectedSearchFields.value = checkedValues;
};

// 搜索表单
const searchForm = reactive({
  soNo: '',
  materialName: '',
  materialModel: '',
  createTimeRange: [],
  creator: '',
  approvalStatus: undefined,
  category: undefined,
  brand: '',
});

// 搜索方法
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

// 重置查询条件
const handleReset = () => {
  Object.keys(searchForm).forEach((key) => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = [];
    } else {
      searchForm[key] = undefined;
    }
  });
  handleSearch();
};

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 视图模式
const viewMode = ref('order'); // 'product' 或 'order'

// 选中行状态
const selectedRowKeys = ref([]);

// 提交相关状态
const submitModalVisible = ref(false);
const submitLoading = ref(false);
const submitForm = reactive({
  submitNote: '',
  approver: undefined,
  expectedDeliveryDate: undefined,
});

// 审批人列表
const approverList = ref([
  { id: 'user001', name: '张经理', department: '采购部' },
  { id: 'user002', name: '李主管', department: '财务部' },
  { id: 'user003', name: '王总监', department: '运营部' },
]);

// 计算选中要提交的项目
const selectedSubmitItems = computed(() => {
  if (viewMode.value === 'order') {
    return tableData.value.filter(item => selectedRowKeys.value.includes(item.id));
  } else {
    // 物料视图下，需要按订单分组
    const selectedMaterials = tableData.value.filter(item => selectedRowKeys.value.includes(item.id));
    const orderMap = new Map();
    selectedMaterials.forEach(material => {
      if (!orderMap.has(material.soNo)) {
        orderMap.set(material.soNo, {
          id: material.soNo,
          soNo: material.soNo,
          totalAmount: 0,
          materials: []
        });
      }
      const order = orderMap.get(material.soNo);
      order.totalAmount += material.totalPrice;
      order.materials.push(material);
    });
    return Array.from(orderMap.values());
  }
});

// 计算总提交金额
const totalSubmitAmount = computed(() => {
  return selectedSubmitItems.value.reduce((sum, item) => sum + (item.totalAmount || 0), 0);
});

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
};

// 选中行变化处理
const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys;
};

// 切换视图模式
const switchViewMode = (mode) => {
  viewMode.value = mode;
  selectedRowKeys.value = []; // 切换视图时清空选择
  fetchData();
};

// 列变更处理
const handleMaterialColumnsChange = (columns) => {
  console.log('Material columns changed:', columns);
};

const handleOrderColumnsChange = (columns) => {
  console.log('Order columns changed:', columns);
};

// Helper function to format date
const formatDateTime = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 获取数据 - 只显示草稿状态的申请
const fetchData = () => {
  loading.value = true;

  setTimeout(() => {
    const flattenedData = [];
    const orderData = [];

    // 模拟8个采购申请单，包含不同的审核状态
    Array.from({ length: 8 }).forEach((_, orderIndex) => {
      const createDate = new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000);
      
      // 审核状态
      const approvalStatuses = ['pending', 'reviewing', 'approved', 'rejected'];
      const approvalStatus = approvalStatuses[orderIndex % approvalStatuses.length];

      const orderInfo = {
        id: `apply-order-${orderIndex}`,
        soNo: `APPLY-${2000 + orderIndex}`,
        rfqNo: `RFQ-2023-${2000 + orderIndex}`,
        status: 'not_submitted', // 保持草稿状态，但用approvalStatus控制审核流程
        creator: `申请人${(orderIndex % 4) + 1}`,
        approvalStatus: approvalStatus,
        createTime: formatDateTime(createDate),
        totalAmount: 0,
        materialCount: 0,
        materialItems: [],
        submitNote: '', // 提交说明
        approver: null, // 指定审批人
        expectedDeliveryDate: null, // 预期交付时间
      };

      // 每个申请单包含1-4个物料
      const materialCount = Math.floor(Math.random() * 4) + 1;
      orderInfo.materialCount = 0;

      Array.from({ length: materialCount }).forEach((_, mIndex) => {
        const quantity = Math.floor(Math.random() * 50) + 1;
        const unitPrice = parseFloat((Math.random() * 800 + 50).toFixed(2));
        const totalPrice = parseFloat((quantity * unitPrice).toFixed(2));
        
        orderInfo.totalAmount += totalPrice;
        orderInfo.materialCount += quantity;

        const materialItem = {
          id: `apply-material-${orderIndex}-${mIndex}`,
          soNo: orderInfo.soNo,
          name: `测试物料 ${mIndex + 1}`,
          model: `MODEL-${1000 + mIndex}`,
          brand: mIndex % 3 === 0 ? '品牌A' : mIndex % 3 === 1 ? '品牌B' : '品牌C',
          category: mIndex % 2 === 0 ? '电子元件' : '机械零件',
          unit: '个',
          quantity: quantity,
          unitPrice: unitPrice,
          totalPrice: totalPrice,
          rfqNo: `RFQ-2023-${2000 + mIndex}`,
          status: 'not_submitted',
          creator: orderInfo.creator,
          approvalStatus: orderInfo.approvalStatus,
          createTime: orderInfo.createTime,
          remark: mIndex % 3 === 0 ? '特殊规格要求' : '',
          // 草稿状态下的数量都为0
          preparingQuantity: 0,
          inTransitQuantity: 0,
          acceptedQuantity: 0,
          cancelledQuantity: 0,
          expectedArrivalTime: null,
          logisticsStatus: null,
          financialStatus: null,
        };

        flattenedData.push(materialItem);
        orderInfo.materialItems.push(materialItem);
      });

      orderInfo.totalAmount = parseFloat(orderInfo.totalAmount.toFixed(2));
      orderData.push(orderInfo);
    });

    if (viewMode.value === 'product') {
      tableData.value = flattenedData;
    } else {
      tableData.value = orderData;
    }
    
    pagination.total = (viewMode.value === 'product' ? flattenedData.length : orderData.length) * 3;
    loading.value = false;
  }, 500);
};

// 快捷操作方法
const handleCreateNew = () => {
  message.info('新建采购申请功能待实现');
  // TODO: 跳转到新建申请页面或打开新建模态框
};

// 单个提交
const handleSubmit = (record) => {
  selectedRowKeys.value = [record.id];
  submitModalVisible.value = true;
};

// 批量提交
const handleBatchSubmit = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要提交的申请');
    return;
  }
  submitModalVisible.value = true;
};

// 确认提交
const confirmSubmit = async () => {
  if (!submitForm.submitNote.trim()) {
    message.error('请输入提交说明');
    return;
  }

  submitLoading.value = true;
  
  try {
    // 模拟提交API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    message.success(`成功提交 ${selectedSubmitItems.value.length} 个采购申请`);
    submitModalVisible.value = false;
    selectedRowKeys.value = [];
    
    // 重置提交表单
    submitForm.submitNote = '';
    submitForm.approver = undefined;
    submitForm.expectedDeliveryDate = undefined;
    
    // 刷新数据
    fetchData();
  } catch (error) {
    message.error('提交失败，请重试');
  } finally {
    submitLoading.value = false;
  }
};

// 取消提交
const cancelSubmit = () => {
  submitModalVisible.value = false;
  submitForm.submitNote = '';
  submitForm.approver = undefined;
  submitForm.expectedDeliveryDate = undefined;
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的申请');
    return;
  }

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个采购申请吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟删除API调用
        await new Promise(resolve => setTimeout(resolve, 500));
        message.success(`成功删除 ${selectedRowKeys.value.length} 个采购申请`);
        selectedRowKeys.value = [];
        fetchData();
      } catch (error) {
        message.error('删除失败，请重试');
      }
    }
  });
};

// 编辑
const handleEdit = (record) => {
  message.info(`编辑申请: ${record.soNo}`);
  // TODO: 跳转到编辑页面或打开编辑模态框
};

// 删除单个
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除申请 ${record.soNo} 吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        message.success('删除成功');
        fetchData();
      } catch (error) {
        message.error('删除失败，请重试');
      }
    }
  });
};

// 审核
const handleReview = (record) => {
  Modal.confirm({
    title: '确认审核',
    content: `确定要审核申请 ${record.soNo} 吗？`,
    okText: '通过',
    cancelText: '不通过',
    onOk: async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        message.success('审核通过');
        fetchData();
      } catch (error) {
        message.error('审核失败，请重试');
      }
    },
    onCancel: async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        message.info('审核不通过');
        fetchData();
      } catch (error) {
        message.error('操作失败，请重试');
      }
    }
  });
};

// 导出
const handleExport = () => {
  message.info('导出功能待实现');
};

// 打印
const handlePrint = () => {
  message.info('打印功能待实现');
};

// 初始化
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.apply-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 20px;
  font-weight: 500;
}

.description {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.search-area {
  margin-bottom: 16px;
}

.search-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.quick-actions {
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.view-selector {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.submit-form {
  margin-top: 16px;
}

.submit-summary {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.submit-summary ul {
  margin: 8px 0;
  padding-left: 20px;
}

.submit-summary li {
  margin: 4px 0;
  font-size: 13px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .search-buttons {
    justify-content: flex-start;
    margin-top: 12px;
  }

  .quick-actions {
    text-align: center;
  }

  .view-selector {
    justify-content: center;
    margin: 16px 0;
  }
}
</style>
