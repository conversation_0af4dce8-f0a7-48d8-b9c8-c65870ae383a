<template>
  <div class="po-detail-container">
    <a-page-header :title="'订单详情 - ' + poData.poNo" :sub-title="'状态：' + getStatusText(poData.status)" @back="goBack">
      <template #extra>
        <a-space>
          <!-- <a-button type="primary" v-if="canApprove">审批</a-button>
          <a-button type="primary" v-if="canEdit">编辑</a-button> -->
          <a-button v-if="canCancel">取消订单（⚠️见需求）</a-button>
          <!-- <a-button>打印</a-button> -->
          <!-- <a-button>导出</a-button> -->
        </a-space>
      </template>
      <!-- <a-descriptions size="small" :column="4">
        <a-descriptions-item label="创建时间">{{ formatDateTime(poData.createTime) }}</a-descriptions-item>
        <a-descriptions-item label="预计全部交货日期">{{ formatDate(poData.expectedDeliveryDate) }}</a-descriptions-item>
        <a-descriptions-item label="采购员">{{ poData.purchaser }}</a-descriptions-item>
      </a-descriptions> -->
    </a-page-header>

    <!-- 订单进度条 -->
    <div class="po-progress-section">
      <a-card title="订单流程">
        <a-steps :current="currentStep" size="small">
          <a-step v-for="(step, index) in orderSteps" :key="index" :title="step.title">
            <template #description>
              <div>{{ step.time ? formatDateTime(step.time) : '未开始' }}</div>
              <div>{{ step.operator || '' }}</div>
            </template>
          </a-step>
        </a-steps>
      </a-card>
    </div>

    <!-- 基本信息 -->
    <a-card title="基本信息" class="detail-card">
      <a-row :gutter="24">
        <a-col :span="8">
          <div class="info-item">
            <span class="label">订单号：</span>
            <span class="value">{{ poData.poNo }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">订单状态：</span>
            <span class="value">
              <a-tag :color="getStatusColor(poData.status)">{{ getStatusText(poData.status) }}</a-tag>
            </span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">下单时间：</span>
            <span class="value">{{ formatDateTime(poData.createTime) }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">采购员：</span>
            <span class="value">{{ poData.contactPerson }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">联系电话：</span>
            <span class="value">{{ poData.contactPhone }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">付款方式：</span>
            <span class="value">{{ poData.paymentMethod }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">付款条件：</span>
            <span class="value">{{ poData.paymentTerms }}</span>
          </div>
        </a-col>
        <!-- <a-col :span="8">
          <div class="info-item">
            <span class="label">付款进度：</span>
            <span class="value">
              <div style="width: 200px;">
                <a-progress :percent="getPaymentProgress(poData.paymentStatus)" :status="getPaymentProgressStatus(poData.paymentStatus)" :stroke-color="getPaymentProgressColor(poData.paymentStatus)" size="small" />
              </div>
            </span>
          </div>
        </a-col> -->
        <a-col :span="8">
          <div class="info-item">
            <span class="label">总金额：</span>
            <span class="value important">¥{{ formatCurrency(poData.totalAmount) }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">当前剩余账期额度：</span>
            <span class="value important">¥{{ formatCurrency(poData.residue) }}</span>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 物料明细 -->
    <a-card title="物料明细" class="detail-card">
      <a-table :columns="productColumns" :data-source="poData.items" :pagination="false" size="middle" :scroll="{ x: 1500 }">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'quantity' && poData.status === 'draft'">
            <a-input-number 
              v-model:value="record.quantity" 
              :min="1" 
              @change="updateItemQuantity(record, index)"
              style="width: 100%"
            />
          </template>
          <template v-else-if="column.dataIndex === 'quantity'">
            {{ record.quantity }}
          </template>
          <template v-if="column.dataIndex === 'shippedQuantity'">
            {{ poData.status === 'draft' ? '-' : record.shippedQuantity }}
          </template>
          <template v-if="column.dataIndex === 'receivedQuantity'">
            {{ poData.status === 'draft' ? '-' : record.receivedQuantity }}
          </template>
          <template v-if="column.dataIndex === 'cancelledQuantity'">
            {{ poData.status === 'draft' ? '-' : record.cancelledQuantity }}
          </template>
          <template v-if="column.dataIndex === 'actions' && poData.status === 'draft'">
            <a-space>
              <a @click="deleteProduct(record, index)" style="color: #ff4d4f;">删除</a>
            </a-space>
          </template>
          <template v-else-if="column.dataIndex === 'actions'">
            <a-space>
              <a @click="cancelProduct(record)">取消</a>
            </a-space>
          </template>
        </template>
      </a-table>
      <div class="summary-section">
        <a-row justify="end">
          <a-col :span="8">
            <div class="summary-item">
              <span class="label">物料总数：</span>
              <span class="value">{{ getTotalQuantity() }} 件</span>
            </div>
            <div class="summary-item">
              <span class="label">物料总价：</span>
              <span class="value important">¥{{ formatCurrency(poData.subtotalAmount) }}</span>
            </div>
            <div class="summary-item">
              <span class="label">运费：</span>
              <span class="value">¥{{ formatCurrency(poData.shippingFee) }}</span>
            </div>
            <!-- <div class="summary-item">
              <span class="label">税费：</span>
              <span class="value">¥{{ formatCurrency(poData.tax) }}</span>
            </div> -->
            <div class="summary-item total">
              <span class="label">应付总额：</span>
              <span class="value important">¥{{ formatCurrency(poData.totalAmount) }}</span>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-card>

    <!-- 收货信息、配送信息、物流信息 -->
    <a-card title="收货与开票信息" class="detail-card">
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" tab="收货信息">
          <div v-if="poData.status === 'draft'" style="margin-bottom: 16px;">
            <a-button type="primary" @click="showReceiverModal">选择收货信息</a-button>
          </div>
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="联系人">{{ poData.receiverName }}</a-descriptions-item>
            <a-descriptions-item label="手机号">{{ poData.receiverPhone }}</a-descriptions-item>
            <a-descriptions-item label="地区" :span="2">{{ poData.receiverRegion }}</a-descriptions-item>
            <a-descriptions-item label="详细地址" :span="2">{{ poData.receiverAddress }}</a-descriptions-item>
            <a-descriptions-item label="备注" :span="2">{{ poData.remark }}</a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
        <a-tab-pane key="2" tab="开票信息">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="发票抬头">{{ poData.invoiceTitle }}</a-descriptions-item>
            <a-descriptions-item label="税号">{{ poData.taxId }}</a-descriptions-item>
            <a-descriptions-item label="注册地址" :span="2">{{ poData.invoiceRegisteredAddress }}</a-descriptions-item>
            <a-descriptions-item label="电话">{{ poData.invoicePhone }}</a-descriptions-item>
            <a-descriptions-item label="开户行">{{ poData.invoiceBankName }}</a-descriptions-item>
            <a-descriptions-item label="银行账户" :span="2">{{ poData.invoiceBankAccount }}</a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 下游单据 -->
    <a-card title="相关单据" class="detail-card">
      <a-tabs default-active-key="statement">
        <a-tab-pane key="2" tab="送货单">
          <a-table :columns="deliveryColumns" :data-source="relatedDocuments.deliveries" :pagination="{ pageSize: 5 }" size="small" />
        </a-tab-pane>
        <a-tab-pane key="3" tab="退货单">
          <a-table :columns="returnColumns" :data-source="relatedDocuments.returns" :pagination="{ pageSize: 5 }" size="small" />
        </a-tab-pane>
        <a-tab-pane key="statement" tab="对账单">
          <a-table :columns="statementColumns" :data-source="relatedDocuments.statements" :pagination="{ pageSize: 5 }" size="small" />
        </a-tab-pane>
        <a-tab-pane key="1" tab="付款单">
          <a-table :columns="paymentColumns" :data-source="relatedDocuments.payments" :pagination="{ pageSize: 5 }" size="small" />
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 附件和备注 -->
    <a-card title="附件与备注" class="detail-card">
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" tab="附件资料">
          <a-upload-dragger v-if="canUpload" name="file" :multiple="true" action="/api/upload" @change="handleUploadChange">
            <p class="ant-upload-drag-icon">
              <inbox-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">支持单个或批量上传。支持 PDF, Word, Excel, 图片等格式文件</p>
          </a-upload-dragger>

          <a-table v-if="poData.attachments && poData.attachments.length > 0" :columns="attachmentColumns" :data-source="poData.attachments" :pagination="false" size="small" />
          <a-empty v-else description="暂无附件" />
        </a-tab-pane>
        <a-tab-pane key="2" tab="沟通记录">
          <a-timeline>
            <a-timeline-item v-for="(comment, index) in poData.comments" :key="index">
              <div class="comment-header">
                <span class="comment-user">{{ comment.userName }}</span>
                <span class="comment-time">{{ formatDateTime(comment.time) }}</span>
              </div>
              <div class="comment-content">{{ comment.content }}</div>
            </a-timeline-item>
          </a-timeline>
          <a-form layout="inline" class="comment-form" v-if="canComment">
            <a-form-item style="flex: 1">
              <a-textarea v-model:value="newComment" placeholder="添加备注..." :rows="2" />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="addComment">发送</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="3" tab="操作历史">
          <a-timeline>
            <a-timeline-item v-for="(log, index) in poData.operationLogs" :key="index">
              <div class="log-time">{{ formatDateTime(log.time) }}</div>
              <div class="log-content">
                <span class="log-user">{{ log.userName }}</span>
                <span>{{ log.action }}</span>
              </div>
              <div class="log-detail" v-if="log.detail">{{ log.detail }}</div>
            </a-timeline-item>
          </a-timeline>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- Fixed action buttons at the bottom right -->
    <div class="bottom-actions">
      <a-space>
        <!-- <a-button type="primary" v-if="canApprove">审批</a-button>
        <a-button type="primary" v-if="canEdit">编辑</a-button> -->
        <a-button v-if="canCancel">取消订单（⚠️见需求）</a-button>
        <!-- <a-button>打印</a-button> -->
        <!-- <a-button>导出</a-button> -->
      </a-space>
    </div>

    <!-- 收货信息选择模态框 -->
    <a-modal
      v-model:visible="receiverModalVisible"
      title="选择收货信息"
      :width="800"
      @ok="handleReceiverSelect"
      @cancel="receiverModalVisible = false"
    >
      <a-table
        :columns="receiverColumns"
        :data-source="receiverList"
        :pagination="false"
        :row-selection="{ type: 'radio', selectedRowKeys: selectedReceiverKeys, onChange: onReceiverSelectChange }"
        size="small"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { InboxOutlined } from '@ant-design/icons-vue';

const route = useRoute();
const router = useRouter();
const id = ref(route.params.id || route.query.id);
const loading = ref(false);
const newComment = ref('');

// 收货信息相关状态
const receiverModalVisible = ref(false);
const selectedReceiverKeys = ref([]);
const receiverList = ref([
  {
    id: '1',
    name: '王五',
    phone: '13900139000',
    region: '北京市海淀区',
    address: '科技园区888号智能制造中心3号楼',
    isDefault: true,
  },
  {
    id: '2',
    name: '李四',
    phone: '13800138000',
    region: '上海市浦东新区',
    address: '张江高科技园区999号创新大厦5楼',
    isDefault: false,
  },
  {
    id: '3',
    name: '张三',
    phone: '13700137000',
    region: '深圳市南山区',
    address: '科技园南区软件园A栋2楼',
    isDefault: false,
  },
]);

const receiverColumns = [
  { title: '联系人', dataIndex: 'name', key: 'name' },
  { title: '联系电话', dataIndex: 'phone', key: 'phone' },
  { title: '地区', dataIndex: 'region', key: 'region' },
  { title: '详细地址', dataIndex: 'address', key: 'address' },
  { 
    title: '默认', 
    dataIndex: 'isDefault', 
    key: 'isDefault',
    customRender: ({ text }) => text ? '✓' : ''
  },
];

// 订单状态数据
const poData = ref({
  id: '',
  poNo: '', // 草稿状态下订单号为空
  status: 'draft',
  invoicingStatus: 'invoicing',
  paymentStatus: 'partially_paid',
  createTime: '', // 草稿状态下下单时间为空
  expectedDeliveryDate: '2023-11-15',
  purchaser: '张三',
  purchaseGroup: '机械装备组',
  contactPerson: '李四',
  contactPhone: '13800138000',
  paymentMethod: '现金（电汇）',
  paymentTerms: '月结，账期15日',
  subtotalAmount: 185000,
  tax: 16650,
  shippingFee: 2500,
  totalAmount: 204150,
  residue: 500000,
  receiverName: '王五',
  receiverPhone: '13900139000',
  receiverRegion: '北京市海淀区',
  receiverAddress: '科技园区888号智能制造中心3号楼',
  receivingTimeRequirement: '工作日9:00-17:00，提前2小时电话通知',
  remark: '物品易碎，请妥善包装',
  deliveryMethod: '快递',
  deliveryService: '包含安装调试服务',
  packagingRequirements: '防潮、防震包装',
  invoiceTitle: 'XX科技有限公司',
  taxId: '91110108MA01R1XNXX',
  invoiceRegisteredAddress: '北京市海淀区中关村软件园XX号',
  invoicePhone: '010-********',
  invoiceBankName: '中国工商银行北京海淀支行',
  invoiceBankAccount: '0200049619200088888',
  items: [
    {
      id: '1',
      key: '1',
      name: '伺服电机',
      model: 'SM2000',
      brand: 'ABB',
      category: '传动系统',
      rfqNo: 'RFQ-2023-0001',
      quantity: 5,
      shippedQuantity: 0, // 草稿状态下显示 "-"
      receivedQuantity: 0, // 草稿状态下显示 "-"
      cancelledQuantity: 0, // 草稿状态下显示 "-"
      expectedArrivalDate: '2023-11-10',
      unitPrice: 12000,
      totalPrice: 60000,
      status: 'draft',
    },
    {
      id: '2',
      key: '2',
      name: '工业控制器',
      model: 'IC5000',
      brand: 'Siemens',
      category: '控制系统',
      rfqNo: 'RFQ-2023-0001',
      quantity: 2,
      shippedQuantity: 0, // 草稿状态下显示 "-"
      receivedQuantity: 0, // 草稿状态下显示 "-"
      cancelledQuantity: 0, // 草稿状态下显示 "-"
      expectedArrivalDate: '2023-11-12',
      unitPrice: 35000,
      totalPrice: 70000,
      status: 'draft',
    },
    {
      id: '3',
      key: '3',
      name: '传感器',
      model: 'S3000',
      brand: 'Honeywell',
      category: '传感设备',
      rfqNo: 'RFQ-2023-0001',
      quantity: 20,
      shippedQuantity: 0, // 草稿状态下显示 "-"
      receivedQuantity: 0, // 草稿状态下显示 "-"
      cancelledQuantity: 0, // 草稿状态下显示 "-"
      expectedArrivalDate: '2023-10-30',
      unitPrice: 2500,
      totalPrice: 50000,
      status: 'draft',
    },
    {
      id: '4',
      key: '4',
      name: '液压阀门',
      model: 'HV200',
      brand: 'Parker',
      category: '液压系统',
      rfqNo: 'RFQ-2023-0001',
      quantity: 5,
      shippedQuantity: 0, // 草稿状态下显示 "-"
      receivedQuantity: 0, // 草稿状态下显示 "-"
      cancelledQuantity: 0, // 草稿状态下显示 "-"
      expectedArrivalDate: '2023-11-05',
      unitPrice: 1000,
      totalPrice: 5000,
      status: 'draft',
    },
  ],
  logisticsRecords: [],
  attachments: [
    {
      id: '1',
      name: '合同文档.pdf',
      type: 'pdf',
      size: '2.5MB',
      uploadTime: '2023-10-15 10:30:00',
      uploadUser: '张三',
    },
    {
      id: '2',
      name: '技术规格说明.docx',
      type: 'docx',
      size: '1.8MB',
      uploadTime: '2023-10-15 11:20:00',
      uploadUser: '张三',
    },
  ],
  comments: [
    {
      id: '1',
      userName: '张三',
      time: '2023-10-15 10:45:00',
      content: '已与供应商确认交货时间',
    },
    {
      id: '2',
      userName: '李四',
      time: '2023-10-16 14:30:00',
      content: '请注意控制器的型号需要与之前的设备兼容',
    },
  ],
  operationLogs: [
    {
      id: '1',
      userName: '张三',
      time: '2023-10-15 09:30:00',
      action: '创建了采购单',
    },
    {
      id: '2',
      userName: '王经理',
      time: '2023-10-15 14:20:00',
      action: '审批通过了采购单',
    },
    {
      id: '3',
      userName: '张三',
      time: '2023-10-16 09:15:00',
      action: '发送采购单给供应商',
    },
    {
      id: '4',
      userName: '系统',
      time: '2023-10-18 14:30:00',
      action: '更新了物流状态',
      detail: '供应商已发货',
    },
  ],
});

// 新增：对账单表格列定义
const statementColumns = [
  { title: '对账单号', dataIndex: 'statementNo', key: 'statementNo' },
  { title: '账期', dataIndex: 'period', key: 'period' },
  { title: '应付金额(元)', dataIndex: 'amountDue', key: 'amountDue', customRender: ({ text }) => formatCurrency(text) },
  { title: '已付金额(元)', dataIndex: 'amountPaid', key: 'amountPaid', customRender: ({ text }) => formatCurrency(text) },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    customRender: ({ text }) => {
      // 定义对账单状态的显示，您可以根据实际需求修改
      const statusMap = {
        pending: { text: '待核对', color: 'orange' },
        confirmed: { text: '已核对', color: 'blue' },
        paid: { text: '已付款', color: 'green' },
      };
      return h('a-tag', { color: statusMap[text]?.color }, statusMap[text]?.text || text);
    },
  },
  { title: '出单日期', dataIndex: 'issueDate', key: 'issueDate' },
  { title: '操作', key: 'action', customRender: () => h('a', {}, '查看') },
];

// 关联单据数据
const relatedDocuments = ref({
  statements: [
    // 新增对账单数据
    // 示例数据，您可以根据实际情况填充
    {
      id: 'stmt-1',
      key: 'stmt-1',
      statementNo: 'STMT-2023-001',
      period: '2023-10',
      amountDue: 50000,
      amountPaid: 20000,
      status: 'pending',
      issueDate: '2023-11-01',
    },
  ],
  payments: [
    {
      id: '1',
      key: '1',
      docNo: 'PAY-2023-0001',
      type: '预付款',
      amount: 59745,
      status: 'paid',
      createTime: '2023-10-16 10:30:00',
      paymentTime: '2023-10-17 14:20:00',
    },
  ],
  deliveries: [
    {
      id: '1',
      key: '1',
      docNo: 'DEL-2023-0001',
      items: ['传感器 x10', '液压阀门 x5'],
      status: 'delivered',
      shippingDate: '2023-10-18 09:30:00',
      logistics: [
        { provider: '顺丰速运', trackingNo: 'SF********90' },
        { provider: '中通快递', trackingNo: 'ZT0987654321' },
      ],
    },
    {
      id: '2',
      key: '2',
      docNo: 'DEL-2023-0002',
      items: ['伺服电机 x2', '工业控制器 x1'],
      status: 'preparing',
      shippingDate: '2023-10-22 10:00:00',
      logistics: [],
    },
  ],
  returns: [],
  invoices: [
    {
      id: '1',
      key: '1',
      invoiceNo: 'INV-2023-0001',
      type: '增值税专用发票',
      amount: 204150,
      status: 'issued',
      issueDate: '2023-10-25 11:00:00',
      downloadUrl: '/api/download/invoice/INV-2023-0001', // Placeholder URL
    },
  ],
});

// 订单流程步骤
const orderSteps = ref([
  { title: '创建草稿', time: '2023-10-15 09:30:00' },
  { title: '提交订单', time: null },
  { title: '订单确认', time: null },
  { title: '完成订单', time: null },
]);

// 当前订单所处的步骤
const currentStep = computed(() => {
  const stepMap = {
    draft: 0, // 草稿状态对应第一步（索引0）
    pending: 1,
    in_progress: 2,
    completed: 3,
    archived: 4,
  };
  return stepMap[poData.value.status] || 0;
});

// 表格列定义
const productColumns = [
  { title: '物料名称', dataIndex: 'name', key: 'name', width: 180 },
  { title: '型号', dataIndex: 'model', key: 'model', width: 150 },
  { title: '品牌', dataIndex: 'brand', key: 'brand', width: 100 },
  { title: '分类', dataIndex: 'category', key: 'category', width: 120 },
  { title: '来源询价单', dataIndex: 'rfqNo', key: 'rfqNo', width: 150 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 80 },
  { title: '已发货数量', dataIndex: 'shippedQuantity', key: 'shippedQuantity', width: 110 },
  { title: '已收货数量', dataIndex: 'receivedQuantity', key: 'receivedQuantity', width: 110 },
  { title: '已取消数量', dataIndex: 'cancelledQuantity', key: 'cancelledQuantity', width: 110 },
  { title: '物流状态', dataIndex: 'logisticsStatus', key: 'logisticsStatus', width: 100 },
  { title: '财务状态', dataIndex: 'financialStatus', key: 'financialStatus', width: 100 },
  { title: '单价（¥）', dataIndex: 'unitPrice', key: 'unitPrice', width: 100, customRender: ({ text }) => formatCurrency(text) },
  { title: '总价（¥）', dataIndex: 'totalPrice', key: 'totalPrice', width: 120, customRender: ({ text }) => formatCurrency(text) },
  { title: '预计到货日期', dataIndex: 'expectedArrivalTime', key: 'expectedArrivalTime', width: 150 },
  { title: '操作（⚠️看需求）', dataIndex: 'actions', key: 'actions', width: 180, fixed: 'right' },
];

const paymentColumns = [
  { title: '单据编号', dataIndex: 'docNo', key: 'docNo' },
  { title: '付款条件', dataIndex: 'type', key: 'type' },
  { title: '金额(元)', dataIndex: 'amount', key: 'amount', customRender: ({ text }) => formatCurrency(text) },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    customRender: ({ text }) => {
      const statusMap = {
        pending: { text: '待支付', color: 'orange' },
        paid: { text: '已支付', color: 'green' },
        cancelled: { text: '已取消', color: 'red' },
      };
      return h('a-tag', { color: statusMap[text]?.color }, statusMap[text]?.text || text);
    },
  },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
  { title: '支付时间', dataIndex: 'paymentTime', key: 'paymentTime' },
  { title: '操作', key: 'action', customRender: () => h('a', {}, '查看') },
];

const deliveryColumns = [
  { title: '单据编号', dataIndex: 'docNo', key: 'docNo' },
  {
    title: '物品',
    dataIndex: 'items',
    key: 'items',
    customRender: ({ record }) => {
      if (record.items && Array.isArray(record.items) && record.items.length > 0) {
        return h(
          'div',
          {},
          record.items.map((item) => h('div', { style: 'margin-bottom: 4px;' }, item))
        );
      }
      return '无物品信息';
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    customRender: ({ text }) => {
      const statusMap = {
        preparing: { text: '准备中', color: 'blue' },
        shipped: { text: '已发货', color: 'purple' },
        in_transit: { text: '运输中', color: 'orange' },
        delivered: { text: '已送达', color: 'green' },
      };
      return h('a-tag', { color: statusMap[text]?.color }, statusMap[text]?.text || text);
    },
  },
  { title: '发货日期', dataIndex: 'shippingDate', key: 'shippingDate' },
  {
    title: '物流单号',
    key: 'logistics',
    customRender: ({ record }) => {
      if (record.logistics && record.logistics.length > 0) {
        return h(
          'div',
          {},
          record.logistics.map((log) => h('div', `${log.provider || ''}: ${log.trackingNo}`))
        );
      }
      return '暂无物流信息';
    },
  },
  { title: '操作', key: 'action', customRender: () => h('a', {}, '查看') },
];

const returnColumns = [
  { title: '单据编号', dataIndex: 'docNo', key: 'docNo' },
  { title: '物品', dataIndex: 'items', key: 'items' },
  { title: '退货原因', dataIndex: 'reason', key: 'reason' },
  { title: '退货数量', dataIndex: 'quantity', key: 'quantity' },
  { title: '退款金额', dataIndex: 'amount', key: 'amount', customRender: ({ text }) => formatCurrency(text) },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '申请日期', dataIndex: 'applyDate', key: 'applyDate' },
  { title: '处理日期', dataIndex: 'processDate', key: 'processDate' },
  { title: '操作', key: 'action', customRender: () => h('a', {}, '查看') },
];

const attachmentColumns = [
  { title: '文件名', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '大小', dataIndex: 'size', key: 'size' },
  { title: '上传时间', dataIndex: 'uploadTime', key: 'uploadTime' },
  { title: '上传人', dataIndex: 'uploadUser', key: 'uploadUser' },
  { title: '操作', key: 'action', customRender: () => h('a-space', {}, [h('a', {}, '预览'), h('a-divider', { type: 'vertical' }), h('a', {}, '下载')]) },
];

// 权限判断
const canEdit = computed(() => ['draft', 'pending'].includes(poData.value.status));
const canApprove = computed(() => poData.value.status === 'draft');
const canCancel = computed(() => !['cancelled', 'completed', 'archived'].includes(poData.value.status));
const canUpload = computed(() => true);
const canComment = computed(() => true);

// 方法
const goBack = () => {
  router.go(-1);
};

const getStatusText = (status) => {
  const statusMap = {
    draft: '草稿',
    pending: '待确认',
    in_progress: '执行中',
    completed: '已完成',
    cancelled: '已取消',
    cancelling: '取消中',
    archived: '已归档',
    exception: '异常待处理',
  };
  return statusMap[status] || status;
};

const getStatusColor = (status) => {
  const colorMap = {
    draft: 'blue',
    pending: 'orange',
    in_progress: 'purple',
    completed: 'green',
    cancelled: 'red',
    cancelling: 'pink',
    archived: 'gray',
    exception: 'volcano',
  };
  return colorMap[status] || 'default';
};

const getItemStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    preparing: '备货中',
    shipped: '已发货',
    partially_received: '部分收货',
    received: '已收货',
    inspected: '已验收',
    cancelled: '已取消',
  };
  return statusMap[status] || status;
};

const getItemStatusColor = (status) => {
  const colorMap = {
    pending: 'blue',
    preparing: 'purple',
    shipped: 'orange',
    partially_received: 'lime',
    received: 'green',
    inspected: 'green',
    cancelled: 'red',
  };
  return colorMap[status] || 'default';
};

const getLogisticsColor = (status) => {
  const colorMap = {
    shipped: 'blue',
    in_transit: 'orange',
    delivered: 'green',
    exception: 'red',
  };
  return colorMap[status] || 'blue';
};

const formatDate = (dateStr) => {
  if (!dateStr) return '';
  return dateStr;
};

const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  return dateTimeStr;
};

const formatCurrency = (value) => {
  if (value === undefined || value === null) return '0.00';
  return value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

const getTotalQuantity = () => {
  return poData.value.items.reduce((total, item) => total + item.quantity, 0);
};

const showProductDetail = (record) => {
  message.info(`查看物料详情: ${record.name}`);
};

const showProductHistory = (record) => {
  message.info(`查看物料历史: ${record.name}`);
};

const handleUploadChange = (info) => {
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 上传成功`);
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 上传失败`);
  }
};

const addComment = () => {
  if (!newComment.value.trim()) {
    message.warning('请输入评论内容');
    return;
  }

  poData.value.comments.push({
    id: Date.now().toString(),
    userName: '当前用户',
    time: new Date().toLocaleString('zh-CN'),
    content: newComment.value,
  });

  message.success('评论已添加');
  newComment.value = '';
};

// 物料管理方法
const updateItemQuantity = (record, index) => {
  // 更新总价
  record.totalPrice = record.quantity * record.unitPrice;
  
  // 重新计算订单总金额
  const subtotal = poData.value.items.reduce((sum, item) => sum + item.totalPrice, 0);
  poData.value.subtotalAmount = subtotal;
  poData.value.totalAmount = subtotal + poData.value.shippingFee;
  
  message.success(`已更新${record.name}的数量为${record.quantity}`);
};

const deleteProduct = (record, index) => {
  poData.value.items.splice(index, 1);
  
  // 重新计算订单总金额
  const subtotal = poData.value.items.reduce((sum, item) => sum + item.totalPrice, 0);
  poData.value.subtotalAmount = subtotal;
  poData.value.totalAmount = subtotal + poData.value.shippingFee;
  
  message.success(`已删除物料: ${record.name}`);
};

const cancelProduct = (record) => {
  // 对于非草稿状态的订单，取消物料而不是删除
  if (poData.value.status !== 'draft') {
    record.status = 'cancelled';
    record.cancelledQuantity = record.quantity;
    message.success(`已取消物料: ${record.name}`);
  }
};

// 收货信息选择方法
const showReceiverModal = () => {
  receiverModalVisible.value = true;
};

const onReceiverSelectChange = (selectedKeys) => {
  selectedReceiverKeys.value = selectedKeys;
};

const handleReceiverSelect = () => {
  if (selectedReceiverKeys.value.length === 0) {
    message.warning('请选择收货信息');
    return;
  }
  
  const selectedReceiver = receiverList.value.find(item => item.id === selectedReceiverKeys.value[0]);
  if (selectedReceiver) {
    poData.value.receiverName = selectedReceiver.name;
    poData.value.receiverPhone = selectedReceiver.phone;
    poData.value.receiverRegion = selectedReceiver.region;
    poData.value.receiverAddress = selectedReceiver.address;
    
    message.success(`已选择收货信息: ${selectedReceiver.name}`);
  }
  
  receiverModalVisible.value = false;
  selectedReceiverKeys.value = [];
};

const downloadInvoice = (record) => {
  message.info(`开始下载发票: ${record.invoiceNo}`);
  // In a real application, you would trigger a file download here,
  // possibly using window.open(record.downloadUrl) or a library function.
  console.log('Download invoice from:', record.downloadUrl);
  // Simulate download link click
  const link = document.createElement('a');
  link.href = record.downloadUrl || '#'; // Use placeholder or actual URL
  link.setAttribute('download', `${record.invoiceNo}.pdf`); // Suggest filename
  link.style.display = 'none';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const fetchPoDetails = async () => {
  loading.value = true;
  try {
    // 实际应用中需要调用API获取订单详情
    // const response = await api.getPurchaseOrderDetail(id.value);
    // poData.value = response.data;

    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 这里使用的是假数据，实际应用中应该从API获取
    loading.value = false;
  } catch (error) {
    message.error('获取订单详情失败');
    loading.value = false;
  }
};

// 生命周期钩子
onMounted(() => {
  // 添加新的物流记录数据结构
  // poData.value.logisticsRecords = [
  //   {
  //     id: '1',
  //     provider: '顺丰速运',
  //     trackingNo: 'SF********90',
  //     shippingTime: '2023-10-18 14:30:00',
  //     estimatedDelivery: '2023-10-21',
  //     contents: '传感器 x10, 液压阀门 x5',
  //     trackingInfo: [
  //       {
  //         time: '2023-10-18 14:30:00',
  //         status: 'shipped',
  //         description: '物品已从供应商仓库发出',
  //         location: '广州发货中心',
  //       },
  //       {
  //         time: '2023-10-19 09:15:00',
  //         status: 'in_transit',
  //         description: '物品运输中，预计3天后到达',
  //         location: '广州转运中心',
  //       },
  //       {
  //         time: '2023-10-20 16:45:00',
  //         status: 'delivered',
  //         description: '物品已送达目的地',
  //         location: '北京海淀区',
  //       },
  //     ],
  //   },
  //   {
  //     id: '2',
  //     provider: '中通快递',
  //     trackingNo: 'ZT9876543210',
  //     shippingTime: '2023-10-19 10:20:00',
  //     estimatedDelivery: '2023-10-22',
  //     contents: '工业控制器 x2',
  //     trackingInfo: [
  //       {
  //         time: '2023-10-19 10:20:00',
  //         status: 'shipped',
  //         description: '包裹已交付快递公司',
  //         location: '上海发货中心',
  //       },
  //       {
  //         time: '2023-10-20 08:30:00',
  //         status: 'in_transit',
  //         description: '包裹正在运输中',
  //         location: '上海转运中心',
  //       },
  //       {
  //         time: '2023-10-21 14:50:00',
  //         status: 'in_transit',
  //         description: '包裹已到达目的地城市',
  //         location: '北京转运中心',
  //       },
  //     ],
  //   },
  // ];

  fetchPoDetails();
});

// 开票状态颜色和文本
const getInvoicingStatusColor = (status) => {
  const map = {
    non_invoiceable: 'default',
    invoiceable: 'blue',
    invoicing: 'orange',
    invoiced: 'green',
  };
  return map[status] || 'default';
};

const getInvoicingStatusText = (status) => {
  const map = {
    non_invoiceable: '不可开票',
    invoiceable: '可开票',
    invoicing: '开票中',
    invoiced: '已开票',
  };
  return map[status] || '未知';
};

// 付款状态进度条相关函数
const getPaymentProgress = (status) => {
  const progressMap = {
    not_applicable: 0,
    pending_payment: 0,
    partially_paid: 50,
    paid: 100,
    refunding: 75,
    partially_refunded: 50,
    refunded: 0,
  };
  return progressMap[status] || 0;
};

const getPaymentProgressStatus = (status) => {
  if (status === 'paid') return 'success';
  if (status === 'refunding' || status === 'partially_refunded') return 'exception';
  if (status === 'refunded') return 'normal';
  if (status === 'pending_payment') return 'exception';
  return 'active';
};

const getPaymentProgressColor = (status) => {
  const colorMap = {
    not_applicable: '#d9d9d9',
    pending_payment: '#ff4d4f',
    partially_paid: '#faad14',
    paid: '#52c41a',
    refunding: '#722ed1',
    partially_refunded: '#13c2c2',
    refunded: '#1890ff',
  };
  return colorMap[status] || '#1890ff';
};
</script>

<style lang="less" scoped>
.po-detail-container {
  min-height: 100vh;
}

.po-progress-section {
  margin-bottom: 24px;
}

.detail-card {
  margin-bottom: 24px;
  border-radius: 4px;

  .ant-card-head {
    background-color: #fafafa;
  }
}

.info-item {
  display: flex;
  margin-bottom: 16px;

  .label {
    color: rgba(0, 0, 0, 0.65);
    min-width: 90px;
    flex-shrink: 0;
  }

  .value {
    flex: 1;
    font-weight: 500;

    &.important {
      color: #f94c30;
      font-weight: 600;
    }
  }
}

.summary-section {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px dashed #e8e8e8;

  .summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 0 24px;

    .label {
      color: rgba(0, 0, 0, 0.65);
    }

    .value {
      font-weight: 500;

      &.important {
        color: #f5222d;
        font-weight: 600;
        font-size: 16px;
      }
    }

    &.total {
      font-size: 16px;
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid #e8e8e8;
    }
  }
}

.logistics-card {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.tracking-header {
  font-weight: 500;
  margin-bottom: 12px;
  padding-left: 4px;
  border-left: 3px solid #1890ff;
}

.logistics-time {
  font-weight: 500;
  margin-bottom: 4px;
}

.logistics-info {
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 4px;
}

.logistics-operator {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;

  .comment-user {
    font-weight: 500;
  }

  .comment-time {
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
  }
}

.comment-content {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 8px;
}

.comment-form {
  display: flex;
  align-items: flex-start;
  margin-top: 16px;
}

.log-time {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  margin-bottom: 4px;
}

.log-content {
  margin-bottom: 4px;

  .log-user {
    font-weight: 500;
    margin-right: 8px;
  }
}

.log-detail {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
}

.bottom-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  margin-top: 24px;
  border-top: 1px solid #e8e8e8;
}
</style>
