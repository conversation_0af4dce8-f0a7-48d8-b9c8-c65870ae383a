<template>
  <div class="po-container">
    <!-- 搜索区域 -->
    <div class="search-area">

      <a-form style="display: block;" layout="inline" :model="searchForm">
        <a-row>
          <template v-for="field in visibleSearchFields" :key="field.key">
            <a-col :span="4">
              <a-form-item :label="field.label">
                <!-- 输入框 -->
                <a-input v-if="field.type === 'input'" v-model:value="searchForm[field.key]" :placeholder="`请输入${field.label}`" />

                <!-- 下拉选择框 -->
                <a-select v-else-if="field.type === 'select'" v-model:value="searchForm[field.key]" :placeholder="`请选择${field.label}`" style="width: 100%" allowClear>
                  <a-select-option v-for="item in field.options" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>

                <!-- 日期范围选择器 -->
                <a-range-picker v-else-if="field.type === 'dateRange'" v-model:value="searchForm[field.key]" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
          </template>

          <a-col :span="4" class="search-buttons">
            <a-space>
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button @click="handleReset">重置</a-button>
              <a-button type="link" @click="toggleSearchConfig">
                <setting-outlined />
                配置搜索项
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>

      <!-- 搜索字段配置抽屉 -->
      <a-drawer title="配置搜索项" placement="right" :visible="showSearchConfig" @close="toggleSearchConfig" width="400px">
        <a-checkbox-group v-model:value="selectedSearchFields" @change="handleSearchFieldsChange">
          <a-row>
            <a-col :span="12" v-for="field in allSearchFields" :key="field.key">
              <a-checkbox :value="field.key">{{ field.label }}</a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </a-drawer>
    </div>

    <!-- 表格区域 -->
    <div class="view-selector">
      <a-button-group>
        <a-button :type="viewMode === 'order' ? 'primary' : 'default'" @click="switchViewMode('order')">
          <ordered-list-outlined /> 订单视图
        </a-button>
        <a-button :type="viewMode === 'product' ? 'primary' : 'default'" @click="switchViewMode('product')">
          <appstore-outlined /> 物料视图
        </a-button>
        
      </a-button-group>
    </div>

    <!-- 物料视图表格 -->
    <material-table
      v-if="viewMode === 'product'"
      :tableData="tableData"
      :loading="loading"
      :pagination="pagination"
      @tableChange="handleTableChange"
      @selectChange="onSelectChange"
      @export="handleExport"
      @print="handlePrint"
      @viewDetail="handleViewDetail"
      @edit="handleEdit"
      @cancel="handleCancel"
      @delete="handleDelete"
      @columnsChange="handleMaterialColumnsChange"
    />

    <!-- 订单视图表格 -->
    <order-table
      v-else
      :tableData="tableData"
      :loading="loading"
      :pagination="pagination"
      @tableChange="handleTableChange"
      @selectChange="onSelectChange"
      @export="handleExport"
      @print="handlePrint"
      @viewDetail="handleViewDetail"
      @edit="handleEdit"
      @cancel="handleCancel"
      @delete="handleDelete"
      @columnsChange="handleOrderColumnsChange"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { UpOutlined, DownOutlined, SettingOutlined, ExportOutlined, AppstoreOutlined, PrinterOutlined, OrderedListOutlined } from '@ant-design/icons-vue';
import MaterialTable from './component/materialTable.vue';
import OrderTable from './component/orderTable.vue';

// 搜索配置
const showSearchConfig = ref(false);
const toggleSearchConfig = () => {
  showSearchConfig.value = !showSearchConfig.value;
};

// 所有可能的搜索字段
const allSearchFields = [
  { key: 'soNo', label: '采购订单号', type: 'input' },
  { key: 'materialName', label: '物料名称', type: 'input' },
  { key: 'materialModel', label: '物料型号', type: 'input' },
  { key: 'createTimeRange', label: '下单时间', type: 'dateRange' },
  {
    key: 'status',
    label: '订单状态',
    type: 'select',
    options: [
      { label: '未提交', value: 'not_submitted' },
      { label: '待确认', value: 'pending_confirmation' },
      { label: '执行中', value: 'in_progress' },
      { label: '已完成', value: 'completed' },
      { label: '取消中', value: 'cancelling' },
      { label: '已取消', value: 'cancelled' },
    ],
  },
  { key: 'creator', label: '采购员', type: 'input' },
  {
    key: 'category',
    label: '物料分类',
    type: 'select',
    options: [
      { label: '电子元件', value: 'electronics' },
      { label: '机械零件', value: 'mechanical' },
      { label: '原材料', value: 'raw' },
    ],
  },
  { key: 'brand', label: '品牌', type: 'input' },
];

// 当前选中的搜索字段
const selectedSearchFields = ref(['soNo', 'status', 'createTimeRange']);

// 可见的搜索字段
const visibleSearchFields = computed(() => {
  return allSearchFields.filter((field) => selectedSearchFields.value.includes(field.key));
});

// 搜索字段变更处理
const handleSearchFieldsChange = (checkedValues) => {
  selectedSearchFields.value = checkedValues;
};

// 搜索表单
const searchForm = reactive({
  soNo: '',
  materialName: '',
  materialModel: '',
  createTimeRange: [],
  status: undefined,
  creator: '',
  category: undefined,
  brand: '',
});

// 搜索方法
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

// 重置查询条件
const handleReset = () => {
  Object.keys(searchForm).forEach((key) => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = [];
    } else {
      searchForm[key] = undefined;
    }
  });
  handleSearch();
};

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 视图模式
const viewMode = ref('order'); // 'product' 或 'order'

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
};

// 选中行状态
const selectedRowKeys = ref([]);

// 选中行变化处理
const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys;
};

// 切换视图模式
const switchViewMode = (mode) => {
  viewMode.value = mode;
  fetchData();
};

// 列变更处理
const handleMaterialColumnsChange = (columns) => {
  console.log('Material columns changed:', columns);
};

const handleOrderColumnsChange = (columns) => {
  console.log('Order columns changed:', columns);
};

// Helper function to format date for soNo
const formatDateForSoNo = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  const milliseconds = date.getMilliseconds().toString().padStart(3, '0');
  return `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;
};

// Helper function to format date to YYYY-MM-DD HH:mm
const formatDateTime = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 获取数据
const fetchData = () => {
  loading.value = true;

  // 模拟数据请求，实际项目中请替换为真实API调用
  setTimeout(() => {
    // 扁平化数据结构 - 每个物料作为一行
    const flattenedData = [];
    // 订单数据结构 - 按订单分组
    const orderData = [];

    // 模拟10个采购单
    Array.from({ length: 10 }).forEach((_, orderIndex) => {
      // 订单状态按照业务要求定义
      const statusArray = ['not_submitted', 'pending_confirmation', 'in_progress', 'completed', 'cancelling', 'cancelled'];
      const orderStatus = statusArray[orderIndex % statusArray.length];
      
      // 根据订单状态确定开票和付款状态
      let invoicingStatus = 'non_invoiceable';
      let paymentStatus = 'not_applicable';

      const createDate = new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000); // Random creation date within last 10 days
      let endDate = null;

      if (['in_progress', 'completed'].includes(orderStatus)) {
        invoicingStatus = ['invoiceable', 'invoicing', 'invoiced'][orderIndex % 3];
      }
      if (orderStatus === 'completed') {
        paymentStatus = 'paid';
      } else if (orderStatus === 'in_progress') {
        paymentStatus = ['pending_payment', 'partially_paid'][orderIndex % 2];
      } else if (orderStatus === 'cancelled') {
        paymentStatus = ['refunded', 'partially_refunded'][orderIndex % 2];
      }

      if (['completed', 'cancelled'].includes(orderStatus)) {
        endDate = new Date(createDate.getTime() + Math.random() * 5 * 24 * 60 * 60 * 1000); // Random end date within 5 days of creation
      }

      // 订单基本信息
      const orderInfo = {
        id: `order-${orderIndex}`,
        soNo: orderStatus === 'not_submitted' ? `SO-DRAFT-${2000 + orderIndex}` : `SO-2023-${2000 + orderIndex}`,
        rfqNo: `RFQ-2023-${2000 + orderIndex}`,
        status: orderStatus,
        invoicingStatus: invoicingStatus,
        paymentStatus: paymentStatus,
        creator: `管理员${(orderIndex % 3) + 1}`,
        orderTime: '2023-07-01', // This seems to be a static value, might need to be dynamic if it represents order creation time
        createTime: orderStatus === 'not_submitted' ? null : formatDateTime(createDate),
        endTime: endDate ? formatDateTime(endDate) : null,
        actualDelivery: orderStatus === 'completed' ? '2023-08-13' : null,
        totalAmount: 0,
        materialCount: 0,
        materialItems: []
      };

      // 每个采购单包含1-5个物料
      const materialCount = Math.floor(Math.random() * 5) + 1;
      
      // 物料数量总和将在循环中计算
      orderInfo.materialCount = 0;

      // 生成物料并添加到扁平数据中
      Array.from({ length: materialCount }).forEach((_, mIndex) => {
        const quantity = Math.floor(Math.random() * 100) + 1;
        const unitPrice = parseFloat((Math.random() * 1000 + 100).toFixed(2));
        const totalPrice = parseFloat((quantity * unitPrice).toFixed(2));
        
        // 计算订单总金额
        orderInfo.totalAmount += totalPrice;
        
        // 累加订单中的物料总数量
        orderInfo.materialCount += quantity;
        
        // 根据订单状态确定物料数量分布
        let preparingQuantity = 0;
        let inTransitQuantity = 0;
        let acceptedQuantity = 0;
        let cancelledQuantity = 0;
        
        // 物料物流状态
        const logisticsStatusOptions = ['备货中', '待发货', '部分发货', '部分收货', '退货中', '已收货'];
        const materialLogisticsStatus = logisticsStatusOptions[mIndex % logisticsStatusOptions.length];

        // 物料财务状态
        const financialStatusOptions = ['未对账', '部分对账', '部分付款', '已支付'];
        const materialFinancialStatus = financialStatusOptions[mIndex % financialStatusOptions.length];

        // 根据业务规则设置数量
        if (orderStatus === 'in_progress') {
          // 执行中订单: 数量分配到备货中、在途和已验收
          const remainingQuantity = quantity;
          preparingQuantity = Math.floor(remainingQuantity * 0.4);
          inTransitQuantity = Math.floor(remainingQuantity * 0.3);
          acceptedQuantity = remainingQuantity - preparingQuantity - inTransitQuantity;
        } else if (orderStatus === 'completed') {
          // 已完成订单: 全部已验收
          acceptedQuantity = quantity;
        } else if (orderStatus === 'cancelling') {
          // 取消中订单: 部分已取消，部分可能在其他状态
          cancelledQuantity = Math.floor(quantity * 0.7);
          preparingQuantity = Math.floor(quantity * 0.1);
          inTransitQuantity = Math.floor(quantity * 0.1);
          acceptedQuantity = quantity - cancelledQuantity - preparingQuantity - inTransitQuantity;
        } else if (orderStatus === 'cancelled') {
          // 已取消订单: 全部已取消
          cancelledQuantity = quantity;
        }
        // 未提交和待确认的订单数量都为0（默认值）

        // 只有执行中和取消中的订单才有预计到货时间
        const expectedArrivalTimeRaw = ['in_progress', 'cancelling'].includes(orderStatus) ? new Date(Date.now() + Math.random() * 10 * 24 * 60 * 60 * 1000) : null;
        const expectedArrivalTime = expectedArrivalTimeRaw ? formatDateTime(expectedArrivalTimeRaw) : null;

        const materialItem = {
          id: `material-${orderIndex}-${mIndex}`,
          soNo: orderInfo.soNo,
          name: `测试物料 ${mIndex + 1}`,
          model: `MODEL-${1000 + mIndex}`,
          brand: mIndex % 3 === 0 ? '品牌A' : mIndex % 3 === 1 ? '品牌B' : '品牌C',
          category: mIndex % 2 === 0 ? '电子元件' : '机械零件',
          unit: '个',
          quantity: quantity,
          unitPrice: unitPrice,
          totalPrice: totalPrice,
          rfqNo: `RFQ-2023-${2000 + mIndex}`,
          status: orderStatus,
          preparingQuantity: preparingQuantity,
          inTransitQuantity: inTransitQuantity,
          acceptedQuantity: acceptedQuantity,
          cancelledQuantity: cancelledQuantity,
          creator: orderInfo.creator,
          orderTime: orderInfo.orderTime,
          expectedArrivalTime: expectedArrivalTime,
          actualDelivery: orderInfo.actualDelivery,
          remark: mIndex % 2 === 0 ? '特殊规格' : '',
          logisticsStatus: materialLogisticsStatus,
          financialStatus: materialFinancialStatus,
        };

        flattenedData.push(materialItem);
        orderInfo.materialItems.push(materialItem);
      });

      // 格式化总金额
      orderInfo.totalAmount = parseFloat(orderInfo.totalAmount.toFixed(2));
      orderData.push(orderInfo);
    });

    // 根据当前视图模式设置表格数据
    if (viewMode.value === 'product') {
      tableData.value = flattenedData;
    } else {
      tableData.value = orderData;
    }
    
    pagination.total = (viewMode.value === 'product' ? flattenedData.length : orderData.length) * 10; // 模拟总数据量
    loading.value = false;
  }, 500);
};

// 操作方法
const handleViewDetail = (record) => {
  console.log('查看采购单详情', record);
};

const handleEdit = (record) => {
  console.log('编辑采购单', record);
};

const handleCancel = (record) => {
  console.log('取消采购单', record);
};

const handleDelete = (record) => {
  console.log('删除采购单', record);
};

const handleExport = () => {
  console.log('导出采购单数据');
};

const handlePrint = () => {
  console.log('打印采购单');
};

// 初始化
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.search-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.view-selector {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .search-buttons {
    justify-content: flex-start;
    margin-top: 12px;
  }
}
</style>
