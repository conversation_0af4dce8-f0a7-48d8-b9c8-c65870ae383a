<template>
  <div class="invoice-detail-container">
    <a-page-header 
      :title="'对账单详情 - ' + invoiceData.invoiceNo" 
      :sub-title="'状态：' + getStatusText(invoiceData.status)" 
      @back="goBack"
    >
      <template #extra>
        <a-space>
          <a-button type="primary" v-if="canConfirm">确认对账单</a-button>
          <a-button type="primary" v-if="canSettle">全部结算</a-button>
          <a-button v-if="canEdit">编辑</a-button>
        </a-space>
      </template>
    </a-page-header>

    <!-- 对账流程 -->
    <div class="invoice-progress-section">
      <a-card title="对账流程">
        <a-steps :current="currentStep" size="small">
          <a-step v-for="(step, index) in invoiceSteps" :key="index" :title="step.title">
            <template #description>
              <div v-if="step.time">{{ formatDateTime(step.time) }}</div>
            </template>
          </a-step>
        </a-steps>
      </a-card>
    </div>

    <!-- 基本信息 -->
    <a-card title="基本信息" class="detail-card">
      <a-descriptions bordered :column="3" size="middle">
        <a-descriptions-item label="对账单号">{{ invoiceData.invoiceNo }}</a-descriptions-item>
        <a-descriptions-item label="付款条件">
          <a-tag :color="getPaymentTermsColor(invoiceData.paymentTerms)">{{ getPaymentTermsText(invoiceData.paymentTerms) }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="对账周期" v-if="invoiceData.paymentTerms === 'account_settlement'">{{ displayReconciliationPeriod }}</a-descriptions-item>
        <a-descriptions-item label="总金额">
          <span class="amount-text">¥{{ formatAmount(invoiceData.totalAmount) }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(invoiceData.status)">{{ getStatusText(invoiceData.status) }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="开票状态">
          <a-tag :color="getBillingStatusColor(invoiceData.billingStatus)">{{ getBillingStatusText(invoiceData.billingStatus) }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ formatDateTime(invoiceData.createTime) }}</a-descriptions-item>
        <!-- <a-descriptions-item label="供应商">{{ invoiceData.supplierName }}</a-descriptions-item> -->
        <!-- <a-descriptions-item label="创建人">{{ invoiceData.creatorName }}</a-descriptions-item> -->
        <a-descriptions-item label="对账时间" v-if="invoiceData.confirmTime">{{ formatDateTime(invoiceData.confirmTime) }}</a-descriptions-item>
      </a-descriptions>
    </a-card>

    <!-- 物料信息 -->
    <a-card title="物料信息" class="detail-card">
      <!-- 查询功能 -->
      <div class="search-section">
        <a-row :gutter="16" style="margin-bottom: 16px;">
          <a-col :span="4">
            <a-input 
              v-model:value="searchParams.materialName" 
              placeholder="物料名称" 
              @change="handleSearch"
              allowClear
            />
          </a-col>
          <a-col :span="4">
            <a-input 
              v-model:value="searchParams.model" 
              placeholder="型号" 
              @change="handleSearch"
              allowClear
            />
          </a-col>
          <a-col :span="4">
            <a-input 
              v-model:value="searchParams.receiptNo" 
              placeholder="收货单号" 
              @change="handleSearch"
              allowClear
            />
          </a-col>
          <a-col :span="4">
            <a-input 
              v-model:value="searchParams.poNo" 
              placeholder="采购单号" 
              @change="handleSearch"
              allowClear
            />
          </a-col>
          <a-col :span="4">
            <a-range-picker 
              v-model:value="searchParams.receiptDateFrom" 
              :placeholder="['收货日期起', '收货日期至']"
              @change="handleSearch"
              style="width: 100%;"
            />
          </a-col>
          <a-col :span="4">
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="resetSearch" style="margin-left: 8px;">重置</a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 物料表格 -->
      <a-table 
        :columns="materialColumns" 
        :data-source="filteredMaterials" 
        :pagination="materialPagination" 
        size="middle"
        @change="handleTableChange"
        style="width: 100%;"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'unitPrice'">
            ¥{{ formatAmount(record.unitPrice) }}
          </template>
          <template v-if="column.key === 'totalPrice'">
            ¥{{ formatAmount(record.totalPrice) }}
          </template>
          <template v-if="column.key === 'receiptDate'">
            {{ formatDate(record.receiptDate) }}
          </template>
          <template v-if="column.key === 'receiptNo'">
            <a-button type="link" @click="viewReceiptOrderDetail(record.receiptNo)" style="padding: 0;">
              {{ record.receiptNo }}
            </a-button>
          </template>
          <template v-if="column.key === 'poNo'">
            <a-button type="link" @click="viewPurchaseOrderByNo(record.poNo)" style="padding: 0;">
              {{ record.poNo }}
            </a-button>
          </template>
        </template>
        <template #summary>
          <a-table-summary-row>
            <a-table-summary-cell :index="0" :col-span="5">
              <strong>合计</strong>
            </a-table-summary-cell>
            <a-table-summary-cell :index="5">
              <strong class="summary-total">{{ getTotalQuantity() }}</strong>
            </a-table-summary-cell>
            <a-table-summary-cell :index="6"></a-table-summary-cell>
            <a-table-summary-cell :index="7">
              <strong class="summary-total">¥{{ formatAmount(getTotalAmount()) }}</strong>
            </a-table-summary-cell>
            <a-table-summary-cell :index="8" :col-span="3"></a-table-summary-cell>
          </a-table-summary-row>
        </template>
      </a-table>
    </a-card>

    <!-- 相关单据 -->
    <a-card title="相关单据" class="detail-card">
      <a-tabs default-active-key="po">
        <a-tab-pane key="po" tab="采购单">
          <a-table 
            :columns="purchaseOrderColumns" 
            :data-source="invoiceData.relatedPurchaseOrders" 
            :pagination="false" 
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'orderNo'">
                <a-button type="link" @click="viewPurchaseOrderDetail(record.id)" style="padding: 0;">
                  {{ record.orderNo }}
                </a-button>
              </template>
              <template v-if="column.key === 'totalAmount'">
                ¥{{ formatAmount(record.totalAmount) }}
              </template>
              <template v-if="column.key === 'reconciliationAmount'">
                ¥{{ formatAmount(record.reconciliationAmount) }}
              </template>
            </template>
          </a-table>
          <a-empty v-if="!invoiceData.relatedPurchaseOrders || invoiceData.relatedPurchaseOrders.length === 0" description="暂无关联采购单" />
        </a-tab-pane>

        <a-tab-pane key="payment" tab="付款单">
          <a-table 
            :columns="paymentOrderColumns" 
            :data-source="invoiceData.relatedPaymentOrder ? [invoiceData.relatedPaymentOrder] : []" 
            :pagination="false" 
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'paymentAmount'">
                ¥{{ formatAmount(record.paymentAmount) }}
              </template>
              <template v-if="column.key === 'paymentDate'">
                {{ formatDate(record.paymentDate) }}
              </template>
              <template v-if="column.key === 'action'">
                <a-button type="link" @click="viewPaymentOrderDetail(record.id)">查看详情</a-button>
              </template>
            </template>
          </a-table>
          <a-empty v-if="!invoiceData.relatedPaymentOrder" description="暂无关联付款单" />
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 附件和备注 -->
    <a-card title="附件与备注" class="detail-card">
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" tab="附件资料">
          <!-- <a-upload-dragger
            v-if="canUpload"
            name="file"
            :multiple="true"
            action="/api/upload"
            @change="handleUploadChange"
          >
            <p class="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此处上传</p>
            <p class="ant-upload-hint">支持单个或批量上传。严禁上传公司内部资料及其他违禁文件。</p>
          </a-upload-dragger> -->
          <a-table 
            :columns="attachmentColumns" 
            :data-source="invoiceData.attachments" 
            :pagination="false" 
            size="small" 
            style="margin-top: 16px;"
          >
            <template #bodyCell="{ column }">
              <template v-if="column.key === 'action'">
                <a-space>
                  <a>预览</a>
                  <a>下载</a>
                  <a-popconfirm title="确定删除吗?" @confirm="() => {}">
                    <a style="color: red">删除</a>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="2" tab="备注信息">
          <a-textarea 
            v-model:value="invoiceData.remarks" 
            placeholder="请输入备注信息" 
            :rows="4" 
            :disabled="!canEdit"
          />
        </a-tab-pane>
        
        <a-tab-pane key="3" tab="操作历史">
          <a-list item-layout="horizontal" :data-source="invoiceData.operationLogs" size="small">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <span class="log-user">{{ item.userName }}</span> {{ item.action }}
                  </template>
                  <template #description>
                    <div class="log-time">{{ formatDateTime(item.time) }}</div>
                    <div v-if="item.detail" class="log-detail">{{ item.detail }}</div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { InboxOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

const route = useRoute();
const router = useRouter();
const id = ref(route.params.id || route.query.id);
const loading = ref(false);

// 搜索参数
const searchParams = reactive({
  materialName: '',
  model: '',
  receiptNo: '',
  poNo: '',
  receiptDateFrom: null,
  receiptDateTo: null,
});

// 对账单数据
const invoiceData = ref({
  id: '',
  invoiceNo: 'INV-2023-0001',
  reconciliationPeriod: '2023-10-01 ~ 2023-10-31',
  totalAmount: 125000,
  status: 'pending', // pending, confirmed, settled
  billingStatus: 'invoicing', // not_invoiced, invoicing, invoiced
  paymentTerms: 'account_settlement', // prepayment(预付款), account_settlement(账期结算)
  // 注意：当 paymentTerms 设置为 'prepayment' 时，对账周期字段将不显示
  createTime: '2023-10-25 10:00:00',
  confirmTime: '2023-10-26 14:30:00',
  settlementTime: null,
  supplierName: 'ABC供应商',
  creatorName: '张三',
  confirmer: '李四',
  remarks: '本期对账单包含10月份全部收货物料，请核对无误后确认。',
  materials: [
    {
      id: '1',
      key: '1',
      materialName: '伺服电机',
      model: 'SM2000',
      quantity: 5,
      unitPrice: 8000,
      totalPrice: 40000,
      receiptDate: '2023-10-15',
      receiptNo: '****************',
      poNo: '****************',
    },
    {
      id: '2',
      key: '2',
      materialName: '工业控制器',
      model: 'IC5000',
      quantity: 3,
      unitPrice: 15000,
      totalPrice: 45000,
      receiptDate: '2023-10-18',
      receiptNo: '****************',
      poNo: '****************',
    },
    {
      id: '3',
      key: '3',
      materialName: '传感器',
      model: 'SE100',
      quantity: 20,
      unitPrice: 500,
      totalPrice: 10000,
      receiptDate: '2023-10-20',
      receiptNo: 'DN20231020141113',
      poNo: 'SO20231020141113',
    },
    {
      id: '4',
      key: '4',
      materialName: '电机驱动器',
      model: 'DR200',
      quantity: 8,
      unitPrice: 3750,
      totalPrice: 30000,
      receiptDate: '2023-10-22',
      receiptNo: 'DN20231022141114',
      poNo: 'SO20231022141114',
    },
  ],
  attachments: [
    { 
      id: 'att-1', 
      name: '对账单明细.xlsx', 
      type: 'xlsx', 
      size: '2.5MB', 
      uploadTime: '2023-10-25 11:00:00', 
      uploadUser: '张三' 
    }
  ],
  operationLogs: [
    { id: 'log-1', userName: '张三', time: '2023-10-25 10:00:00', action: '生成对账单' },
    { id: 'log-2', userName: '李四', time: '2023-10-26 14:30:00', action: '确认对账单', detail: '已核对物料清单，确认无误' },
  ],
  relatedPurchaseOrders: [
    { id: 'po-1', orderNo: 'SO20231001100000', orderDate: '2023-10-01', endDate: '2023-10-15', totalAmount: 40000, reconciliationAmount: 35000, status: '已完成' },
    { id: 'po-2', orderNo: 'SO20231005143000', orderDate: '2023-10-05', endDate: '2023-10-18', totalAmount: 45000, reconciliationAmount: 45000, status: '已完成' },
    { id: 'po-3', orderNo: 'SO20231008090000', orderDate: '2023-10-08', endDate: '2023-10-20', totalAmount: 10000, reconciliationAmount: 8000, status: '执行中' },
    { id: 'po-4', orderNo: 'SO20231012160000', orderDate: '2023-10-12', endDate: '2023-10-22', totalAmount: 30000, reconciliationAmount: 30000, status: '已完成' },
  ],
  relatedPaymentOrder: { id: 'pay-1', paymentNo: 'PAY-2023-001', paymentAmount: 125000, paymentDate: '2023-10-27', status: '已付款' },
  stepTimes: {
    generation: '2023-10-25 10:00:00',
    confirmation: '2023-10-26 14:30:00',
    settlement: null,
  }
});

// 对账流程步骤
const invoiceSteps = computed(() => [
  { title: '待对账', time: invoiceData.value.stepTimes?.generation },
  { title: '已锁定', time: invoiceData.value.stepTimes?.confirmation },
  { title: '已结算', time: invoiceData.value.stepTimes?.settlement },
]);

// 当前步骤
const currentStep = computed(() => {
  const stepMap = {
    pending: 0,
    confirmed: 1,
    settled: 2,
  };
  return stepMap[invoiceData.value.status] ?? 0;
});

// 对账周期显示（当付款条件为预付款时不显示）
const displayReconciliationPeriod = computed(() => {
  return invoiceData.value.paymentTerms === 'account_settlement' ? invoiceData.value.reconciliationPeriod : '';
});

// 筛选后的物料数据
const filteredMaterials = computed(() => {
  let materials = invoiceData.value.materials;
  
  if (searchParams.materialName) {
    materials = materials.filter(item => 
      item.materialName.toLowerCase().includes(searchParams.materialName.toLowerCase())
    );
  }
  
  if (searchParams.model) {
    materials = materials.filter(item => 
      item.model.toLowerCase().includes(searchParams.model.toLowerCase())
    );
  }
  
  if (searchParams.receiptNo) {
    materials = materials.filter(item => 
      item.receiptNo.toLowerCase().includes(searchParams.receiptNo.toLowerCase())
    );
  }
  
  if (searchParams.poNo) {
    materials = materials.filter(item => 
      item.poNo.toLowerCase().includes(searchParams.poNo.toLowerCase())
    );
  }
  
  if (searchParams.receiptDateFrom && searchParams.receiptDateTo) {
    const fromDate = dayjs(searchParams.receiptDateFrom);
    const toDate = dayjs(searchParams.receiptDateTo);
    materials = materials.filter(item => {
      const receiptDate = dayjs(item.receiptDate);
      return receiptDate.isAfter(fromDate.subtract(1, 'day')) && receiptDate.isBefore(toDate.add(1, 'day'));
    });
  }
  
  return materials;
});

// 分页配置
const materialPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
});

// 更新分页总数
onMounted(() => {
  materialPagination.value.total = filteredMaterials.value.length;
});

// 物料表格列定义
const materialColumns = [
  { title: '物料名称', dataIndex: 'materialName', key: 'materialName', fixed: 'left' },
  { title: '型号', dataIndex: 'model', key: 'model', },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 80, align: 'right' },
  { title: '单价', dataIndex: 'unitPrice', key: 'unitPrice', width: 120, align: 'right' },
  { title: '总价', dataIndex: 'totalPrice', key: 'totalPrice', width: 120, align: 'right' },
  { title: '收货日期', dataIndex: 'receiptDate', key: 'receiptDate', width: 120 },
  { title: '收货单号', dataIndex: 'receiptNo', key: 'receiptNo' },
  { title: '采购单号', dataIndex: 'poNo', key: 'poNo'},
];

// 采购单表格列定义
const purchaseOrderColumns = [
  { title: '采购单号', dataIndex: 'orderNo', key: 'orderNo' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '下单时间', dataIndex: 'orderDate', key: 'orderDate' },
  { title: '结束时间', dataIndex: 'endDate', key: 'endDate' },
  { title: '总金额', dataIndex: 'totalAmount', key: 'totalAmount', align: 'right' },
  { title: '本次对账金额', dataIndex: 'reconciliationAmount', key: 'reconciliationAmount', align: 'right' },
];

// 付款单表格列定义
const paymentOrderColumns = [
  { title: '付款单号', dataIndex: 'paymentNo', key: 'paymentNo' },
  { title: '付款金额', dataIndex: 'paymentAmount', key: 'paymentAmount', align: 'right' },
  { title: '付款日期', dataIndex: 'paymentDate', key: 'paymentDate' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '操作', key: 'action', width: 120 },
];

// 附件表格列定义
const attachmentColumns = [
  { title: '文件名', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type', width: 80 },
  { title: '大小', dataIndex: 'size', key: 'size', width: 100 },
  { title: '上传时间', dataIndex: 'uploadTime', key: 'uploadTime', width: 150 },
  { title: '上传人', dataIndex: 'uploadUser', key: 'uploadUser', width: 100 },
  { title: '操作', key: 'action', width: 150, fixed: 'right' },
];

// 权限判断
const canEdit = computed(() => invoiceData.value.status === 'pending');
const canConfirm = computed(() => invoiceData.value.status === 'pending');
const canSettle = computed(() => invoiceData.value.status === 'confirmed');
const canUpload = computed(() => true);

// 方法
const goBack = () => {
  router.go(-1);
};

const getStatusText = (status) => {
  const statusMap = {
    pending: '待对账',
    confirmed: '已锁定',
    settled: '已结算',
  };
  return statusMap[status] || status;
};

const getStatusColor = (status) => {
  const colorMap = {
    pending: 'orange',
    confirmed: 'blue',
    settled: 'green',
  };
  return colorMap[status] || 'default';
};

const getBillingStatusText = (status) => {
  const statusMap = {
    not_invoiced: '未开票',
    invoicing: '开票中',
    invoiced: '已开票',
  };
  return statusMap[status] || status;
};

const getBillingStatusColor = (status) => {
  const colorMap = {
    not_invoiced: 'red',
    invoicing: 'orange',
    invoiced: 'green',
  };
  return colorMap[status] || 'default';
};

const getPaymentTermsText = (terms) => {
  const termsMap = {
    prepayment: '预付款',
    account_settlement: '账期结算',
  };
  return termsMap[terms] || terms;
};

const getPaymentTermsColor = (terms) => {
  const colorMap = {
    prepayment: 'volcano',
    account_settlement: 'geekblue',
  };
  return colorMap[terms] || 'default';
};

const formatDate = (dateStr) => {
  if (!dateStr) return '';
  return dayjs(dateStr).format('YYYY-MM-DD');
};

const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  return dayjs(dateTimeStr).format('YYYY-MM-DD HH:mm:ss');
};

const formatAmount = (amount) => {
  if (!amount && amount !== 0) return '0.00';
  return Number(amount).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

const getTotalQuantity = () => {
  return filteredMaterials.value.reduce((sum, item) => sum + item.quantity, 0);
};

const getTotalAmount = () => {
  return filteredMaterials.value.reduce((sum, item) => sum + item.totalPrice, 0);
};

const handleSearch = () => {
  materialPagination.value.current = 1;
  materialPagination.value.total = filteredMaterials.value.length;
};

const resetSearch = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = key.includes('Date') ? null : '';
  });
  handleSearch();
};

const handleTableChange = (pagination) => {
  materialPagination.value = { ...materialPagination.value, ...pagination };
};

const handleUploadChange = (info) => {
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 上传成功`);
    invoiceData.value.attachments.push({
      id: `att-${Date.now()}`,
      name: info.file.name,
      type: info.file.type,
      size: `${(info.file.size / 1024 / 1024).toFixed(2)}MB`,
      uploadTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      uploadUser: '当前用户'
    });
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 上传失败`);
  }
};

const viewPurchaseOrderDetail = (id) => {
  message.info(`查看采购单详情: ${id}`);
  // router.push({ name: 'PurchaseOrderDetail', params: { id } });
};

const viewPurchaseOrderByNo = (poNo) => {
  message.info(`查看采购单详情: ${poNo}`);
  // router.push({ name: 'PurchaseOrderDetail', params: { poNo } });
};

const viewReceiptOrderDetail = (receiptNo) => {
  message.info(`查看收货单详情: ${receiptNo}`);
  // router.push({ name: 'ReceiptOrderDetail', params: { receiptNo } });
};

const viewPaymentOrderDetail = (id) => {
  message.info(`查看付款单详情: ${id}`);
  // router.push({ name: 'PaymentOrderDetail', params: { id } });
};

const fetchInvoiceDetails = async () => {
  loading.value = true;
  try {
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('Fetching invoice details for id:', id.value);
    loading.value = false;
  } catch (error) {
    message.error('获取对账单详情失败');
    loading.value = false;
  }
};

onMounted(() => {
  if (id.value) {
    fetchInvoiceDetails();
  } else {
    message.warn('未指定对账单ID');
  }
});
</script>

<style lang="less" scoped>
.invoice-detail-container {
  min-height: 100vh;
}

.invoice-progress-section {
  margin-bottom: 24px;
}

.detail-card {
  margin-bottom: 24px;
  border-radius: 4px;

  .ant-card-head {
    background-color: #fafafa;
  }
}

.search-section {
  background-color: #fafafa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.amount-text {
  font-weight: 600;
  color: #f5222d;
  font-size: 16px;
}

.log-user {
  font-weight: 500;
  margin-right: 8px;
}

.log-time {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  margin-bottom: 4px;
}

.log-detail {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  margin-top: 4px;
}

.ant-table-summary {
  background-color: #fafafa;
  
  .ant-table-summary-cell {
    border-top: 2px solid #f0f0f0;
  }
}

.summary-total {
  color: #f5222d !important;
  font-weight: bold !important;
}
</style>