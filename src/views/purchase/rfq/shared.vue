<template>
  <div class="shared-rfq-page">
    <!-- 独立页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="logo-section">
          <img src="../../../assets/images/logo.png" alt="研选工场" class="logo-image">
        </div>
        <div class="header-actions">
          <template v-if="!isLoggedIn">
            <a-button @click="handleLogin">登录</a-button>
            <a-button type="primary" @click="handleRegister">立即注册</a-button>
          </template>
          <template v-else>
            <a-button type="primary" @click="handleGoToPlatform">前往平台，采纳报价</a-button>
          </template>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 企业信息 - 简化版 -->
      <div class="enterprise-info">
        <a-card>
          <div class="enterprise-header">
            <div class="enterprise-avatar">
              <a-avatar size="large" style="background-color: #1890ff;">{{ enterpriseInfo.name.charAt(0) }}</a-avatar>
            </div>
            <div class="enterprise-details">
              <h2>{{ enterpriseInfo.fullName }}</h2>
              <p>{{ inviterInfo.name }}邀请您查看询价单</p>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 询价单信息 -->
      <div class="rfq-info">
        <a-card title="询价单详情" class="detail-card">
          <!-- 基本信息 -->
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="info-item">
                <span class="label">询价单号：</span>
                <span class="value">{{ rfqData.rfqNo }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="info-item">
                <span class="label">询价状态：</span>
                <span class="value">
                  <a-tag :color="getStatusColor(rfqData.status)">{{ getStatusText(rfqData.status) }}</a-tag>
                </span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ formatDateTime(rfqData.createTime) }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="info-item">
                <span class="label">询价时间：</span>
                <span class="value">{{ formatDateTime(rfqData.inquiryTime) }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="info-item">
                <span class="label">截止时间：</span>
                <span class="value">{{ formatDateTime(rfqData.deadline) }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="info-item">
                <span class="label">结束时间：</span>
                <span class="value">{{ formatDateTime(rfqData.endTime) }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="info-item">
                <span class="label">创建人：</span>
                <span class="value">{{ rfqData.creator }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="info-item">
                <span class="label">联系电话：</span>
                <span class="value">{{ rfqData.contactPhone }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="info-item">
                <span class="label">物料型号数：</span>
                <span class="value">{{ rfqData.materialModelCount }} 种</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="info-item">
                <span class="label">物料总数：</span>
                <span class="value">{{ getTotalQuantity() }} 件</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="info-item">
                <span class="label">已采纳总价：</span>
                <span class="value important">{{ getAcceptedTotalPrice() }}</span>
              </div>
            </a-col>
            <a-col :span="24">
              <div class="info-item">
                <span class="label">备注：</span>
                <span class="value">{{ rfqData.remark || '-' }}</span>
              </div>
            </a-col>
          </a-row>
        </a-card>
      </div>

      <!-- 物料清单 -->
      <div class="material-info">
        <a-card title="物料清单">
          <a-alert 
            :message="isLoggedIn ? '以下为物料明细及供应商报价情况，点击展开查看各供应商的详细报价。' : '以下为物料明细，登录后可查看供应商报价详情。'" 
            type="info" 
            show-icon 
            style="margin-bottom: 16px;"
          />
          
          <!-- 表格操作栏 -->
          <div class="table-toolbar" style="margin-bottom: 16px;">
            <a-button 
              type="primary" 
              @click="toggleAllExpanded"
              :icon="allExpanded ? 'up' : 'down'"
            >
              {{ allExpanded ? '全部收起' : '全部展开' }}
            </a-button>
          </div>
          
          <!-- 外层表格 - 物料信息（始终显示） -->
          <a-table 
            :columns="materialColumns" 
            :data-source="rfqData.materials" 
            size="middle" 
            :pagination="{ pageSize: 10 }" 
            row-key="id" 
            bordered 
            :scroll="{ x: 1200 }"
            :expandedRowKeys="expandedRowKeys"
            @expand="onExpand"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'acceptAlternative'">
                {{ record.acceptAlternative ? '是' : '否' }}
              </template>
              <template v-if="column.dataIndex === 'status'">
                <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
              </template>
              <template v-if="column.dataIndex === 'supplierCount'">
                <a-tag color="blue">{{ record.suppliers.length }} 家</a-tag>
              </template>
              <template v-if="column.dataIndex === 'priceRange'">
                <span v-if="isLoggedIn && record.priceRange">
                  ¥{{ record.priceRange.min.toFixed(2) }} - ¥{{ record.priceRange.max.toFixed(2) }}
                </span>
                <span v-else-if="!isLoggedIn" class="text-muted">需登录查看</span>
                <span v-else>-</span>
              </template>
            </template>

            <!-- 内层表格 - 供应商报价信息 -->
            <template #expandedRowRender="{ record }">
              <div style="margin: 12px 0;">
                <h4 style="margin-bottom: 12px; color: #262626;">供应商报价详情</h4>
                
                <!-- 未登录状态 - 显示模糊化的供应商报价和登录提示 -->
                <div v-if="!isLoggedIn" class="supplier-blur-overlay">
                  <!-- 模糊化的供应商报价表格 -->
                  <div class="blurred-supplier-table">
                    <a-table 
                      size="small" 
                      :columns="supplierColumns" 
                      :data-source="record.suppliers" 
                      :pagination="false" 
                      row-key="id" 
                      bordered
                      class="blurred-table"
                    >
                      <template #bodyCell="{ column }">
                        <span class="blurred-text">***</span>
                      </template>
                    </a-table>
                  </div>
                  
                  <!-- 登录引导覆盖层 -->
                  <div class="login-prompt-overlay">
                    <div class="prompt-content">
                      <h4>查看供应商报价需要登录</h4>
                      <p>登录后可以查看详细的供应商报价信息</p>
                      <a-space>
                        <a-button @click="handleLogin">登录</a-button>
                        <a-button type="primary" @click="handleRegister">立即注册</a-button>
                      </a-space>
                    </div>
                  </div>
                </div>

                <!-- 已登录状态 - 正常显示供应商报价 -->
                <a-table 
                  v-else
                  size="small" 
                  :columns="supplierColumns" 
                  :data-source="record.suppliers" 
                  :pagination="false" 
                  row-key="id" 
                  bordered
                >
                  <template #bodyCell="{ column, record: supplierRecord }">
                    <template v-if="column.dataIndex === 'price'">
                      <span v-if="supplierRecord.status === 'quoted'">
                        ¥{{ supplierRecord.price.toFixed(2) }}
                      </span>
                      <span v-else class="text-muted">-</span>
                    </template>
                    <template v-if="column.dataIndex === 'totalPrice'">
                      <span v-if="supplierRecord.status === 'quoted'">
                        ¥{{ supplierRecord.totalPrice.toFixed(2) }}
                      </span>
                      <span v-else class="text-muted">-</span>
                    </template>
                    <template v-if="column.dataIndex === 'minOrderQuantity'">
                      <span v-if="supplierRecord.status === 'quoted'">
                        {{ supplierRecord.minOrderQuantity }}
                      </span>
                      <span v-else class="text-muted">-</span>
                    </template>
                    <template v-if="column.dataIndex === 'promisedDelivery'">
                      <span v-if="supplierRecord.status === 'quoted'">
                        {{ supplierRecord.promisedDelivery }}
                      </span>
                      <span v-else class="text-muted">-</span>
                    </template>
                    <template v-if="column.dataIndex === 'validityPeriod'">
                      <span v-if="supplierRecord.status === 'quoted'">
                        {{ supplierRecord.validityPeriod }}
                      </span>
                      <span v-else class="text-muted">-</span>
                    </template>
                    <template v-if="column.dataIndex === 'quoteTime'">
                      <span v-if="supplierRecord.status === 'quoted'">
                        {{ formatDateTime(supplierRecord.quoteTime) }}
                      </span>
                      <span v-else class="text-muted">-</span>
                    </template>
                    <template v-if="column.dataIndex === 'status'">
                      <a-tag :color="getSupplierStatusColor(supplierRecord.status)">
                        {{ getSupplierStatusText(supplierRecord.status) }}
                      </a-tag>
                    </template>
                  </template>
                </a-table>
              </div>
            </template>
          </a-table>
        </a-card>
      </div>

      <!-- 供应商参与询价 -->
      <div class="participation-info">
        <a-card>
          <a-alert 
            message="想要参与此询价单？" 
            description="研选工场诚邀优质供应商参与报价，无需注册即可快速参与询价，获得更多商机！"
            type="info" 
            show-icon 
            action
          >
            <template #action>
              <a-button type="primary" @click="handleTempQuote">
                参与报价
              </a-button>
            </template>
          </a-alert>
        </a-card>
      </div>
    </div>

    <!-- 研选工场介绍页脚 -->
    <div class="page-footer">
      <div class="footer-content">
        <a-row :gutter="48">
          <a-col :span="8">
            <div class="footer-section">
              <h3>研选工场</h3>
              <p>自动化装备制造行业AI供应链服务平台</p>
              <p>用AI技术重构供应链，为装备制造商研发采购全流程提供高效解决方案</p>
              <a-button type="link" @click="visitWebsite" style="padding-left: 0;">
                访问官网 →
              </a-button>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="footer-section">
              <h4>平台优势</h4>
              <ul>
                <li>AI智能匹配</li>
                <li>成本优化 - 平均降低5%采购成本</li>
                <li>响应迅速 - 响应时间缩短70%</li>
                <li>全流程透明</li>
              </ul>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="footer-section">
              <h4>联系我们</h4>
              <p>400-888-9999</p>
              <p><EMAIL></p>
              <p>江苏省苏州市工业园区启泰路66号</p>
            </div>
          </a-col>
        </a-row>
        <a-divider />
        <div class="footer-bottom">
          <p>&copy; 2025 研选工场（苏州）网络有限公司 | 苏ICP备2024149956号</p>
          <p>专业的工业品采购平台 · 智能化供应链解决方案</p>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

const route = useRoute();
const router = useRouter();

// 登录状态 - 模拟未登录状态
const isLoggedIn = ref(false);

// 表格展开状态
const expandedRowKeys = ref([]);

// 全部展开状态
const allExpanded = ref(false);

// 企业信息
const enterpriseInfo = ref({
  name: '大疆科技',
  fullName: '深圳市大疆科技有限公司'
});

// 邀请人信息
const inviterInfo = ref({
  name: '张三'
});

// 询价单数据
const rfqData = ref({
  id: '',
  rfqNo: '',
  status: 'inProgress',
  createTime: '',
  inquiryTime: '',
  deadline: '',
  endTime: '',
  creator: '',
  contactPhone: '',
  materialModelCount: 0,
  remark: '',
  materials: []
});

// 外层表格列定义 - 物料信息
const materialColumns = [
  { title: '物料名称', dataIndex: 'name', key: 'name', width: 180, fixed: 'left' },
  { title: '型号', dataIndex: 'model', key: 'model', width: 180 },
  { title: '品牌', dataIndex: 'brand', key: 'brand', width: 100 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 80, align: 'center' },
  { title: '询价状态', dataIndex: 'status', key: 'status', width: 100, align: 'center' },
  { title: '期望交期', dataIndex: 'expectedDelivery', key: 'expectedDelivery', width: 100 },
  { title: '接受平替', dataIndex: 'acceptAlternative', key: 'acceptAlternative', width: 100, align: 'center' },
  { title: '供应商数量', dataIndex: 'supplierCount', key: 'supplierCount', width: 120, align: 'center' },
  // { title: '报价区间', dataIndex: 'priceRange', key: 'priceRange', width: 180, align: 'center' },
  { title: '备注', dataIndex: 'remark', key: 'remark', width: 150 },
];

// 内层表格列定义 - 供应商报价信息
const supplierColumns = [
  { title: '供应商名称', dataIndex: 'name', key: 'name', width: 150 },
  { title: '报价 (¥)', dataIndex: 'price', key: 'price', width: 100, align: 'right' },
  { title: '总价 (¥)', dataIndex: 'totalPrice', key: 'totalPrice', width: 100, align: 'right' },
  { title: '最小起订量', dataIndex: 'minOrderQuantity', key: 'minOrderQuantity', width: 120, align: 'center' },
  { title: '承诺交期', dataIndex: 'promisedDelivery', key: 'promisedDelivery', width: 100 },
  { title: '有效期', dataIndex: 'validityPeriod', key: 'validityPeriod', width: 100 },
  { title: '报价时间', dataIndex: 'quoteTime', key: 'quoteTime', width: 150 },
  { title: '报价状态', dataIndex: 'status', key: 'status', width: 100, align: 'center' },
  { title: '备注', dataIndex: 'remark', key: 'remark', width: 150 },
];

// 表格展开事件
const onExpand = (expanded, record) => {
  if (expanded) {
    expandedRowKeys.value.push(record.id);
  } else {
    const index = expandedRowKeys.value.indexOf(record.id);
    if (index > -1) {
      expandedRowKeys.value.splice(index, 1);
    }
  }
  // 检查是否全部展开
  checkAllExpandedState();
};

// 检查全部展开状态
const checkAllExpandedState = () => {
  allExpanded.value = expandedRowKeys.value.length === rfqData.value.materials.length;
};

// 全部展开/收起切换
const toggleAllExpanded = () => {
  if (allExpanded.value) {
    // 全部收起
    expandedRowKeys.value = [];
    allExpanded.value = false;
  } else {
    // 全部展开
    expandedRowKeys.value = rfqData.value.materials.map(item => item.id);
    allExpanded.value = true;
  }
};

// 工具方法
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  return dateTimeStr;
};

const getStatusText = (status) => {
  const statusMap = {
    notStarted: '未开始',
    inProgress: '询价中',
    accepted: '已采纳',
    expired: '已过期',
    invalid: '已失效',
    cancelled: '已取消',
  };
  return statusMap[status] || status;
};

const getStatusColor = (status) => {
  const colorMap = {
    notStarted: 'default',
    inProgress: 'blue',
    accepted: 'green',
    expired: 'orange',
    invalid: 'red',
    cancelled: 'red',
  };
  return colorMap[status] || 'default';
};

const getSupplierStatusColor = (status) => {
  const map = {
    pending: 'blue',
    quoted: 'green',
    rejected: 'red',
    expired: 'orange',
  };
  return map[status] || 'default';
};

const getSupplierStatusText = (status) => {
  const map = {
    pending: '待报价',
    quoted: '已报价',
    rejected: '已拒绝',
    expired: '已过期',
  };
  return map[status] || '未知';
};

const getTotalQuantity = () => {
  return rfqData.value.materials.reduce((total, item) => total + item.quantity, 0);
};

const getAcceptedTotalPrice = () => {
  // 计算已采纳的供应商报价总价
  const totalPrice = rfqData.value.materials.reduce((sum, item) => {
    // 假设已采纳的是状态为 'accepted' 的供应商报价
    const acceptedSupplier = item.suppliers.find(s => s.status === 'accepted');
    if (acceptedSupplier) {
      return sum + acceptedSupplier.totalPrice;
    }
    return sum;
  }, 0);
  
  if (totalPrice > 0) {
    return `¥${totalPrice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  }
  return '-';
};

// 操作方法
const handleRegister = () => {
  // 跳转到注册页面，实际项目中根据需要调整
  message.info('跳转到注册页面');
  // router.push('/register');
};

const handleLogin = () => {
  // 模拟登录成功，将状态改为已登录
  isLoggedIn.value = true;
  message.success('登录成功！');
};

// 临时报价
const handleTempQuote = () => {
  // 跳转到临时报价页面（无需登录）
  const params = parseShareParams();
  if (params) {
    const queryParam = btoa(JSON.stringify(params));
    router.push({
      path: '/supplier/temp-quote',
      query: { param: queryParam }
    });
  } else {
    message.error('分享链接参数错误');
  }
};

const visitWebsite = () => {
  // 打开研选工场官网
  window.open('https://www.yanxuan.cloud/', '_blank');
};

const handleGoToPlatform = () => {
  // 跳转到平台采纳报价页面
  message.info('跳转到平台采纳报价页面');
  // router.push('/purchase/rfq/detail/' + rfqData.value.id);
};

// 解析分享参数并获取询价单详情
const parseShareParams = () => {
  try {
    const paramStr = route.query.param;
    if (!paramStr) {
      throw new Error('无效的分享链接');
    }
    
    const params = JSON.parse(atob(paramStr));
    return params;
  } catch (error) {
    message.error('分享链接格式错误');
    return null;
  }
};

// 获取询价单详情
const fetchRfqDetails = async () => {
  try {
    const params = parseShareParams();
    if (!params) return;

    // 模拟API调用，实际项目中根据 rfqNo 获取对应的询价单数据
    console.log('获取分享的询价单详情，询价单号：', params.rfqNo);
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 模拟询价单数据，包含嵌套的供应商信息
    rfqData.value = {
      id: '1',
      rfqNo: params.rfqNo || 'RFQ-2023-0001',
      status: 'inProgress',
      createTime: '2023-10-15 09:30:00',
      inquiryTime: '2023-10-15 10:00:00',
      deadline: '2023-10-25 18:00:00',
      endTime: '2023-10-26 18:00:00',
      creator: '张三',
      contactPhone: '138****8888',
      materialModelCount: 4,
      remark: '紧急采购项目，欢迎供应商报价',
      materials: Array.from({ length: 4 }).map((_, index) => {
        const quantity = Math.floor(Math.random() * 100 + 10);
        
        // 生成供应商报价数据
        const suppliers = Array.from({ length: 3 + Math.floor(Math.random() * 2) }).map((_, sIndex) => {
          const supplierStatuses = ['pending', 'quoted', 'rejected', 'expired'];
          const status = sIndex === 0 ? 'quoted' : supplierStatuses[sIndex % supplierStatuses.length];
          const price = Math.floor(Math.random() * 1000 + 100);
          
          return {
            id: `supplier-${index}-${sIndex}`,
            name: `供应商${String.fromCharCode(65 + sIndex)}`,
            price: status === 'quoted' ? price : 0,
            totalPrice: status === 'quoted' ? price * quantity : 0,
            minOrderQuantity: status === 'quoted' ? Math.floor(Math.random() * 100 + 10) : 0,
            promisedDelivery: status === 'quoted' ? `${Math.floor(Math.random() * 30 + 15)}天` : '',
            validityPeriod: status === 'quoted' ? '2023-11-05' : '',
            quoteTime: status === 'quoted' ? `2023-10-${16 + sIndex} 10:30:00` : '',
            status: status,
            remark: status === 'quoted' && sIndex % 2 === 0 ? '含税价格' : '',
          };
        });
        
        // 计算报价区间
        const quotedSuppliers = suppliers.filter(s => s.status === 'quoted' && s.price > 0);
        const priceRange = quotedSuppliers.length > 0 ? {
          min: Math.min(...quotedSuppliers.map(s => s.price)),
          max: Math.max(...quotedSuppliers.map(s => s.price))
        } : null;
        
        return {
          id: `material-${index}`,
          name: `电子元件 ${index + 1}`,
          model: `MODEL-${100 + index}`,
          brand: index % 3 === 0 ? '品牌A' : index % 3 === 1 ? '品牌B' : '品牌C',
          quantity: quantity,
          expectedDelivery: '30天',
          status: 'inProgress',
          remark: index % 2 === 0 ? '紧急采购' : '',
          acceptAlternative: index % 3 === 0,
          suppliers: suppliers,
          priceRange: priceRange
        };
      })
    };

  } catch (error) {
    message.error('获取询价单详情失败');
  }
};

// 生命周期
onMounted(() => {
  fetchRfqDetails();
});
</script>

<style lang="less" scoped>
.shared-rfq-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  
  // 确保页面完全独立，覆盖可能的外部样式
  position: relative;
  z-index: 1;
}

.page-header {
  background-color: #2a2a35;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 100;

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;

    .logo-section {
      display: flex;
      align-items: center;
      gap: 12px;
      .logo-image {
        width: 150px;
      }
      .logo-placeholder {
        font-size: 24px;
        font-weight: 600;
        color: #fff;
        padding: 8px 16px;
        border: 2px solid #fff;
        border-radius: 8px;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
}

.main-content {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.enterprise-info,
.rfq-info,
.material-info,
.participation-info {
  margin-bottom: 24px;
}

.enterprise-header {
  display: flex;
  align-items: center;
  gap: 16px;

  .enterprise-details {
    h2 {
      margin: 0 0 8px 0;
      color: #262626;
      font-size: 20px;
    }

    p {
      margin: 0;
      color: #8c8c8c;
    }
  }
}

.detail-card {
  border-radius: 8px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  :deep(.ant-card-head) {
    background-color: #fafafa;
  }
}

.info-item {
  display: flex;
  margin-bottom: 16px;

  .label {
    color: rgba(0, 0, 0, 0.65);
    min-width: 90px;
    flex-shrink: 0;
  }

  .value {
    flex: 1;
    font-weight: 500;

    &.important {
      color: #f94c30;
      font-weight: 600;
    }
  }
}

.text-muted {
  color: #bfbfbf;
}

.table-toolbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.supplier-blur-overlay {
  position: relative;
  
  .blurred-supplier-table {
    position: relative;
    
    .blurred-table {
      opacity: 0.4;
      filter: blur(2px);
      pointer-events: none;
      
      .blurred-text {
        color: #bfbfbf;
      }
    }
  }
  
  .login-prompt-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 6px;
    
    .prompt-content {
      background: #fff;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      text-align: center;
      border: 1px solid #d9d9d9;
      
      h4 {
        margin-bottom: 8px;
        color: #262626;
        font-size: 16px;
        font-weight: 500;
      }
      
      p {
        margin-bottom: 16px;
        color: #595959;
        line-height: 1.5;
        font-size: 14px;
      }
    }
  }
}

.page-footer {
  background-color: #2a2a35;
  color: #fff;
  margin-top: 48px;
  
  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 48px 24px 24px;

    .footer-section {
      h3 {
        color: #fff;
        font-size: 20px;
        margin-bottom: 16px;
        font-weight: 600;
      }

      h4 {
        color: #fff;
        font-size: 16px;
        margin-bottom: 12px;
        font-weight: 500;
      }

      p {
        color: #bfbfbf;
        margin-bottom: 8px;
        line-height: 1.6;
      }

      ul {
        color: #bfbfbf;
        padding-left: 16px;
        
        li {
          margin-bottom: 6px;
          line-height: 1.6;
        }
      }
    }

    .footer-bottom {
      text-align: center;
      color: #8c8c8c;
      margin-top: 16px;

      p {
        margin: 4px 0;
      }
    }
  }
}



// 响应式设计
@media (max-width: 768px) {
  .page-header .header-content {
    padding: 0 16px;
    
    .logo-section .logo-placeholder {
      font-size: 18px;
      padding: 6px 12px;
    }
  }

  .main-content {
    padding: 16px;
  }

  .enterprise-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .info-item {
    flex-direction: column;
    margin-bottom: 12px;
    
    .label {
      min-width: auto;
      margin-bottom: 4px;
      font-weight: 500;
    }
  }

  .supplier-blur-overlay {
    .login-prompt-overlay .prompt-content {
      margin: 0 16px;
      padding: 20px;
    }
  }

  .page-footer {
    .footer-content {
      padding: 32px 16px 16px;
      
      .footer-section {
        margin-bottom: 32px;
      }
    }
  }
}

// 确保表格在移动端的显示
:deep(.ant-table-wrapper) {
  .ant-table-tbody {
    .ant-table-expanded-row > td {
      padding: 8px 16px;
    }
  }
}
</style> 