<template>
  <div class="rfq-container">
    <!-- 搜索区域 -->
    <div class="search-area">
      <a-form layout="inline" :model="searchForm">
        <a-row :gutter="12">
          <a-col :span="8">
            <a-form-item label="产品型号">
              <a-input v-model:value="searchForm.model" placeholder="请输入型号" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="品牌">
              <a-select v-model:value="searchForm.brand" placeholder="请选择品牌" style="width: 100%" allowClear>
                <a-select-option v-for="item in brandOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="产品分类">
              <a-tree-select v-model:value="searchForm.category" style="width: 100%" :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" :tree-data="categoryOptions" placeholder="请选择产品分类" tree-default-expand-all allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="询价单号">
              <a-input v-model:value="searchForm.rfqNo" placeholder="请输入询价单号" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="询价时间">
              <a-range-picker v-model:value="searchForm.rfqTimeRange" format="YYYY-MM-DD" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="截止时间">
              <a-range-picker v-model:value="searchForm.deadlineRange" format="YYYY-MM-DD" />
            </a-form-item>
          </a-col>

          <a-col :span="2">
            <a-space>
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button @click="handleReset">重置</a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 视图切换和表格区域 -->
    <div class="table-area">
      <!-- 视图切换按钮 -->
      <div class="view-switch">
        <a-radio-group v-model:value="currentView" button-style="solid" @change="handleViewChange">
          <a-radio-button value="material">物料视图</a-radio-button>
          <a-radio-button value="order">单据视图</a-radio-button>
        </a-radio-group>
      </div>

      <!-- 物料视图 -->
      <MaterialTable 
        v-if="currentView === 'material'" 
        ref="materialTableRef"
        :searchForm="searchForm"
        @search="handleSearch"
        @reset="handleReset"
      />

      <!-- 单据视图 -->
      <OrderTable 
        v-if="currentView === 'order'" 
        ref="orderTableRef"
        :searchForm="searchForm"
        @viewDetail="handleViewOrderDetail"
      />
    </div>

    <!-- 询价单详情弹窗 -->
    <a-modal 
      v-model:visible="orderDetailVisible" 
      title="询价单详情" 
      width="1200px" 
      :footer="null"
      @cancel="handleCloseOrderDetail"
    >
      <div class="order-detail">
        <a-descriptions :column="3" bordered>
          <a-descriptions-item label="询价单号">{{ currentOrderDetail.rfqNo }}</a-descriptions-item>
          <a-descriptions-item label="物料总数">{{ currentOrderDetail.materialCount }} 种</a-descriptions-item>
          <a-descriptions-item label="物料总价">¥{{ currentOrderDetail.totalPrice?.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</a-descriptions-item>
          <a-descriptions-item label="询价时间">{{ currentOrderDetail.rfqTime }}</a-descriptions-item>
          <a-descriptions-item label="截止时间">{{ currentOrderDetail.deadline }}</a-descriptions-item>

        </a-descriptions>
        
        <!-- 这里可以显示该询价单下的物料详情表格 -->
        <div style="margin-top: 20px;">
          <h4>物料明细</h4>
          <a-alert 
            message="此处可显示该询价单下的具体物料信息，包括每个物料的供应商报价情况等详细信息。" 
            type="info" 
            show-icon 
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import MaterialTable from './component/materialTable.vue';
import OrderTable from './component/orderTable.vue';

// 当前视图
const currentView = ref('material');

// 搜索表单
const searchForm = reactive({
  model: '',
  brand: undefined,
  category: undefined,
  rfqNo: '',
  rfqTimeRange: [],
  deadlineRange: [],
});

// 品牌选项
const brandOptions = ref([
  { label: '品牌A', value: 'A' },
  { label: '品牌B', value: 'B' },
  { label: '品牌C', value: 'C' },
]);

// 产品分类树形选项
const categoryOptions = ref([
  {
    title: '电子元件',
    value: 'electronics',
    children: [
      {
        title: '集成电路',
        value: 'ic',
      },
      {
        title: '电阻电容',
        value: 'passive',
      },
    ],
  },
  {
    title: '机械零件',
    value: 'mechanical',
    children: [
      {
        title: '螺丝螺栓',
        value: 'screws',
      },
      {
        title: '轴承',
        value: 'bearings',
      },
    ],
  },
]);



// 表格引用
const materialTableRef = ref(null);
const orderTableRef = ref(null);

// 询价单详情弹窗相关
const orderDetailVisible = ref(false);
const currentOrderDetail = ref({});



// 视图切换处理
const handleViewChange = () => {
  console.log('切换到', currentView.value, '视图');
  // 切换视图时可以重新加载数据
  if (currentView.value === 'material' && materialTableRef.value) {
    materialTableRef.value.fetchData();
  } else if (currentView.value === 'order' && orderTableRef.value) {
    orderTableRef.value.fetchData();
  }
};

// 查询方法
const handleSearch = () => {
  console.log('搜索条件:', searchForm);
  if (currentView.value === 'material' && materialTableRef.value) {
    materialTableRef.value.fetchData();
  } else if (currentView.value === 'order' && orderTableRef.value) {
    orderTableRef.value.fetchData();
  }
};

// 重置查询条件
const handleReset = () => {
  Object.keys(searchForm).forEach((key) => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = [];
    } else {
      searchForm[key] = undefined;
    }
  });
  handleSearch();
};

// 查看询价单详情
const handleViewOrderDetail = (orderRecord) => {
  currentOrderDetail.value = orderRecord;
  orderDetailVisible.value = true;
};

// 关闭询价单详情弹窗
const handleCloseOrderDetail = () => {
  orderDetailVisible.value = false;
  currentOrderDetail.value = {};
};
</script>

<style scoped>
.rfq-container {
  padding: 16px;
}

.search-area {
  margin-bottom: 16px;
}

.table-area {
  margin-bottom: 16px;
}

.view-switch {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.order-detail {
  margin-top: 10px;
}

.order-detail h4 {
  margin-bottom: 12px;
  color: #333;
}
</style>
