<template>
  <a-modal :title="title" :width="800" :visible="visible" :confirm-loading="confirmLoading" @ok="handleOk" @cancel="handleCancel">
    <a-table :columns="columns" :data-source="suppliers" :pagination="{ pageSize: 5 }" rowKey="id" size="middle" :row-selection="{ selectedRowKeys, onChange: onSelectChange }" bordered>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          <span>{{ record.type === '品牌商' ? record.name : '严选供应商' }}</span>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <p style="margin: 0">
          <a-descriptions>
            <a-descriptions-item v-for="(value, key) in record.expandData" :key="key" :label="key">{{ value }}</a-descriptions-item>
          </a-descriptions>
        </p>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, h } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: '配置供应商',
  },
  visible: {
    type: Boolean,
    default: false,
  },
  confirmLoading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:visible', 'ok', 'cancel', 'select']);

// 表格列定义
const columns = [
  { title: '供应商名称', dataIndex: 'name', key: 'name', width: 200 },
  { title: '供应商类型', dataIndex: 'type', key: 'type', width: 150 },
  { title: '经营品牌', dataIndex: 'brands', key: 'brands', width: 200 },
];

// 供应商数据
const suppliers = ref([
  {
    id: '1',
    name: '京东电子',
    type: '贸易商',
    brands: 'Intel, AMD, NVIDIA',
    expandData: {
      厂房产权: '自有',
      厂房面积: '5000平方米',
      在职人数: '120人',
      年营业额: '1500万元',
      代理区域范围: '华北、华东地区',
    },
  },
  {
    id: '2',
    name: '力创科技',
    type: '品牌商',
    brands: 'Samsung, LG',
    expandData: {
      厂房产权: '租赁',
      厂房面积: '8000平方米',
      在职人数: '350人',
      年营业额: '6000万元',
    },
  },
  {
    id: '3',
    name: '联创电子',
    type: '加工商',
    brands: 'Philips, Sony',
    expandData: {
      厂房产权: '自有',
      厂房面积: '12000平方米',
      在职人数: '500人',
      年营业额: '8000万元',
      核心加工能力: 'PCB组装、电子元器件加工',
    },
  },
  {
    id: '4',
    name: '鼎盛电子',
    type: '加工商',
    brands: 'Huawei, Xiaomi',
    expandData: {
      厂房产权: '自有',
      厂房面积: '15000平方米',
      在职人数: '680人',
      年营业额: '9500万元',
      核心加工能力: '芯片封装、电路板制造',
    },
  },
  {
    id: '5',
    name: '中科电子',
    type: '贸易商',
    brands: 'Dell, HP, Lenovo',
    expandData: {
      厂房产权: '租赁',
      厂房面积: '3000平方米',
      在职人数: '85人',
      年营业额: '3200万元',
      代理区域范围: '华南、西南地区',
    },
  },
]);

// 选中行
const selectedRowKeys = ref([]);

const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 处理确认
const handleOk = () => {
  if (selectedRowKeys.value.length > 0) {
    // 获取选中的供应商
    const selectedSuppliers = suppliers.value.filter((item) => selectedRowKeys.value.includes(item.id));
    emit('ok', selectedSuppliers);
  } else {
    // 未选择任何供应商
    alert('请选择至少一个供应商');
  }
};

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
};

// 选择特定供应商
const handleSelect = (record) => {
  emit('select', record);
  emit('update:visible', false);
};

// 扩展行渲染
const expandedRowRender = (record) => {
  const baseInfo = `
    <div class="expanded-row">
      <p><strong>厂房产权：</strong>${record.expandData.厂房产权}</p>
      <p><strong>厂房面积：</strong>${record.expandData.厂房面积}</p>
      <p><strong>在职人数：</strong>${record.expandData.在职人数}</p>
      <p><strong>年营业额：</strong>${record.expandData.年营业额}</p>
      ${record.type === '贸易商' ? `<p><strong>代理区域范围：</strong>${record.expandData.代理区域范围}</p>` : ''}
      ${record.type === '加工商' ? `<p><strong>核心加工能力：</strong>${record.expandData.核心加工能力}</p>` : ''}
    </div>
  `;
  return h('div', { innerHTML: baseInfo });
};
</script>

<style scoped>
.ant-table {
  margin-bottom: 16px;
}

.expanded-row {
  padding: 12px;
  background-color: #fafafa;
  border-radius: 4px;
}

.expanded-row p {
  margin-bottom: 8px;
}
</style>
