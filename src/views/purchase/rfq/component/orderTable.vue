<template>
  <div class="order-table">
    <!-- 表格操作栏 -->
    <div class="table-operations">
      <a-space>
        <a-dropdown>
          <a-button type="primary">
            批量操作
            <down-outlined />
          </a-button>
          <template #overlay>
            <a-menu @click="handleBatchOperation">
              <a-menu-item key="toPurchaseOrder">转采购单</a-menu-item>
              <a-menu-item key="reInquiry">再次询价</a-menu-item>
              <a-menu-item key="cancel">取消</a-menu-item>
              <a-menu-item key="delete">删除</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button type="primary" @click="handleBatchToPurchaseOrder">批量转采购单</a-button>
      </a-space>
    </div>

    <!-- 选中询价单汇总信息 -->
    <div class="selection-summary">
      <div>
        <a-tooltip placement="top">
          <template #title>
            <div>1. 物料总价为各询价单内已选择供应商的物料价格汇总。</div>
            <div>2. 询价单状态包括：未开始、询价中、已采纳、已过期、已失效、已取消。</div>
          </template>
          <span style="color: #666"><InfoCircleFilled style="margin-right: 4px" />询价单说明</span>
        </a-tooltip>
      </div>
      <div class="summary-content">
        <span
          >已选择：<a-tag color="red">{{ selectedRowKeys.length }}</a-tag> 个询价单</span
        >
        <span
          >总金额：<a-tag color="red">¥{{ selectedTotalPrice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</a-tag></span
        >
      </div>
    </div>

    <!-- 询价单表格 -->
    <a-table 
      :columns="orderColumns" 
      :data-source="orderData" 
      size="middle" 
      :loading="loading" 
      :pagination="pagination" 
      @change="handleTableChange" 
      row-key="rfqNo" 
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" 
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'rfqNo'">
          <a @click="handleViewDetail(record)" style="color: #1890ff;">{{ record.rfqNo }}</a>
        </template>
        <template v-if="column.dataIndex === 'materialCount'">
          <span>{{ record.materialCount }} </span>
        </template>
        <template v-if="column.dataIndex === 'materialModelCount'">
          <span>{{ record.materialModelCount }} </span>
        </template>
        <template v-if="column.dataIndex === 'totalPrice'">
          <span style="font-weight: bold; color: #f5222d;">¥{{ record.totalPrice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a v-if="canToPurchaseOrder(record.status)" @click="handleToPurchaseOrder(record)">转采购单</a>
            <a v-if="canReInquiry(record.status)" @click="handleReInquiry(record)">再次询价</a>
            <a v-if="canCancel(record.status)" @click="handleCancel(record)" class="danger-link">取消</a>
            <a-popconfirm v-if="canDelete(record.status)" title="确定要删除此询价单吗?" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record)">
              <a class="danger-link">删除</a>
            </a-popconfirm>
            <a @click="handleShare(record)">分享</a>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 分享询价单弹窗 -->
    <a-modal 
      v-model:visible="shareModalVisible" 
      title="分享询价单" 
      width="600px" 
      :footer="null"
      @cancel="handleCloseShare"
    >
      <div class="share-content">
        <div class="share-text">
          <a-typography-paragraph>
            <strong>{{ currentUser.name }}</strong>邀请您加入研选工场，并查看<strong>{{ currentEnterprise.fullName }}</strong>的询价单。
          </a-typography-paragraph>
          <a-typography-paragraph>
            点击链接关注最新报价: 
            <a-typography-text copyable :copy-config="{ text: shareUrl }" type="success">
              {{ shareUrl }}
            </a-typography-text>
          </a-typography-paragraph>
        </div>
        <a-alert 
          message="本链接为公开链接，任何研选工场会员均可查看报价，请谨慎处理。" 
          type="warning" 
          show-icon 
        />
        <div class="share-actions">
          <a-space>
            <a-button type="primary" @click="handleCopyLink">复制链接</a-button>
            <a-button @click="handleCloseShare">关闭</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { DownOutlined, InfoCircleFilled } from '@ant-design/icons-vue';

const props = defineProps({
  searchForm: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['viewDetail']);

const router = useRouter();

// 询价单表格列定义
const orderColumns = [
  { title: '询价单号', dataIndex: 'rfqNo', key: 'rfqNo', width: 160, fixed: 'left' },
  { title: '物料型号数', dataIndex: 'materialModelCount', key: 'materialModelCount', width: 110, align: 'center' },
  { title: '物料总数', dataIndex: 'materialCount', key: 'materialCount', width: 100, align: 'center' },
  { title: '物料总价', dataIndex: 'totalPrice', key: 'totalPrice', width: 140, align: 'right' },
  { title: '询价时间', dataIndex: 'rfqTime', key: 'rfqTime', width: 120 },
  { title: '截止时间', dataIndex: 'deadline', key: 'deadline', width: 120 },
  { title: '操作', dataIndex: 'action', key: 'action', fixed: 'right', width: 200 },
];

// 表格数据
const orderData = ref([]);
const loading = ref(false);

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 选中行状态
const selectedRowKeys = ref([]);

// 选中行变化处理
const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys;
};

// 计算选中询价单的总价
const selectedTotalPrice = computed(() => {
  return parseFloat(
    orderData.value
      .filter((item) => selectedRowKeys.value.includes(item.rfqNo))
      .reduce((sum, item) => sum + item.totalPrice, 0)
      .toFixed(2)
  );
});

// 状态颜色和文本
const getStatusColor = (status) => {
  const map = {
    inProgress: 'blue',
    accepted: 'green',
    deadline: 'orange',
    expired: 'red',
    cancelled: 'red',
  };
  return map[status] || 'default';
};

const getStatusText = (status) => {
  const map = {
    inProgress: '询价中',
    accepted: '已采纳',
    deadline: '已截止',
    expired: '已过期',
    cancelled: '已取消',
  };
  return map[status] || '未知';
};

// 判断可执行的操作
const canToPurchaseOrder = (status) => status === 'accepted';
const canReInquiry = (status) => ['accepted', 'deadline', 'expired', 'cancelled'].includes(status);
const canCancel = (status) => status === 'inProgress';
const canDelete = (status) => ['deadline', 'expired', 'cancelled'].includes(status);

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
};

// 获取数据
const fetchData = () => {
  loading.value = true;

  // 模拟数据请求，实际项目中请替换为真实API调用
  setTimeout(() => {
    // 将物料数据聚合为询价单数据
    const mockOrderData = [];
    
    // 模拟不同状态的询价单
    const statuses = ['inProgress', 'accepted', 'deadline', 'expired', 'cancelled'];
    
    for (let i = 0; i < 15; i++) {
      const status = statuses[i % statuses.length];
      const materialCount = Math.floor(Math.random() * 8) + 2; // 2-10种物料
      const materialModelCount = Math.floor(Math.random() * 5) + 1; // 1-6种型号
      const avgPrice = Math.floor(Math.random() * 5000) + 1000; // 平均价格1000-6000
      const totalPrice = avgPrice * materialCount;
      
      mockOrderData.push({
        rfqNo: `RFQ202300${1000 + i}`,
        materialCount: materialCount,
        materialModelCount: materialModelCount,
        totalPrice: totalPrice,
        rfqTime: '2023-06-0' + ((i % 9) + 1),
        deadline: '2023-06-' + (10 + (i % 20)),
        status: status,
        materials: [] // 实际项目中这里会包含具体的物料信息
      });
    }

    orderData.value = mockOrderData;
    pagination.total = 50; // 模拟总数据量
    loading.value = false;
  }, 500);
};

// 操作方法
const handleViewDetail = (record) => {
  router.push(`/purchase/rfq/detail/${record.rfqNo}`);
};

const handleToPurchaseOrder = (record) => {
  console.log('转采购单', record);
  // 这里可以跳转到采购单页面或打开转换弹窗
};

const handleReInquiry = (record) => {
  console.log('再次询价', record);
  // 这里可以重新发起询价流程
};

const handleCancel = (record) => {
  console.log('取消询价单', record);
  // 这里处理取消逻辑
};

const handleDelete = (record) => {
  console.log('删除询价单', record);
  // 这里处理删除逻辑
  // 删除后重新获取数据
  fetchData();
};

// 批量操作处理
const handleBatchOperation = ({ key }) => {
  if (selectedRowKeys.value.length === 0) {
    return alert('请先选择要操作的询价单');
  }

  const selectedRecords = orderData.value.filter((item) => selectedRowKeys.value.includes(item.rfqNo));

  switch (key) {
    case 'toPurchaseOrder':
      console.log('批量转采购单', selectedRecords);
      break;
    case 'reInquiry':
      console.log('批量再次询价', selectedRecords);
      break;
    case 'cancel':
      console.log('批量取消', selectedRecords);
      break;
    case 'delete':
      console.log('批量删除', selectedRecords);
      break;
    default:
      break;
  }
};

// 批量转采购单
const handleBatchToPurchaseOrder = () => {
  if (selectedRowKeys.value.length === 0) {
    return alert('请先选择要转换的询价单');
  }

  const selectedRecords = orderData.value.filter((item) => selectedRowKeys.value.includes(item.rfqNo));
  const validRecords = selectedRecords.filter(record => canToPurchaseOrder(record.status));
  
  if (validRecords.length === 0) {
    return alert('选中的询价单中没有可转换的记录（只有已采纳状态的询价单可以转换）');
  }

  console.log('批量转采购单', validRecords);
  // 这里处理批量转换逻辑
};

// 分享相关状态
const shareModalVisible = ref(false);
const currentShareRecord = ref(null);

// 模拟当前用户和企业信息
const currentUser = ref({
  name: '张三'
});

const currentEnterprise = ref({
  fullName: '深圳市研选科技有限公司'
});

// 生成分享链接
const shareUrl = computed(() => {
  if (!currentShareRecord.value) return '';
  // 在开发环境下使用本地链接，生产环境使用.env.prod中的VITE_DOMAIN
  const baseUrl = import.meta.env.DEV 
    ? `${window.location.origin}/bomai/workSpace/company-space/ans-price`
    : `http://${import.meta.env.VITE_DOMAIN}/workSpace/company-space/ans-price`;
  const params = btoa(JSON.stringify({
    rfqNo: currentShareRecord.value.rfqNo,
    timestamp: Date.now()
  }));
  return `${baseUrl}?param=${params}`;
});

// 分享询价单
const handleShare = (record) => {
  currentShareRecord.value = record;
  shareModalVisible.value = true;
};

// 复制分享链接
const handleCopyLink = async () => {
  try {
    await navigator.clipboard.writeText(shareUrl.value);
    message.success('链接已复制到剪贴板');
  } catch (err) {
    // 降级处理
    const textArea = document.createElement('textarea');
    textArea.value = shareUrl.value;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    message.success('链接已复制到剪贴板');
  }
};

// 关闭分享弹窗
const handleCloseShare = () => {
  shareModalVisible.value = false;
  currentShareRecord.value = null;
};

// 暴露给父组件的方法
defineExpose({
  fetchData,
  selectedRowKeys,
  selectedTotalPrice
});

// 初始化
fetchData();
</script>

<style scoped>
.order-table {
  width: 100%;
}

.table-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
}

.danger-link {
  color: #ff4d4f;
}

.selection-summary {
  position: sticky;
  bottom: 0;
  margin-bottom: 16px;
  background-color: #fff4f0;
  padding: 12px 16px;
  border-radius: 4px;
  border: 1px solid #ffa39e;
  display: flex;
  justify-content: space-between;
  z-index: 1;
}

.summary-content {
  display: flex;
  gap: 16px;
}

.share-content {
  padding: 16px 0;
}

.share-text {
  margin-bottom: 16px;
}

.share-actions {
  margin-top: 16px;
  text-align: right;
}
</style> 