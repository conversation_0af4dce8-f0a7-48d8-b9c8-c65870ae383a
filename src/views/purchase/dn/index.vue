<template>
  <div class="receipt-container">
    <!-- 搜索区域 -->
    <div class="search-area">
      <a-form style="display: block;" layout="inline" :model="searchForm">
        <a-row>
          <template v-for="field in visibleSearchFields" :key="field.key">
            <a-col :span="4">
              <a-form-item :label="field.label">
                <!-- 输入框 -->
                <a-input v-if="field.type === 'input'" v-model:value="searchForm[field.key]" :placeholder="`请输入${field.label}`" />

                <!-- 下拉选择框 -->
                <a-select v-else-if="field.type === 'select'" v-model:value="searchForm[field.key]" :placeholder="`请选择${field.label}`" style="width: 100%" allowClear>
                  <a-select-option v-for="item in field.options" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>

                <!-- 日期范围选择器 -->
                <a-range-picker v-else-if="field.type === 'dateRange'" v-model:value="searchForm[field.key]" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
          </template>

          <a-col :span="4" class="search-buttons">
            <a-space>
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button @click="handleReset">重置</a-button>
              <a-button type="link" @click="toggleSearchConfig">
                <setting-outlined />
                配置搜索项
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>

      <!-- 搜索字段配置抽屉 -->
      <a-drawer title="配置搜索项" placement="right" :visible="showSearchConfig" @close="toggleSearchConfig" width="400px">
        <a-checkbox-group v-model:value="selectedSearchFields" @change="handleSearchFieldsChange">
          <a-row>
            <a-col :span="12" v-for="field in allSearchFields" :key="field.key">
              <a-checkbox :value="field.key">{{ field.label }}</a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </a-drawer>
    </div>

    <!-- 表格区域 -->
    <div class="view-selector">
      <a-button-group>
        <a-button :type="viewMode === 'order' ? 'primary' : 'default'" @click="switchViewMode('order')">
          <ordered-list-outlined /> 单据视图
        </a-button>
        <a-button :type="viewMode === 'product' ? 'primary' : 'default'" @click="switchViewMode('product')">
          <appstore-outlined /> 物料视图
        </a-button>
      </a-button-group>
    </div>

    <!-- 物料视图表格 -->
    <material-table
      v-if="viewMode === 'product'"
      :tableData="tableData"
      :loading="loading"
      :pagination="pagination"
      @tableChange="handleTableChange"
      @selectChange="onSelectChange"
      @export="handleExport"
      @print="handlePrint"
      @viewDetail="handleViewDetail"
      @edit="handleEdit"
      @cancel="handleCancel"
      @columnsChange="handleMaterialColumnsChange"
    />

    <!-- 订单视图表格 -->
    <order-table
      v-else
      :tableData="tableData"
      :loading="loading"
      :pagination="pagination"
      @tableChange="handleTableChange"
      @selectChange="onSelectChange"
      @viewDetail="handleViewDetail"
      @edit="handleEdit"
      @cancel="handleCancel"
      @columnsChange="handleOrderColumnsChange"
      @batchReceipt="handleBatchReceipt"
      @batchReturn="handleBatchReturn"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { SettingOutlined, ExportOutlined, AppstoreOutlined, PrinterOutlined, OrderedListOutlined } from '@ant-design/icons-vue';
import MaterialTable from './component/materialTable.vue';
import OrderTable from './component/orderTable.vue';
import { useRouter } from 'vue-router';
// 搜索配置
const showSearchConfig = ref(false);
const toggleSearchConfig = () => {
  showSearchConfig.value = !showSearchConfig.value;
};

const router = useRouter();

// 所有可能的搜索字段
const allSearchFields = [
  { key: 'receiptNo', label: '送货单号', type: 'input' },
  { key: 'materialName', label: '物料名称', type: 'input' },
  { key: 'materialModel', label: '物料型号', type: 'input' },
  { key: 'deliveryTimeRange', label: '发货时间', type: 'dateRange' },
  {
    key: 'status',
    label: '状态',
    type: 'select',
    options: [
      { label: '已发货', value: 'shipped' },
      { label: '已收货', value: 'received' },
      { label: '退货中', value: 'returning' },
      { label: '已收货（部分退货）', value: 'partial_returned' }
    ],
  },
  { key: 'poNo', label: '采购订单号', type: 'input' },
  { key: 'deliveryNo', label: '送货单号', type: 'input' },
  {
    key: 'category',
    label: '物料分类',
    type: 'select',
    options: [
      { label: '电子元件', value: 'electronics' },
      { label: '机械零件', value: 'mechanical' },
      { label: '原材料', value: 'raw' },
    ],
  },
  { key: 'brand', label: '品牌', type: 'input' },
];

// 当前选中的搜索字段
const selectedSearchFields = ref(['receiptNo', 'status', 'deliveryTimeRange']);

// 可见的搜索字段
const visibleSearchFields = computed(() => {
  return allSearchFields.filter((field) => selectedSearchFields.value.includes(field.key));
});

// 搜索字段变更处理
const handleSearchFieldsChange = (checkedValues) => {
  selectedSearchFields.value = checkedValues;
};

// 搜索表单
const searchForm = reactive({
  receiptNo: '',
  materialName: '',
  materialModel: '',
  deliveryTimeRange: [],
  status: undefined,
  poNo: '',
  deliveryNo: '',
  category: undefined,
  brand: '',
});

// 搜索方法
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

// 重置查询条件
const handleReset = () => {
  Object.keys(searchForm).forEach((key) => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = [];
    } else {
      searchForm[key] = undefined;
    }
  });
  handleSearch();
};

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 视图模式
const viewMode = ref('order'); // 'product' 或 'order'

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
};

// 选中行状态
const selectedRowKeys = ref([]);

// 选中行变化处理
const onSelectChange = (newSelectedRowKeys) => {
  selectedRowKeys.value = newSelectedRowKeys;
};

// 切换视图模式
const switchViewMode = (mode) => {
  viewMode.value = mode;
  fetchData();
};

// 列变更处理
const handleMaterialColumnsChange = (columns) => {
  console.log('Material columns changed:', columns);
};

const handleOrderColumnsChange = (columns) => {
  console.log('Order columns changed:', columns);
};

// Helper function to format date to YYYY-MM-DD HH:mm
const formatDateTime = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// Helper function to generate DN number with timestamp format
const generateDNNumber = (baseDate, index) => {
  const date = new Date(baseDate.getTime() + index * 60 * 1000); // 每个送货单间隔1分钟
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  return `DN${year}${month}${day}${hours}${minutes}${seconds}`;
};

// Helper function to generate SO number with timestamp format
const generateSONumber = (baseDate, index) => {
  const date = new Date(baseDate.getTime() + index * 30 * 1000); // 每个采购单间隔30秒
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  return `SO${year}${month}${day}${hours}${minutes}${seconds}`;
};

// 获取数据
const fetchData = () => {
  loading.value = true;

  // 模拟数据请求，实际项目中请替换为真实API调用
  setTimeout(() => {
    // 扁平化数据结构 - 每个物料作为一行
    const flattenedData = [];
    // 单据数据结构 - 按送货单分组
    const orderData = [];

    // 模拟10个送货单
    Array.from({ length: 10 }).forEach((_, orderIndex) => {
      // 送货单状态
      const statusArray = ['shipped', 'received', 'returning', 'partial_returned'];
      const orderStatus = statusArray[orderIndex % statusArray.length];
      
      // 生成基准时间（最近10天内的随机时间）
      const baseDate = new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000);
      
      // 送货单基本信息
      const receiptInfo = {
        id: `receipt-${orderIndex}`,
        receiptNo: generateDNNumber(baseDate, orderIndex),
        status: orderStatus,
        deliveryTime: formatDateTime(baseDate),
        logisticsCount: Math.floor(Math.random() * 3) + 1,
        materialCount: 0,
        materialItems: []
      };

      // 每个送货单包含1-5个物料
      const materialCount = Math.floor(Math.random() * 5) + 1;
      
      // 物料数量总和将在循环中计算
      receiptInfo.materialCount = 0;

      // 生成物料并添加到扁平数据中
      Array.from({ length: materialCount }).forEach((_, mIndex) => {
        const quantity = Math.floor(Math.random() * 100) + 1;
        
        // 累加送货单中的物料总数量
        receiptInfo.materialCount += quantity;
        
        const materialItem = {
          id: `material-${orderIndex}-${mIndex}`,
          receiptNo: receiptInfo.receiptNo,
          materialName: `测试物料 ${mIndex + 1}`,
          model: `MODEL-${1000 + mIndex}`,
          brand: mIndex % 3 === 0 ? '品牌A' : mIndex % 3 === 1 ? '品牌B' : '品牌C',
          quantity: quantity,
          poNo: generateSONumber(baseDate, orderIndex * 5 + mIndex),
          deliveryNo: `DEL-2023-${3000 + orderIndex}-${mIndex}`,
          status: receiptInfo.status
        };

        flattenedData.push(materialItem);
        receiptInfo.materialItems.push(materialItem);
      });

      orderData.push(receiptInfo);
    });

    // 根据当前视图模式设置表格数据
    if (viewMode.value === 'product') {
      tableData.value = flattenedData;
    } else {
      tableData.value = orderData;
    }
    
    pagination.total = (viewMode.value === 'product' ? flattenedData.length : orderData.length) * 10; // 模拟总数据量
    loading.value = false;
  }, 500);
};

// 操作方法
const handleViewDetail = (record) => {
  router.push({
    path: '/purchase/dnDetail',
    params: { id: record.id },
  });
};

const handleEdit = (record) => {
  console.log('确认收货', record);
};

const handleCancel = (record) => {
  console.log('标记异常', record);
};

const handleExport = () => {
  console.log('导出送货单数据');
};

const handlePrint = () => {
  console.log('打印送货单');
};

// 批量操作处理
const handleBatchReceipt = () => {
  console.log('批量收货', selectedRowKeys.value);
};

const handleBatchReturn = () => {
  console.log('批量退货', selectedRowKeys.value);
};

// 初始化
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.search-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.view-selector {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .search-buttons {
    justify-content: flex-start;
    margin-top: 12px;
  }
}
</style>