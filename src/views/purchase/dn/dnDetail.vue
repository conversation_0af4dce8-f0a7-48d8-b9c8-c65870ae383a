<template>
  <div class="delivery-detail-container">
    <a-page-header :title="'送货单详情 - ' + deliveryData.deliveryNo" :sub-title="'状态：' + getStatusText(deliveryData.status)" @back="goBack">
      <template #extra>
        <a-space>
          <a-button type="primary">收货</a-button>
          <a-button>退货</a-button>
        </a-space>
      </template>
    </a-page-header>

    <!-- 送货单流程 -->
    <!-- <div class="delivery-progress-section">
      <a-card title="送货流程">
        <a-steps :current="currentStep" size="small">
          <a-step v-for="(step, index) in deliverySteps" :key="index" :title="step.title">
            <template #description>
              <div v-if="step.time">{{ formatDateTime(step.time) }}</div>
            </template>
          </a-step>
        </a-steps>
      </a-card>
    </div> -->

    <a-card title="基本信息" class="detail-card">
      <a-descriptions bordered :column="2" size="small">
        <a-descriptions-item label="送货单号">{{ deliveryData.deliveryNo }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(deliveryData.status)">{{ getStatusText(deliveryData.status) }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="物料总数量">{{ totalMaterialQuantity }}</a-descriptions-item>
        <a-descriptions-item label="物流单数量">{{ logisticsRecordCount }}</a-descriptions-item>

        <a-descriptions-item label="创建时间">{{ formatDateTime(deliveryData.createTime) }}</a-descriptions-item>
        <a-descriptions-item label="收货时间" >{{ formatDateTime(deliveryData.stepTimes.reception) }}</a-descriptions-item>
      </a-descriptions>
    </a-card>

    <!-- 物料明细 -->
    <a-card title="物料明细" class="detail-card">
      <a-table :columns="materialColumns" :data-source="deliveryData.items" :pagination="false" size="middle" :scroll="{ x: 1200 }">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getItemStatusColor(record.status)">{{ getItemStatusText(record.status) }}</a-tag>
          </template>
          <template v-if="column.key === 'poNo'">
            <a-button type="link" size="small" @click="viewPurchaseOrderDetail(record.poNo)">{{ record.poNo }}</a-button>
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="link" size="small" @click="handleReturn(record)">退货</a-button>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 收货与物流信息 -->
    <a-card title="收货与物流信息" class="detail-card">
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" tab="收货信息">
          <a-descriptions bordered :column="2" size="small">
            <a-descriptions-item label="收货人">{{ deliveryData.receiverName }}</a-descriptions-item>
            <a-descriptions-item label="联系电话">{{ deliveryData.receiverPhone }}</a-descriptions-item>
            <a-descriptions-item label="收货地址" :span="2">{{ deliveryData.receiverAddress }}</a-descriptions-item>
            <a-descriptions-item label="收货要求" :span="2">{{ deliveryData.receivingTimeRequirement }}</a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
        <a-tab-pane key="logistics-content" tab="物流信息" v-if="deliveryData.logisticsRecords && deliveryData.logisticsRecords.length > 0">
          <a-tabs type="card" :tab-position="'top'">
            <a-tab-pane v-for="(logistics, index) in deliveryData.logisticsRecords" :key="logistics.id" :tab="`物流单 ${index + 1}`">
              <div class="logistics-card">
                <a-descriptions bordered size="small" :column="2">
                  <a-descriptions-item label="物流公司">{{ logistics.provider }}</a-descriptions-item>
                  <a-descriptions-item label="物流单号">{{ logistics.trackingNo }}</a-descriptions-item>
                  <a-descriptions-item label="发货时间">{{ formatDateTime(logistics.shippingTime) }}</a-descriptions-item>
                  <a-descriptions-item label="预计送达时间">{{ formatDate(logistics.estimatedDelivery) }}</a-descriptions-item>
                  <a-descriptions-item label="物流内容" :span="2">{{ logistics.contents }}</a-descriptions-item>
                </a-descriptions>
                <a-timeline style="margin-top: 16px;">
                  <a-timeline-item v-for="track in logistics.trackingInfo" :key="track.time" :color="getLogisticsColor(track.status)">
                    <p class="logistics-time">{{ formatDateTime(track.time) }}</p>
                    <p class="logistics-info">{{ track.description }}</p>
                    <p class="logistics-location">当前位置: {{ track.location }}</p>
                  </a-timeline-item>
                </a-timeline>
              </div>
            </a-tab-pane>
          </a-tabs>
        </a-tab-pane>
        <a-tab-pane key="logistics-empty" tab="物流信息" v-else>
          <a-empty description="暂无物流信息" />
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 相关单据 -->
    <a-card title="相关单据" class="detail-card">
      <a-tabs default-active-key="po">
        <a-tab-pane key="po" tab="采购单">
          <a-table :columns="purchaseOrderColumns" :data-source="deliveryData.relatedPurchaseOrders" :pagination="false" size="small">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-button type="link" @click="viewPurchaseOrderDetail(record.id)">查看详情</a-button>
              </template>
            </template>
          </a-table>
          <a-empty v-if="!deliveryData.relatedPurchaseOrders || deliveryData.relatedPurchaseOrders.length === 0" description="暂无关联采购单" />
        </a-tab-pane>
        <a-tab-pane key="returnOrder" tab="退货单">
          <a-table :columns="returnOrderColumns" :data-source="deliveryData.relatedReturnOrders" :pagination="false" size="small">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-button type="link" @click="viewReturnOrderDetail(record.id)">查看详情</a-button>
              </template>
            </template>
          </a-table>
          <a-empty v-if="!deliveryData.relatedReturnOrders || deliveryData.relatedReturnOrders.length === 0" description="暂无退货单" />
        </a-tab-pane>
      </a-tabs>
    </a-card>

     <!-- 附件和备注 -->
    <a-card title="其他信息" class="detail-card">
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" tab="附件资料">
          <!-- <a-upload-dragger
            v-if="canUpload"
            name="file"
            :multiple="true"
            action="/api/upload"
            @change="handleUploadChange"
          >
            <p class="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此处上传</p>
            <p class="ant-upload-hint">支持单个或批量上传。严禁上传公司内部资料及其他违禁文件。</p>
          </a-upload-dragger> -->
          <a-table :columns="attachmentColumns" :data-source="deliveryData.attachments" :pagination="false" size="small" style="margin-top: 16px;">
             <template #bodyCell="{ column }">
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a>预览</a>
                    <a>下载</a>
                    <!-- <a-popconfirm title="确定删除吗?" @confirm="() => {}">
                      <a style="color: red">删除</a>
                    </a-popconfirm> -->
                  </a-space>
                </template>
              </template>
          </a-table>
        </a-tab-pane>
        <!-- <a-tab-pane key="2" tab="备注信息">
          <a-textarea v-model:value="deliveryData.remarks" placeholder="请输入备注信息" :rows="4" />
        </a-tab-pane> -->
        <a-tab-pane key="3" tab="操作历史">
          <a-list item-layout="horizontal" :data-source="deliveryData.operationLogs" size="small">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <span class="log-user">{{ item.userName }}</span> {{ item.action }}
                  </template>
                  <template #description>
                    <div class="log-time">{{ formatDateTime(item.time) }}</div>
                    <div v-if="item.detail" class="log-detail">{{ item.detail }}</div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- <div class="bottom-actions">
      <a-space>
        <a-button type="primary" v-if="canEdit">保存</a-button>
        <a-button type="danger" v-if="canCancel">取消送货单</a-button>
      </a-space>
    </div> -->

  </div>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { InboxOutlined } from '@ant-design/icons-vue';

const route = useRoute();
const router = useRouter();
const id = ref(route.params.id || route.query.id);
const loading = ref(false);

// 送货单数据
const deliveryData = ref({
  id: '',
  deliveryNo: 'DN2023121211110001',
  poNo: 'PO-2023-0001', // This can be used to link to the main PO
  supplierName: 'ABC供应商',
  status: 'shipped', // draft, pending_dispatch, shipped, partially_received, fully_received, cancelled
  createTime: '2023-10-20 10:00:00',
  // expectedShippingDate, actualShippingDate, expectedArrivalDate are now part of step times or logistics
  actualShippingDate: '2023-10-21 15:30:00', // Example: used for "发货" step time
  // creatorName removed as basic info is gone
  receiverName: '李四',
  receiverPhone: '13912345678',
  receiverAddress: '上海市浦东新区XX路XX号',
  receivingTimeRequirement: '工作日 9:00-17:00',
  remarks: '请注意轻放，内含易碎品。',
  items: [
    {
      id: '1',
      key: '1',
      materialName: '伺服电机',
      model: 'SM2000',
      brand: 'ABB',
      quantity: 2, // Combined quantity, unit removed from here
      poNo: 'SO20231212141111', // Added PO number
      status: 'pending_receipt',
    },
    {
      id: '2',
      key: '2',
      materialName: '工业控制器',
      model: 'IC5000',
      brand: 'Siemens',
      quantity: 1, // Combined quantity, unit removed from here
      poNo: 'SO20231212141111', // Added PO number
      status: 'fully_received',
    },
  ],
  logisticsRecords: [
    {
      id: 'log-1',
      provider: '顺丰速运',
      trackingNo: 'SF1234567890',
      shippingTime: '2023-10-21 15:30:00',
      estimatedDelivery: '2023-10-23',
      contents: '伺服电机 x2, 工业控制器 x1',
      trackingInfo: [
        { time: '2023-10-21 15:30:00', status: 'shipped', description: '已揽收', location: '供应商仓库' },
        { time: '2023-10-22 08:00:00', status: 'in_transit', description: '运输中', location: '上海转运中心' },
        // Example: Add a delivered status for the "收货" step
        // { time: '2023-10-23 10:00:00', status: 'delivered', description: '已签收', location: '收货地址' },
      ],
    },
    // Add more logistics records here if needed
    // {
    //   id: 'log-2',
    //   provider: '圆通速递',
    //   trackingNo: 'YT0987654321',
    //   shippingTime: '2023-10-22 09:00:00',
    //   estimatedDelivery: '2023-10-24',
    //   contents: '传感器 x5',
    //   trackingInfo: [
    //     { time: '2023-10-22 09:00:00', status: 'shipped', description: '已揽收', location: '供应商仓库B' },
    //   ],
    // }
  ],
  attachments: [
    { id: 'att-1', name: '发货单扫描件.pdf', type: 'pdf', size: '1.2MB', uploadTime: '2023-10-20 11:00:00', uploadUser: '王五' }
  ],
  operationLogs: [
    { id: 'log-op-1', userName: '王五', time: '2023-10-20 10:00:00', action: '创建送货单' },
    { id: 'log-op-2', userName: '王五', time: '2023-10-21 15:35:00', action: '更新物流信息，已发货' },
  ],
  // Placeholder for related documents data
  relatedPurchaseOrders: [
    { id: 'po-1', orderNo: 'PO-2023-0001', creationDate: '2023-10-15', totalAmount: 5000, status: '已发货部分' }
  ],
  relatedReturnOrders: [
    // { id: 'ro-1', orderNo: 'RO-2023-001', creationDate: '2023-10-25', reason: '质量问题', status: '待退货' }
  ],
  // Timestamps for delivery steps
  // These should be dynamically determined based on actual process events
  stepTimes: {
    creation: '2023-10-20 10:00:00', // Corresponds to deliveryData.createTime
    shipping: '2023-10-21 15:30:00', // Corresponds to deliveryData.actualShippingDate or logistics shippingTime
    reception: null, // To be filled when reception happens, e.g., first logistics item delivered time
  }
});

// 送货流程步骤 - Enhanced with time
const deliverySteps = computed(() => [
  { title: '创建送货单', time: deliveryData.value.stepTimes?.creation },
  { title: '发货', time: deliveryData.value.stepTimes?.shipping },
  { title: '收货', time: deliveryData.value.stepTimes?.reception }, // This time would be updated when goods are received
]);

// 当前送货单所处的步骤
const currentStep = computed(() => {
  const stepMap = {
    draft: 0,
    pending_dispatch: 1,
    shipped: 2,
    partially_received: 2, // 收货中也算在收货阶段
    fully_received: 3,
    cancelled: -1, // 取消状态不显示在正常流程中
  };
  const step = stepMap[deliveryData.value.status];
  if (step === undefined || step === -1) { // 如果状态未知或已取消，则不突出显示任何步骤
      if (deliveryData.value.status === 'fully_received') return deliverySteps.value.length; // 完成状态
      return -1; // 或者根据需求返回特定值，比如0
  }
  return step;
});

// 物料总数量计算
const totalMaterialQuantity = computed(() => {
  return deliveryData.value.items?.reduce((total, item) => total + (item.quantity || 0), 0) || 0;
});

// 物流单数量计算
const logisticsRecordCount = computed(() => {
  return deliveryData.value.logisticsRecords?.length || 0;
});

// 物料明细表格列定义
const materialColumns = [
  { title: '物料名称', dataIndex: 'materialName', key: 'materialName', width: 180 },
  { title: '型号', dataIndex: 'model', key: 'model', width: 150 },
  { title: '品牌', dataIndex: 'brand', key: 'brand', width: 100 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100 }, // Changed from 发货数量
  { title: '采购单号', dataIndex: 'poNo', key: 'poNo', width: 150 }, // Added
  // { title: '状态', dataIndex: 'status', key: 'status', width: 120 },
  { title: '操作', key: 'action', width: 100, fixed: 'right' }, // Added
];

const attachmentColumns = [
  { title: '文件名', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type', width: 80 },
  { title: '大小', dataIndex: 'size', key: 'size', width: 100 },
  { title: '上传时间', dataIndex: 'uploadTime', key: 'uploadTime', width: 150, customRender: ({text}) => formatDateTime(text) },
  { title: '上传人', dataIndex: 'uploadUser', key: 'uploadUser', width: 100 },
  { title: '操作', key: 'action', width: 150, fixed: 'right' },
];

// Columns for Related Purchase Orders Table
const purchaseOrderColumns = [
  { title: '采购单号', dataIndex: 'orderNo', key: 'orderNo' },
  { title: '创建日期', dataIndex: 'creationDate', key: 'creationDate', customRender: ({text}) => formatDate(text) },
  { title: '总金额', dataIndex: 'totalAmount', key: 'totalAmount' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '操作', key: 'action', width: 120 },
];

// Columns for Related Return Orders Table
const returnOrderColumns = [
  { title: '退货单号', dataIndex: 'orderNo', key: 'orderNo' },
  { title: '创建日期', dataIndex: 'creationDate', key: 'creationDate', customRender: ({text}) => formatDate(text) },
  { title: '退货原因', dataIndex: 'reason', key: 'reason' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '操作', key: 'action', width: 120 },
];

// 权限判断
const canEdit = computed(() => ['draft', 'pending_dispatch'].includes(deliveryData.value.status));
const canCancel = computed(() => !['fully_received', 'cancelled'].includes(deliveryData.value.status));
const canUpload = computed(() => true); // 根据实际业务调整

// 方法
const goBack = () => {
  router.go(-1);
};

const getStatusText = (status) => {
  const statusMap = {
    shipped: '已发货',
    received: '已收货',
    returning: '退货中',
    partial_returned: '已收货（部分退货）',
  };
  return statusMap[status] || status;
};

const getStatusColor = (status) => {
  const colorMap = {
    shipped: 'blue',
    received: 'green',
    returning: 'red',
    partial_returned: 'cyan',
  };
  return colorMap[status] || 'default';
};

const getItemStatusText = (status) => {
  const statusMap = {
    pending_receipt: '待收货',
    partially_received: '部分收货',
    fully_received: '已收货',
  };
  return statusMap[status] || status;
};

const getItemStatusColor = (status) => {
  const colorMap = {
    pending_receipt: 'orange',
    partially_received: 'blue',
    fully_received: 'green',
  };
  return colorMap[status] || 'default';
};

const getLogisticsColor = (status) => {
  const colorMap = {
    shipped: 'blue',
    in_transit: 'orange',
    delivered: 'green', // 假设有delivered状态
    exception: 'red',
  };
  return colorMap[status] || 'blue';
};

const formatDate = (dateStr) => {
  if (!dateStr) return '';
  // 简单实现，实际项目中建议使用日期处理库如dayjs
  return new Date(dateStr).toLocaleDateString();
};

const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  const YYYY = date.getFullYear();
  const MM = (date.getMonth() + 1).toString().padStart(2, '0');
  const DD = date.getDate().toString().padStart(2, '0');
  const HH = date.getHours().toString().padStart(2, '0');
  const mm = date.getMinutes().toString().padStart(2, '0');
  const ss = date.getSeconds().toString().padStart(2, '0');
  return `${YYYY}-${MM}-${DD} ${HH}:${mm}:${ss}`;
};

const handleReturn = (record) => {
  message.info(`对物料 ${record.materialName} (采购单: ${record.poNo}) 进行退货操作`);
  // Add actual return logic here, e.g., open a modal or navigate to a return page
};

const handleUploadChange = (info) => {
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 上传成功`);
    // 更新附件列表
    deliveryData.value.attachments.push({
        id: `att-${Date.now()}`, // 模拟ID
        name: info.file.name,
        type: info.file.type,
        size: `${(info.file.size / 1024 / 1024).toFixed(2)}MB`,
        uploadTime: new Date().toISOString(),
        uploadUser: '当前用户' // 应从实际用户数据获取
    });
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 上传失败`);
  }
};

const fetchDeliveryDetails = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    // 实际应用中会根据 id 获取数据并赋值给 deliveryData.value
    // 例如: const response = await api.getDeliveryDetail(id.value);
    // deliveryData.value = response.data;
    console.log('Fetching delivery details for id:', id.value);
    loading.value = false;
  } catch (error) {
    message.error('获取送货单详情失败');
    loading.value = false;
  }
};

onMounted(() => {
  if (id.value) {
    fetchDeliveryDetails();
  } else {
    // 处理没有ID的情况，可能是创建新送货单的场景，或进行提示
    message.warn('未指定送货单ID');
    // deliveryData.value = { ...initialEmptyDeliveryData }; // 如果需要初始化空表单
  }
});

const viewPurchaseOrderDetail = (id) => {
  // router.push({ name: 'PurchaseOrderDetail', params: { id } }); // Adjust route name as needed
  message.info(`查看采购单详情: ${id}`);
};

const viewReturnOrderDetail = (id) => {
  // router.push({ name: 'ReturnOrderDetail', params: { id } }); // Adjust route name as needed
  message.info(`查看退货单详情: ${id}`);
};
</script>

<style lang="less" scoped>
.delivery-detail-container {
  min-height: 100vh;
}

.delivery-progress-section {
  margin-bottom: 24px;
}

.detail-card {
  margin-bottom: 24px;
  border-radius: 4px;

  .ant-card-head {
    background-color: #fafafa;
  }
}

.info-item {
  display: flex;
  margin-bottom: 16px;
  align-items: center;

  .label {
    color: rgba(0, 0, 0, 0.65);
    min-width: 80px; /* Adjusted min-width for labels in other sections if needed */
    flex-shrink: 0;
    font-weight: normal;
    margin-right: 8px;
  }

  .value {
    flex: 1;
    font-weight: 500;
  }
}

.logistics-card {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  &:last-child {
    margin-bottom: 0;
  }
}

.logistics-time, .logistics-info, .logistics-location {
  margin-bottom: 4px;
  font-size: 13px;
}
.logistics-location {
    color: rgba(0,0,0,0.45);
}


.bottom-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  margin-top: 24px;
  background-color: #fff;
  border-top: 1px solid #e8e8e8;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.log-user {
  font-weight: 500;
  margin-right: 8px;
}
.log-time {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  margin-bottom: 4px;
}
.log-detail {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  margin-top: 4px;
}
</style>
