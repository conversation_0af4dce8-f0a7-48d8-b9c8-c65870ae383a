<template>
  <div class="material-table">
    <div class="table-operations">
      <a-space>
        <!-- <a-button type="primary" @click="$emit('export')">
          <template #icon><export-outlined /></template>导出
        </a-button>
        <a-button @click="$emit('print')">
          <template #icon><printer-outlined /></template>打印
        </a-button> -->
        <a-button @click="handleColumnSetting">
          <template #icon><setting-outlined /></template>列设置
        </a-button>
      </a-space>
    </div>

    <a-table
      :dataSource="tableData"
      :columns="columns"
      :rowKey="record => record.id"
      :pagination="pagination"
      :loading="loading"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      @change="handleTableChange"
    >
      <!-- 物料名称 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'materialName'">
          {{ record.materialName }}
        </template>
        
        <!-- 型号 -->
        <template v-else-if="column.dataIndex === 'model'">
          {{ record.model }}
        </template>
        
        <!-- 品牌 -->
        <template v-else-if="column.dataIndex === 'brand'">
          {{ record.brand }}
        </template>
        
        <!-- 数量 -->
        <template v-else-if="column.dataIndex === 'quantity'">
          {{ record.quantity }}
        </template>
        
        <!-- 送货单号 -->
        <template v-else-if="column.dataIndex === 'receiptNo'">
          <a @click="$emit('viewDetail', record)">{{ formatReceiptNo(record.receiptNo) }}</a>
        </template>
        
        <!-- 所属订单 -->
        <template v-else-if="column.dataIndex === 'poNo'">
          <a @click="viewPO(record)">{{ record.poNo }}</a>
        </template>
        
        <!-- 操作 -->
        <template v-else-if="column.dataIndex === 'action'">
          <a @click="$emit('cancel', record)">退货</a>
        </template>
      </template>
    </a-table>

    <!-- 列设置抽屉 -->
    <a-drawer
      title="列设置"
      placement="right"
      :visible="columnSettingVisible"
      @close="columnSettingVisible = false"
      width="300px"
    >
      <a-checkbox-group v-model:value="visibleColumns" @change="handleColumnsChange">
        <div v-for="col in allColumns" :key="col.dataIndex" style="margin-bottom: 8px;">
          <a-checkbox :value="col.dataIndex">{{ col.title }}</a-checkbox>
        </div>
      </a-checkbox-group>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { ExportOutlined, PrinterOutlined, SettingOutlined } from '@ant-design/icons-vue';

// 定义接收的props
const props = defineProps({
  tableData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
const emit = defineEmits([
  'tableChange', 
  'selectChange', 
  'export', 
  'print', 
  'viewDetail', 
  'edit', 
  'cancel',
  'columnsChange'
]);

// 所有可用列定义
const allColumns = [
  {
    title: '物料名称',
    dataIndex: 'materialName',
    key: 'materialName',
    sorter: true
  },
  {
    title: '型号',
    dataIndex: 'model',
    key: 'model',
    sorter: true
  },
  {
    title: '品牌',
    dataIndex: 'brand',
    key: 'brand',
    sorter: true
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
    sorter: true
  },
  {
    title: '送货单号',
    dataIndex: 'receiptNo',
    key: 'receiptNo',
    sorter: true
  },
  {
    title: '所属订单',
    dataIndex: 'poNo',
    key: 'poNo',
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 100
  }
];

// 当前可见列
const visibleColumns = ref(allColumns.map(col => col.dataIndex));

// 根据可见列筛选显示的列
const columns = computed(() => {
  return allColumns.filter(col => visibleColumns.value.includes(col.dataIndex));
});

// 列设置抽屉可见性
const columnSettingVisible = ref(false);

// 打开列设置抽屉
const handleColumnSetting = () => {
  columnSettingVisible.value = true;
};

// 处理列变更
const handleColumnsChange = (checkedValues) => {
  visibleColumns.value = checkedValues;
  emit('columnsChange', columns.value);
};

// 处理表格变更
const handleTableChange = (pagination, filters, sorter) => {
  emit('tableChange', { pagination, filters, sorter });
};

// 选中行状态
const selectedRowKeys = ref([]);

// 选中行变化处理
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
  emit('selectChange', keys);
};

// 查看关联的采购订单
const viewPO = (record) => {
  // 可以跳转到采购订单详情页
  console.log('查看采购订单:', record.poNo);
};

// 格式化送货单号
const formatReceiptNo = (receiptNo) => {
  // 如果已经是DN开头的正确格式，直接返回
  if (receiptNo && receiptNo.startsWith('DN') && receiptNo.length >= 16) {
    return receiptNo;
  }
  
  // 如果是其他格式，生成新的DN格式
  // 这里可以基于原有编号的一些信息来生成，或者使用当前时间
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const second = String(now.getSeconds()).padStart(2, '0');
  
  // 如果原编号中有数字，可以提取作为后缀，否则使用时间戳
  const numberMatch = receiptNo ? receiptNo.match(/\d+/) : null;
  const suffix = numberMatch ? numberMatch[0].slice(-2).padStart(2, '0') : String(Math.floor(Math.random() * 100)).padStart(2, '0');
  
  return `DN${year}${month}${day}${hour}${minute}${suffix}`;
};

// 组件挂载时初始化
onMounted(() => {
  // 可以在这里执行初始化操作
});
</script>

<style scoped>
.material-table {
  background: #fff;
  min-height: 360px;
}

.table-operations {
  margin-bottom: 16px;
  text-align: right;
}
</style>