<template>
  <div class="order-table">
    <div class="table-operations">
      <div class="operations-left">
        <a-dropdown>
          <a-button type="primary"> 批量操作 <down-outlined /> </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="receipt" @click="$emit('batchReceipt')"> 收货 </a-menu-item>
              <a-menu-item key="return" @click="$emit('batchReturn')"> 退货 </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
      <div class="operations-right">
        <a-button @click="handleColumnSetting">
          <template #icon><setting-outlined /></template>列设置
        </a-button>
      </div>
    </div>

    <a-table
      :dataSource="tableData"
      :columns="columns"
      :rowKey="(record) => record.id"
      :pagination="pagination"
      :loading="loading"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      @change="handleTableChange"
      :expandable="{
        expandedRowKeys: expandedRowKeys,
        onExpand: onExpand,
        expandRowByClick: true,
      }"
    >
      <template #expandedRowRender="{ record }">
        <div style="margin: 12px;">
          <a-table 
          :columns="visibleMaterialColumns" 
          :data-source="record.materialItems" 
          :pagination="false"
          row-key="id"
          bordered
          size="small"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'unitPrice' || column.dataIndex === 'totalPrice'">
              {{ parseFloat(text).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
            </template>
            <template v-if="column.dataIndex === 'shippedQuantity' || column.dataIndex === 'receivedQuantity' || column.dataIndex === 'cancelledQuantity'">
              {{ text || 0 }}
            </template>
            <template v-if="column.dataIndex === 'expectedArrivalTime'">
              {{ record.expectedArrivalTime ? record.expectedArrivalTime : '-' }}
            </template>
            <template v-if="column.dataIndex === 'logisticsStatus'">
              <a-tag :color="getLogisticsStatusColor(record.logisticsStatus)">{{ record.logisticsStatus || '-' }}</a-tag>
            </template>
            <template v-if="column.dataIndex === 'financialStatus'">
              <a-tag :color="getFinancialStatusColor(record.financialStatus)">{{ record.financialStatus || '-' }}</a-tag>
            </template>
            <template v-if="column.dataIndex === 'poNo'">
              <a @click="handleViewPo(record.poNo)">{{ record.poNo }}</a>
            </template>
            <template v-if="column.dataIndex === 'operation'">
              <a-button type="link" size="small" @click="handleMaterialReturn(record)">
                退货
              </a-button>
            </template>
          </template>
        </a-table>
        </div>
        
      </template>
      <!-- 送货单号 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'receiptNo'">
          <a @click="$emit('viewDetail', record)">{{ formatReceiptNo(record.receiptNo) }}</a>
        </template>

        <!-- 状态 -->
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
        </template>

        <!-- 操作 -->
        <template v-else-if="column.dataIndex === 'action'">
          <a-space>
            <a @click="$emit('receipt', record)">收货</a>
            <a @click="$emit('return', record)">退货</a>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 列设置抽屉 -->
    <a-drawer title="列设置" placement="right" :visible="columnSettingVisible" @close="columnSettingVisible = false" width="300px">
      <a-checkbox-group v-model:value="visibleColumns" @change="handleColumnsChange">
        <div v-for="col in allColumns" :key="col.dataIndex" style="margin-bottom: 8px">
          <a-checkbox :value="col.dataIndex">{{ col.title }}</a-checkbox>
        </div>
      </a-checkbox-group>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, h } from 'vue';
import { SettingOutlined, DownOutlined, CheckCircleOutlined, RollbackOutlined } from '@ant-design/icons-vue';
import { Table } from 'ant-design-vue';

// 定义接收的props
const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  pagination: {
    type: Object,
    default: () => ({}),
  },
});

// 定义事件
const emit = defineEmits(['tableChange', 'selectChange', 'viewDetail', 'receipt', 'return', 'columnsChange', 'batchReceipt', 'batchReturn', 'materialReturn', 'viewPo']);

// 展开的行
const expandedRowKeys = ref([]);

// 展开/折叠行处理函数
const onExpand = (expanded, record) => {
  if (expanded) {
    expandedRowKeys.value = [...expandedRowKeys.value, record.id];
  } else {
    expandedRowKeys.value = expandedRowKeys.value.filter(key => key !== record.id);
  }
};

// 物料表格列定义
const materialColumns = [
  { title: '物料名称', dataIndex: 'materialName', key: 'materialName' },
  { title: '型号', dataIndex: 'model', key: 'model' },
  { title: '品牌', dataIndex: 'brand', key: 'brand' },
  { title: '数量', dataIndex: 'quantity', key: 'quantity' },
  { title: '采购单号', dataIndex: 'poNo', key: 'poNo' },
  { title: '操作', dataIndex: 'operation', key: 'operation' }
];

// 可见的物料表格列
const visibleMaterialColumns = computed(() => {
  return materialColumns;
});

// 所有可用列定义
const allColumns = [
  {
    title: '送货单号',
    dataIndex: 'receiptNo',
    key: 'receiptNo',
    // sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    // sorter: true,
    width: 120,
  },
  {
    title: '物料数量',
    dataIndex: 'materialCount',
    key: 'materialCount',
    // sorter: true,
    width: 150,
  },
  {
    title: '发货时间',
    dataIndex: 'deliveryTime',
    key: 'deliveryTime',
    // sorter: true,
    width: 180,
  },
  {
    title: '物流单数量',
    dataIndex: 'logisticsCount',
    key: 'logisticsCount',
    // sorter: true,
    width: 120,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 200,
  },
];

// 当前可见列
const visibleColumns = ref(allColumns.map((col) => col.dataIndex));

// 根据可见列筛选显示的列
const columns = computed(() => {
  return allColumns.filter((col) => visibleColumns.value.includes(col.dataIndex));
});

// 列设置抽屉可见性
const columnSettingVisible = ref(false);

// 打开列设置抽屉
const handleColumnSetting = () => {
  columnSettingVisible.value = true;
};

// 处理列变更
const handleColumnsChange = (checkedValues) => {
  visibleColumns.value = checkedValues;
  emit('columnsChange', columns.value);
};

// 处理表格变更
const handleTableChange = (pagination, filters, sorter) => {
  emit('tableChange', { pagination, filters, sorter });
};

// 选中行状态
const selectedRowKeys = ref([]);

// 选中行变化处理
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
  emit('selectChange', keys);
};

// 根据状态获取颜色
const getStatusColor = (status) => {
  switch (status) {
    case 'shipped':
      return 'blue';
    case 'received':
      return 'green';
    case 'returning':
      return 'red';
    case 'partial_returned':
      return 'cyan';
    default:
      return 'default';
  }
};

// 根据状态获取文字
const getStatusText = (status) => {
  switch (status) {
    case 'shipped':
      return '已发货';
    case 'received':
      return '已收货';
    case 'returning':
      return '退货中';
    case 'partial_returned':
      return '已收货（部分退货）';
    default:
      return '未知状态';
  }
};

// 获取物流状态颜色
const getLogisticsStatusColor = (status) => {
  switch (status) {
    case '已发货':
      return 'green';
    case '运输中':
      return 'blue';
    case '已签收':
      return 'purple';
    case '配送中':
      return 'cyan';
    default:
      return 'default';
  }
};

// 获取财务状态颜色
const getFinancialStatusColor = (status) => {
  switch (status) {
    case '已付款':
      return 'green';
    case '部分付款':
      return 'orange';
    case '待付款':
      return 'red';
    default:
      return 'default';
  }
};

// 处理物料退货
const handleMaterialReturn = (materialRecord) => {
  emit('materialReturn', materialRecord);
};

// 处理查看采购单
const handleViewPo = (poNo) => {
  emit('viewPo', poNo);
};

// 格式化送货单号
const formatReceiptNo = (receiptNo) => {
  // 如果已经是DN开头的正确格式，直接返回
  if (receiptNo && receiptNo.startsWith('DN') && receiptNo.length >= 16) {
    return receiptNo;
  }
  
  // 如果是其他格式，生成新的DN格式
  // 这里可以基于原有编号的一些信息来生成，或者使用当前时间
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const second = String(now.getSeconds()).padStart(2, '0');
  
  // 如果原编号中有数字，可以提取作为后缀，否则使用时间戳
  const numberMatch = receiptNo ? receiptNo.match(/\d+/) : null;
  const suffix = numberMatch ? numberMatch[0].slice(-2).padStart(2, '0') : String(Math.floor(Math.random() * 100)).padStart(2, '0');
  
  return `DN${year}${month}${day}${hour}${minute}${suffix}`;
};

// 组件挂载时初始化
onMounted(() => {
  // 可以在这里执行初始化操作
});
</script>

<style scoped>
.order-table {
  background: #fff;
  min-height: 360px;
}

.table-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
}

.operations-left {
  flex: 1;
}

.operations-right {
  text-align: right;
}
</style>
