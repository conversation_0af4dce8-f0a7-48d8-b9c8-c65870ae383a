<template>
  <div class="rfq-management">
    <div class="action-bar">
      <div class="filter-row">
        <div class="button-group">
          <a-button type="primary" @click="generatePurchaseOrder">
            <template #icon><i class="fas fa-plus"></i></template>
            生成采购订单
          </a-button>
          <a-dropdown>
            <template #overlay>
              <a-menu @click="handleSmartAssign">
                <a-menu-item key="score">评分优先</a-menu-item>
                <a-menu-item key="price">价格优先</a-menu-item>
                <a-menu-item key="delivery">交期优先</a-menu-item>
              </a-menu>
            </template>
            <a-button type="primary">
              <template #icon><i class="fas fa-robot"></i></template>
              智能分配 <i class="fas fa-caret-down"></i>
            </a-button>
          </a-dropdown>
        </div>
        <div class="search-filters">
          <a-input placeholder="搜索询价单..." style="width: 200px" />
          <a-select style="width: 150px" placeholder="全部状态" allowClear>
            <a-select-option value="pending">待发送</a-select-option>
            <a-select-option value="sent">已发送</a-select-option>
            <a-select-option value="quoted">已报价</a-select-option>
            <a-select-option value="purchased">已转采购</a-select-option>
          </a-select>
        </div>
      </div>
    </div>

    <a-table
      :columns="rfqColumns"
      :data-source="rfqData"
      rowKey="id"
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      :pagination="false"
    >
      <template #expandedRowRender="{ record }">
        <a-table
          :columns="materialColumns"
          :data-source="record.materials"
          rowKey="id"
          :pagination="false"
          class="nested-table"
        >
          <template #expandedRowRender="{ record: materialRecord }">
            <a-table
              :columns="quoteColumns"
              :data-source="materialRecord.quotes"
              rowKey="id"
              :pagination="false"
              class="nested-quotes-table"
            >
              <template #bodyCell="{ column, record: quoteRecord }">
                <template v-if="column.key === 'rating'">
                  <span style="color: #e60000; font-weight: bold;">{{ quoteRecord.rating }}</span>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a @click="selectSupplier(quoteRecord, materialRecord)">选择</a>
                  <a-divider type="vertical" />
                  <a @click="showSupplierDetail(quoteRecord)">详情</a>
                </template>
              </template>
            </a-table>
          </template>
          
          <template #bodyCell="{ column, record: materialRecord }">
            <template v-if="column.key === 'action'">
              <a @click="toggleQuotes(materialRecord)">查看报价</a>
              <a-divider type="vertical" />
              <a @click="assignSupplier(materialRecord)">分配供应商</a>
              <a-divider type="vertical" />
              <a @click="removeMaterial(materialRecord)">移除</a>
            </template>
          </template>
        </a-table>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">{{ record.status }}</a-tag>
        </template>
        <template v-else-if="column.key === 'action'">
          <a @click="viewRfq(record)">查看</a>
          <a-divider type="vertical" />
          <a @click="editRfq(record)">编辑</a>
          <a-divider type="vertical" />
          <a @click="deleteRfq(record)">删除</a>
        </template>
      </template>
    </a-table>

    <div class="risk-warning" v-if="riskWarning">
      <i class="fas fa-exclamation-triangle"></i> {{ riskWarning }}
    </div>

    <div class="batch-actions">
      <a-button type="primary" @click="batchCompare" :disabled="!hasSelected">批量比价</a-button>
      <a-button type="primary" @click="batchCreateOrder" :disabled="!hasSelected">批量生成订单</a-button>
      <a-button @click="exportRfq" :disabled="!hasSelected">导出询价单</a-button>
    </div>

    <!-- 查看报价详情的抽屉 -->
    <a-drawer
      :visible="quoteDrawerVisible"
      :title="drawerTitle"
      placement="right"
      width="50%"
      @close="closeQuoteDrawer"
    >
      <div class="material-info-card">
        <h4>物料基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">产品名称</div>
            <div class="info-value">{{ selectedMaterial.name || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">型号</div>
            <div class="info-value">{{ selectedMaterial.model || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">品牌</div>
            <div class="info-value">{{ selectedMaterial.brand || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">产品分类</div>
            <div class="info-value">{{ selectedMaterial.category || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">数量</div>
            <div class="info-value">{{ selectedMaterial.quantity || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">期望交期(天)</div>
            <div class="info-value">{{ selectedMaterial.leadTime || '-' }}</div>
          </div>
        </div>
      </div>

      <a-table
        :columns="quoteColumnsInDrawer"
        :data-source="selectedMaterial.quotes || []"
        rowKey="id"
        :row-selection="{
          type: 'radio',
          selectedRowKeys: selectedQuoteKeys,
          onChange: onQuoteSelectChange
        }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'rating'">
            <span style="color: #e60000; font-weight: bold;">{{ record.rating }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a @click="showSupplierDetail(record)">详情</a>
          </template>
        </template>
      </a-table>

      <template #footer>
        <a-button type="primary" @click="selectBestQuote">选择最优报价</a-button>
        <a-button style="margin-left: 8px" @click="closeQuoteDrawer">关闭</a-button>
      </template>
    </a-drawer>
  </div>
</template>

<script>
export default {
  name: 'RfqManagement',
  data() {
    return {
      selectedRowKeys: [],
      selectedQuoteKeys: [],
      quoteDrawerVisible: false,
      drawerTitle: '物料报价详情',
      selectedMaterial: {},
      riskWarning: '风险检查：传动系统存在独家供应风险，请重点关注',
      
      rfqColumns: [
        { title: '询价单号', dataIndex: 'id', key: 'id' },
        { title: '商品数', dataIndex: 'itemCount', key: 'itemCount' },
        { title: '状态', dataIndex: 'status', key: 'status' },
        { title: '截止日期', dataIndex: 'deadline', key: 'deadline' },
        { title: '操作', key: 'action' }
      ],
      
      materialColumns: [
        { title: '产品名称', dataIndex: 'name', key: 'name' },
        { title: '型号', dataIndex: 'model', key: 'model' },
        { title: '品牌', dataIndex: 'brand', key: 'brand' },
        { title: '产品分类', dataIndex: 'category', key: 'category' },
        { title: '数量', dataIndex: 'quantity', key: 'quantity' },
        { title: '期望交期(天)', dataIndex: 'leadTime', key: 'leadTime' },
        { title: '供应商数量', dataIndex: 'supplierCount', key: 'supplierCount' },
        { title: '备注', dataIndex: 'note', key: 'note' },
        { title: '操作', key: 'action' }
      ],
      
      quoteColumns: [
        { title: '供应商名称', dataIndex: 'supplier', key: 'supplier' },
        { title: '评分', dataIndex: 'rating', key: 'rating' },
        { title: '单价(元)', dataIndex: 'unitPrice', key: 'unitPrice' },
        { title: '总价(元)', dataIndex: 'totalPrice', key: 'totalPrice' },
        { title: '交期(天)', dataIndex: 'leadTime', key: 'leadTime' },
        { title: '供应商备注', dataIndex: 'note', key: 'note' },
        { title: '操作', key: 'action' }
      ],
      
      quoteColumnsInDrawer: [
        { title: '供应商名称', dataIndex: 'supplier', key: 'supplier' },
        { title: '评分', dataIndex: 'rating', key: 'rating' },
        { title: '单价(元)', dataIndex: 'unitPrice', key: 'unitPrice' },
        { title: '总价(元)', dataIndex: 'totalPrice', key: 'totalPrice' },
        { title: '交期(天)', dataIndex: 'leadTime', key: 'leadTime' },
        { title: '供应商备注', dataIndex: 'note', key: 'note' },
        { title: '操作', key: 'action' }
      ],
      
      rfqData: [
        {
          id: 'RFQ-2024-001',
          itemCount: 5,
          status: '已报价',
          deadline: '2024-05-25',
          materials: [
            {
              id: 'MAT-001',
              name: '伺服电机',
              model: 'SM-2000',
              brand: '西门子',
              category: '驱动系统',
              quantity: 10,
              leadTime: 30,
              supplierCount: 2,
              note: '高精度',
              quotes: [
                {
                  id: 'Q-001-1',
                  supplier: '博世自动化',
                  rating: 4.8,
                  unitPrice: 4500,
                  totalPrice: 45000,
                  leadTime: 30,
                  note: '现货可发'
                },
                {
                  id: 'Q-001-2',
                  supplier: '安川电机',
                  rating: 4.5,
                  unitPrice: 4200,
                  totalPrice: 42000,
                  leadTime: 35,
                  note: '送货上门'
                }
              ]
            },
            {
              id: 'MAT-002',
              name: '控制器',
              model: 'PLC-5000',
              brand: '三菱',
              category: '控制系统',
              quantity: 5,
              leadTime: 45,
              supplierCount: 3,
              note: '带显示屏',
              quotes: [
                {
                  id: 'Q-002-1',
                  supplier: '三菱电机',
                  rating: 4.7,
                  unitPrice: 8500,
                  totalPrice: 42500,
                  leadTime: 40,
                  note: '含安装调试'
                },
                {
                  id: 'Q-002-2',
                  supplier: 'ABB',
                  rating: 4.6,
                  unitPrice: 8900,
                  totalPrice: 44500,
                  leadTime: 35,
                  note: '2年质保'
                },
                {
                  id: 'Q-002-3',
                  supplier: '施耐德',
                  rating: 4.4,
                  unitPrice: 7800,
                  totalPrice: 39000,
                  leadTime: 45,
                  note: '可定制化'
                }
              ]
            }
          ]
        },
        {
          id: 'RFQ-2024-002',
          itemCount: 3,
          status: '部分报价',
          deadline: '2024-05-28',
          materials: []
        },
        {
          id: 'RFQ-2024-003',
          itemCount: 2,
          status: '待报价',
          deadline: '2024-06-05',
          materials: []
        }
      ]
    };
  },
  computed: {
    hasSelected() {
      return this.selectedRowKeys.length > 0;
    }
  },
  methods: {
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    
    onQuoteSelectChange(selectedRowKeys) {
      this.selectedQuoteKeys = selectedRowKeys;
    },
    
    getStatusColor(status) {
      const statusColors = {
        '已报价': 'purple',
        '部分报价': 'orange',
        '待报价': 'blue',
        '已转采购': 'green'
      };
      return statusColors[status] || 'blue';
    },
    
    toggleQuotes(material) {
      this.selectedMaterial = material;
      this.drawerTitle = `${material.name} 报价详情`;
      this.quoteDrawerVisible = true;
    },
    
    closeQuoteDrawer() {
      this.quoteDrawerVisible = false;
      this.selectedQuoteKeys = [];
    },
    
    viewRfq(rfq) {
      console.log('查看询价单', rfq);
    },
    
    editRfq(rfq) {
      console.log('编辑询价单', rfq);
    },
    
    deleteRfq(rfq) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除询价单 ${rfq.id} 吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          console.log('删除询价单', rfq);
          this.$message.success(`询价单 ${rfq.id} 已删除`);
        }
      });
    },
    
    assignSupplier(material) {
      console.log('分配供应商', material);
      this.$message.info('打开供应商分配界面');
    },
    
    removeMaterial(material) {
      console.log('移除物料', material);
    },
    
    selectSupplier(quote, material) {
      console.log('选择供应商', quote, material);
      this.$message.success(`已选择 ${quote.supplier} 作为 ${material.name} 的供应商`);
    },
    
    showSupplierDetail(quote) {
      console.log('查看供应商详情', quote);
    },
    
    selectBestQuote() {
      if (this.selectedQuoteKeys.length > 0) {
        const selectedQuote = this.selectedMaterial.quotes.find(q => q.id === this.selectedQuoteKeys[0]);
        if (selectedQuote) {
          this.$message.success(`已选择 ${selectedQuote.supplier} 作为 ${this.selectedMaterial.name} 的供应商`);
          this.closeQuoteDrawer();
        }
      } else {
        this.$message.warning('请先选择一个报价');
      }
    },
    
    generatePurchaseOrder() {
      console.log('生成采购订单');
    },
    
    handleSmartAssign({ key }) {
      const strategies = {
        score: '评分优先',
        price: '价格优先',
        delivery: '交期优先'
      };
      
      this.$message.info(`使用${strategies[key]}策略进行智能分配`);
    },
    
    batchCompare() {
      console.log('批量比价', this.selectedRowKeys);
    },
    
    batchCreateOrder() {
      console.log('批量生成订单', this.selectedRowKeys);
    },
    
    exportRfq() {
      console.log('导出询价单', this.selectedRowKeys);
    }
  }
};
</script>

<style scoped>
.rfq-management {
  width: 100%;
}

.action-bar {
  margin-bottom: 16px;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.button-group {
  display: flex;
  gap: 10px;
}

.search-filters {
  display: flex;
  gap: 10px;
}

.risk-warning {
  background-color: #fff7e6;
  border: 1px solid #ffe58f;
  padding: 8px 12px;
  border-radius: 4px;
  color: #d46b08;
  margin: 15px 0;
}

.batch-actions {
  margin-top: 16px;
  display: flex;
  gap: 10px;
}

.nested-table {
  margin: 10px 0;
}

.nested-quotes-table {
  margin: 5px 0;
  padding-left: 15px;
}

.material-info-card {
  margin-bottom: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.material-info-card h4 {
  margin-top: 0;
  color: #333;
  margin-bottom: 12px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.info-label {
  font-size: 13px;
  color: #888;
}

.info-value {
  font-weight: 500;
  margin-top: 4px;
}
</style>
