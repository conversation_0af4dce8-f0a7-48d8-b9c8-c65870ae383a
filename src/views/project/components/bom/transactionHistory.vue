<template>
  <a-modal
    :visible="visible"
    @update:visible="emit('update:visible', $event)"
    title="交易历史"
    width="1000px"
    :footer="null"
  >
    <div class="material-info-section">
      <h3>物料基本信息</h3>
      <a-descriptions bordered size="small" :column="3">
        <a-descriptions-item label="物料名称">{{ material.name }}</a-descriptions-item>
        <a-descriptions-item label="型号">{{ material.model }}</a-descriptions-item>
        <a-descriptions-item label="品牌">{{ material.brand }}</a-descriptions-item>
        <a-descriptions-item label="产品分类">{{ material.category }}</a-descriptions-item>
        <a-descriptions-item label="规格参数">{{ material.specs || '-' }}</a-descriptions-item>
        <a-descriptions-item label="备注">{{ material.remark || '-' }}</a-descriptions-item>
      </a-descriptions>
    </div>
    
    <div class="transaction-tabs">
      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="inquiry" tab="询价历史">
          <a-table
            :dataSource="inquiryHistoryData"
            :columns="inquiryHistoryColumns"
            size="small"
            :pagination="{ pageSize: 5 }"
            :row-key="record => record.id"
            :scroll="{ x: 1300 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'unitPrice'">
                <span>¥{{ record.unitPrice.toFixed(2) }}</span>
              </template>
              <template v-else-if="column.key === 'totalPrice'">
                <span>¥{{ record.totalPrice.toFixed(2) }}</span>
              </template>
              <template v-else-if="column.key === 'status'">
                <a-tag :color="
                  record.status === '已报价' ? 'green' :
                  record.status === '询价中' ? 'blue' :
                  record.status === '已取消' ? 'red' : 
                  record.status === '已过期' ? 'orange' : 'default'
                ">
                  {{ record.status }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'operation'">
                <a-button type="primary" size="small" @click="handleReInquiry(record)">
                  再次询价
                </a-button>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="purchase" tab="采购历史">
          <a-table
            :dataSource="purchaseHistoryData"
            :columns="purchaseHistoryColumns"
            size="small"
            :pagination="{ pageSize: 5 }"
            :row-key="record => record.id"
            :scroll="{ x: 1300 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'unitPrice'">
                <span>¥{{ record.unitPrice.toFixed(2) }}</span>
              </template>
              <template v-else-if="column.key === 'totalPrice'">
                <span>¥{{ record.totalPrice.toFixed(2) }}</span>
              </template>
              <template v-else-if="column.key === 'status'">
                <a-tag :color="
                  record.status === '已完成' ? 'green' :
                  record.status === '进行中' ? 'blue' :
                  record.status === '待发货' ? 'orange' : 
                  record.status === '已取消' ? 'red' : 'default'
                ">
                  {{ record.status }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'paymentStatus'">
                <a-tag :color="
                  record.paymentStatus === '已付款' ? 'green' :
                  record.paymentStatus === '预付款已付' ? 'blue' :
                  record.paymentStatus === '待付款' ? 'orange' : 'default'
                ">
                  {{ record.paymentStatus }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'qualityCheck'">
                <a-tag :color="
                  record.qualityCheck === '合格' ? 'green' :
                  record.qualityCheck === '待检' ? 'blue' :
                  record.qualityCheck === '不合格' ? 'red' : 'default'
                ">
                  {{ record.qualityCheck }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'operation'">
                <a-button type="primary" size="small" @click="handleRePurchase(record)">
                  再次购买
                </a-button>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, h } from 'vue';
import { message, Modal } from 'ant-design-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  material: {
    type: Object,
    required: true
  },
  inquiryHistoryData: {
    type: Array,
    default: () => []
  },
  purchaseHistoryData: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'reinquiry', 'repurchase']);

const activeTab = ref('inquiry');

// 再次询价
const handleReInquiry = (record) => {
  emit('reinquiry', { material: props.material, record });
};

// 再次购买
const handleRePurchase = (record) => {
  Modal.confirm({
    title: '确认创建采购单',
    content: `确定要从 ${record.supplier} 再次采购 ${props.material.name} (${props.material.model}) 吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      emit('repurchase', { material: props.material, record });
    },
  });
};

// 询价历史表格列
const inquiryHistoryColumns = [
  {
    title: '询价单号',
    dataIndex: 'inquiryNo',
    key: 'inquiryNo',
    width: 120,
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
    width: 70,
  },
  {
    title: '询价时间',
    dataIndex: 'date',
    key: 'date',
    width: 100,
  },
  {
    title: '供应商',
    dataIndex: 'supplier',
    key: 'supplier',
    width: 200,
  },
  {
    title: '报价单价',
    dataIndex: 'unitPrice',
    key: 'unitPrice',
    width: 100,
  },
  {
    title: '总价',
    dataIndex: 'totalPrice',
    key: 'totalPrice',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
  },
  {
    title: '响应时间',
    dataIndex: 'responseTime',
    key: 'responseTime',
    width: 100,
  },
  {
    title: '交期(天)',
    dataIndex: 'deliveryDays',
    key: 'deliveryDays',
    width: 90,
  },
  {
    title: '付款条件',
    dataIndex: 'paymentTerms',
    key: 'paymentTerms',
    width: 150,
  },
  {
    title: '有效期',
    dataIndex: 'validityPeriod',
    key: 'validityPeriod',
    width: 80,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 100,
  }
];

// 采购历史表格列
const purchaseHistoryColumns = [
  {
    title: '采购单号',
    dataIndex: 'purchaseNo',
    key: 'purchaseNo',
    width: 120,
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
    width: 70,
  },
  {
    title: '采购时间',
    dataIndex: 'date',
    key: 'date',
    width: 100,
  },
  {
    title: '供应商',
    dataIndex: 'supplier',
    key: 'supplier',
    width: 200,
  },
  {
    title: '采购单价',
    dataIndex: 'unitPrice',
    key: 'unitPrice',
    width: 100,
  },
  {
    title: '总价',
    dataIndex: 'totalPrice',
    key: 'totalPrice',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
  },
  {
    title: '收货日期',
    dataIndex: 'receiptDate',
    key: 'receiptDate',
    width: 120,
  },
  {
    title: '付款状态',
    dataIndex: 'paymentStatus',
    key: 'paymentStatus',
    width: 100,
  },
  {
    title: '付款日期',
    dataIndex: 'paymentDate',
    key: 'paymentDate',
    width: 100,
  },
  {
    title: '质保期',
    dataIndex: 'warranty',
    key: 'warranty',
    width: 80,
  },
  {
    title: '质检结果',
    dataIndex: 'qualityCheck',
    key: 'qualityCheck',
    width: 90,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 100,
  }
];
</script>

<style scoped>
.material-info-section {
  margin-bottom: 20px;
}

.transaction-tabs {
  margin-top: 20px;
}
</style>
