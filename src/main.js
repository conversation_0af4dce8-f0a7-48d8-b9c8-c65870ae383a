import { createApp } from 'vue'
import App from './App.vue'
import Antd from 'ant-design-vue'
import router from './router'
import 'ant-design-vue/dist/reset.css'
import ECharts from 'vue-echarts'
import { use } from 'echarts/core'
import { <PERSON>vasRenderer } from 'echarts/renderers'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'

use([
  <PERSON>vas<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Pie<PERSON><PERSON>,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const app = createApp(App)
app.use(Antd)
app.use(router)
app.component('v-chart', ECharts)
app.mount('#app')