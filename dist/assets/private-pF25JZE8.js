import{_ as Ve}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{d as t,G as ve,r as f,n as O,y as j,o as qe,c as A,w as a,e as r,a as d,i,l as U,P as Be,b as K,j as $,t as je,h as T}from"./index-DB3jM_eC.js";import{U as Ae}from"./UploadOutlined-U0JvOBVa.js";import{I as te}from"./InboxOutlined-C00g7rQT.js";var Ke={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-600 72h560v208H232V136zm560 480H232V408h560v208zm0 272H232V680h560v208zM304 240a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"database",theme:"outlined"};function pe(g){for(var o=1;o<arguments.length;o++){var p=arguments[o]!=null?Object(arguments[o]):{},u=Object.keys(p);typeof Object.getOwnPropertySymbols=="function"&&(u=u.concat(Object.getOwnPropertySymbols(p).filter(function(b){return Object.getOwnPropertyDescriptor(p,b).enumerable}))),u.forEach(function(b){He(g,b,p[b])})}return g}function He(g,o,p){return o in g?Object.defineProperty(g,o,{value:p,enumerable:!0,configurable:!0,writable:!0}):g[o]=p,g}var ae=function(o,p){var u=pe({},o,p.attrs);return t(ve,pe({},u,{icon:Ke}),null)};ae.displayName="DatabaseOutlined";ae.inheritAttrs=!1;var Ne={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"};function me(g){for(var o=1;o<arguments.length;o++){var p=arguments[o]!=null?Object(arguments[o]):{},u=Object.keys(p);typeof Object.getOwnPropertySymbols=="function"&&(u=u.concat(Object.getOwnPropertySymbols(p).filter(function(b){return Object.getOwnPropertyDescriptor(p,b).enumerable}))),u.forEach(function(b){Ye(g,b,p[b])})}return g}function Ye(g,o,p){return o in g?Object.defineProperty(g,o,{value:p,enumerable:!0,configurable:!0,writable:!0}):g[o]=p,g}var le=function(o,p){var u=me({},o,p.attrs);return t(ve,me({},u,{icon:Ne}),null)};le.displayName="MailOutlined";le.inheritAttrs=!1;const Ge={class:"private-supplier-container"},Je={class:"dashboard-header"},Qe={class:"progress-stats"},We={class:"dashboard-actions"},Xe={key:2,class:"table-actions"},Ze=["onClick"],et=["onClick"],tt=["onClick"],at=["onClick"],lt={class:"upload-container"},nt={class:"ant-upload-drag-icon"},ot={class:"api-integration-form"},st={class:"notice-setting"},it={class:"ant-upload-drag-icon"},ut={class:"upload-container"},rt={class:"ant-upload-drag-icon"},dt={__name:"private",setup(g){const o=f([{key:"1",name:"杭州机电有限公司",contact:"张三",phone:"13800138000",email:"<EMAIL>",status:"已入驻",inviteTime:"2023-08-15",registerTime:"2023-08-17",dataCompletionRate:100,type:"component"},{key:"2",name:"苏州精密机械制造厂",contact:"李四",phone:"13900139000",email:"<EMAIL>",status:"已入驻",inviteTime:"2023-08-15",registerTime:"2023-08-20",dataCompletionRate:85,type:"material"},{key:"3",name:"深圳电子科技有限公司",contact:"王五",phone:"13700137000",email:"<EMAIL>",status:"未入驻",inviteTime:"2023-08-18",registerTime:null,dataCompletionRate:0,type:"component"},{key:"4",name:"宁波自动化设备有限公司",contact:"赵六",phone:"13600136000",email:"<EMAIL>",status:"待邀请",inviteTime:null,registerTime:null,dataCompletionRate:0,type:"service"},{key:"5",name:"无锡工业零部件有限公司",contact:"钱七",phone:"13500135000",email:"<EMAIL>",status:"已入驻",inviteTime:"2023-08-10",registerTime:"2023-08-12",dataCompletionRate:100,type:"component"}]),p=[{title:"供应商名称",dataIndex:"name",key:"name"},{title:"联系人",dataIndex:"contact",key:"contact"},{title:"联系电话",dataIndex:"phone",key:"phone"},{title:"邮箱",dataIndex:"email",key:"email"},{title:"供应商类型",dataIndex:"type",key:"type",customRender:({text:l})=>({component:"零部件供应商",material:"原材料供应商",service:"服务供应商",other:"其他供应商"})[l]||l},{title:"入驻状态",dataIndex:"status",key:"status"},{title:"邀请时间",dataIndex:"inviteTime",key:"inviteTime"},{title:"入驻时间",dataIndex:"registerTime",key:"registerTime"},{title:"数据迁移状态",dataIndex:"dataStatus",key:"dataStatus"},{title:"操作",dataIndex:"action",key:"action"}],u=O({registered:3,pending:1,waitingForInvite:1,registerRate:60}),b=j(()=>o.value.length>0),ce=j(()=>u.pending>0),fe=f(!1),ne=f("all"),M=f(!1),H=f(!1),oe=f([]),se=f("excel"),P=f([]),ge=f(""),ie=f(["email","sms"]),x=O({systemType:void 0,apiUrl:"",authType:"token",authCredential:""}),N=f(!1),Y=f(!1),G=f([]),S=O({supplierId:void 0,dataTypes:["deliveryRecords","contracts"]}),ye=j(()=>o.value.map(l=>({value:l.key,label:l.name}))),be=(l,e)=>((e==null?void 0:e.label)??"").toLowerCase().includes(l.toLowerCase());j(()=>o.value.filter(l=>l.status==="待邀请"));const we=()=>{P.value=[],ge.value="",M.value=!0},_e=l=>{const e=l.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||l.type==="application/vnd.ms-excel",v=l.size/1024/1024<2;return e?v?!1:window.$message.error("文件必须小于2MB!"):window.$message.error("只能上传Excel文件!")},ke=l=>l.size/1024/1024<20?!1:window.$message.error("文件必须小于20MB!"),xe=()=>{console.log("下载模板")},Se=async()=>{if(P.value.length===0)return window.$message.warning("请至少选择一家供应商进行邀请!");H.value=!0;try{await new Promise(_=>setTimeout(_,1500));const l=new Date,e=`${l.getFullYear()}-${String(l.getMonth()+1).padStart(2,"0")}-${String(l.getDate()).padStart(2,"0")}`;P.value.forEach(_=>{const L=o.value.findIndex(E=>E.key===_);L!==-1&&(o.value[L].status="未入驻",o.value[L].inviteTime=e)});const v=P.value.length;u.waitingForInvite-=v,u.pending+=v,window.$message.success(`成功向 ${v} 家供应商发送入驻邀请!`),M.value=!1}catch(l){console.error("邀请发送失败",l),window.$message.error("邀请发送失败，请重试!")}finally{H.value=!1}},Te=async()=>{Y.value=!0;try{await new Promise(e=>setTimeout(e,1500)),console.log("提交数据导入",{supplierId:S.supplierId,dataTypes:S.dataTypes,fileList:G.value}),window.$message.success("数据上传成功!"),N.value=!1;const l=o.value.find(e=>e.key===S.supplierId);l&&(l.dataCompletionRate=100)}catch(l){console.error("数据上传失败",l),window.$message.error("数据上传失败，请重试!")}finally{Y.value=!1}},Ie=async l=>{try{await new Promise(e=>setTimeout(e,800)),console.log("发送催办提醒",l),window.$message.success(`已向 ${l.name} 发送入驻催办提醒!`)}catch(e){console.error("催办发送失败",e),window.$message.error("催办发送失败，请重试!")}},Ue=l=>{console.log("查看供应商详情",l)},$e=async()=>{try{const l=o.value.filter(e=>e.status==="未入驻");console.log("批量催办",l),await new Promise(e=>setTimeout(e,1e3)),window.$message.success(`已向 ${l.length} 家未入驻供应商发送入驻催办提醒!`)}catch(l){console.error("批量催办失败",l),window.$message.error("批量催办失败，请重试!")}},h=f(!1),J=f(!1),D=f("manual"),R=f([]),m=O({name:"",contact:"",phone:"",email:"",type:"component",remark:""}),z=f(!1),Q=f(!1),s=O({key:"",name:"",contact:"",phone:"",email:"",type:"component",remark:""}),Le=()=>{Object.keys(m).forEach(l=>{l!=="type"?m[l]="":m[l]="component"}),D.value="manual",R.value=[],h.value=!0},Ce=l=>{const e=l.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||l.type==="application/vnd.ms-excel",v=l.size/1024/1024<2;return e?v?!1:window.$message.error("文件必须小于2MB!"):window.$message.error("只能上传Excel文件!")},Oe=()=>{console.log("下载供应商导入模板")},Me=async()=>{J.value=!0;try{if(await new Promise(l=>setTimeout(l,1e3)),D.value==="manual"){const l={key:`${o.value.length+1}`,name:m.name,contact:m.contact,phone:m.phone,email:m.email,type:m.type,status:"待邀请",inviteTime:null,registerTime:null,dataCompletionRate:0};o.value.push(l),u.waitingForInvite+=1,window.$message.success("供应商添加成功!")}else console.log("Excel导入供应商",R.value),window.$message.success("成功导入3家供应商!"),u.waitingForInvite+=3;h.value=!1}catch(l){console.error("添加供应商失败",l),window.$message.error("添加供应商失败，请重试!")}finally{J.value=!1}},Pe=l=>{s.key=l.key,s.name=l.name,s.contact=l.contact,s.phone=l.phone,s.email=l.email,s.type=l.type||"component",s.remark=l.remark||"",z.value=!0},he=async()=>{Q.value=!0;try{await new Promise(e=>setTimeout(e,1e3));const l=o.value.findIndex(e=>e.key===s.key);l!==-1&&(o.value[l]={...o.value[l],name:s.name,contact:s.contact,phone:s.phone,email:s.email,type:s.type,remark:s.remark},window.$message.success("供应商信息更新成功!")),z.value=!1}catch(l){console.error("更新供应商失败",l),window.$message.error("更新供应商失败，请重试!")}finally{Q.value=!1}},De=async l=>{try{await new Promise(v=>setTimeout(v,800));const e=o.value.findIndex(v=>v.key===l.key);if(e!==-1){const v=new Date,_=`${v.getFullYear()}-${String(v.getMonth()+1).padStart(2,"0")}-${String(v.getDate()).padStart(2,"0")}`;o.value[e].status="未入驻",o.value[e].inviteTime=_,u.waitingForInvite-=1,u.pending+=1,window.$message.success(`已向 ${l.name} 发送入驻邀请!`)}}catch(e){console.error("邀请发送失败",e),window.$message.error("邀请发送失败，请重试!")}};return qe(()=>{console.log("组件挂载")}),(l,e)=>{const v=r("a-statistic"),_=r("a-button"),L=r("a-card"),E=r("a-col"),ue=r("a-row"),Re=r("a-tag"),ze=r("a-progress"),F=r("a-divider"),Ee=r("a-table"),I=r("a-tab-pane"),W=r("a-tabs"),X=r("a-upload-dragger"),w=r("a-select-option"),V=r("a-select"),c=r("a-form-item"),k=r("a-input"),Z=r("a-radio"),Fe=r("a-radio-group"),q=r("a-form"),C=r("a-checkbox"),re=r("a-checkbox-group"),B=r("a-modal"),de=r("a-textarea");return T(),A("div",Ge,[t(ue,{gutter:16},{default:a(()=>[t(E,{span:24},{default:a(()=>[t(L,{title:"私有供应商管理",class:"dashboard-card"},{default:a(()=>[d("div",Je,[d("div",Qe,[t(v,{title:"已入驻供应商",value:u.registered,suffix:"家",class:"stat-item"},null,8,["value"]),t(v,{title:"未入驻供应商",value:u.pending,suffix:"家",class:"stat-item"},null,8,["value"]),t(v,{title:"待邀请供应商",value:u.waitingForInvite,suffix:"家",class:"stat-item"},null,8,["value"]),t(v,{title:"入驻率",value:u.registerRate,suffix:"%",class:"stat-item"},null,8,["value"])]),d("div",We,[t(_,{type:"primary",onClick:Le,class:"action-btn"},{default:a(()=>[t(U(Be)),e[29]||(e[29]=i(" 添加供应商 "))]),_:1}),t(_,{onClick:we,class:"action-btn",disabled:!b.value},{default:a(()=>[t(U(Ae)),e[30]||(e[30]=i(" 邀请供应商入驻 "))]),_:1},8,["disabled"]),t(_,{onClick:l.showDataImportModal},{default:a(()=>[t(U(ae)),e[31]||(e[31]=i(" 上传合作数据 "))]),_:1},8,["onClick"]),t(_,{onClick:$e,disabled:!ce.value},{default:a(()=>[t(U(le)),e[32]||(e[32]=i(" 批量催办 "))]),_:1},8,["disabled"])])])]),_:1})]),_:1})]),_:1}),t(ue,{gutter:16,class:"row-margin"},{default:a(()=>[t(E,{span:24},{default:a(()=>[t(L,{title:"供应商迁移看板",class:"migration-board"},{default:a(()=>[t(W,{activeKey:ne.value,"onUpdate:activeKey":e[0]||(e[0]=n=>ne.value=n)},{default:a(()=>[t(I,{key:"all",tab:"全部供应商"},{default:a(()=>[t(Ee,{dataSource:o.value,columns:p,pagination:{pageSize:10},loading:fe.value},{bodyCell:a(({column:n,record:y})=>[n.dataIndex==="status"?(T(),K(Re,{key:0,color:y.status==="已入驻"?"success":y.status==="未入驻"?"warning":"default"},{default:a(()=>[i(je(y.status),1)]),_:2},1032,["color"])):$("",!0),n.dataIndex==="dataStatus"?(T(),K(ze,{key:1,percent:y.dataCompletionRate,size:"small",status:y.dataCompletionRate===100?"success":"active"},null,8,["percent","status"])):$("",!0),n.dataIndex==="action"?(T(),A("div",Xe,[y.status==="待邀请"?(T(),A("a",{key:0,onClick:ee=>De(y)},"邀请入驻",8,Ze)):$("",!0),y.status==="待邀请"?(T(),K(F,{key:1,type:"vertical"})):$("",!0),y.status==="未入驻"&&y.inviteTime?(T(),A("a",{key:2,onClick:ee=>Ie(y)},"催办",8,et)):$("",!0),y.status==="未入驻"&&y.inviteTime?(T(),K(F,{key:3,type:"vertical"})):$("",!0),d("a",{onClick:ee=>Ue(y)},"详情",8,tt),t(F,{type:"vertical"}),d("a",{onClick:ee=>Pe(y)},"编辑",8,at)])):$("",!0)]),_:1},8,["dataSource","loading"])]),_:1}),t(I,{key:"registered",tab:"已入驻供应商"}),t(I,{key:"pending",tab:"未入驻供应商"})]),_:1},8,["activeKey"])]),_:1})]),_:1})]),_:1}),t(B,{visible:M.value,"onUpdate:visible":e[8]||(e[8]=n=>M.value=n),title:"一键邀请供应商",onOk:Se,confirmLoading:H.value,width:"700px"},{default:a(()=>[t(W,{activeKey:se.value,"onUpdate:activeKey":e[6]||(e[6]=n=>se.value=n)},{default:a(()=>[t(I,{key:"excel",tab:"Excel导入"},{default:a(()=>[d("div",lt,[t(X,{fileList:oe.value,"onUpdate:fileList":e[1]||(e[1]=n=>oe.value=n),beforeUpload:_e,multiple:!1,showUploadList:!0,accept:".xlsx,.xls"},{default:a(()=>[d("p",nt,[t(U(te))]),e[33]||(e[33]=d("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),e[34]||(e[34]=d("p",{class:"ant-upload-hint"}," 支持Excel格式(.xlsx, .xls)，请确保文件包含供应商名称、联系人、联系电话、电子邮箱等字段 ",-1))]),_:1},8,["fileList"]),d("div",{class:"template-download"},[d("a",{onClick:xe},"下载导入模板")])])]),_:1}),t(I,{key:"api",tab:"API对接"},{default:a(()=>[d("div",ot,[t(q,{model:x,layout:"vertical"},{default:a(()=>[t(c,{label:"系统类型",name:"systemType"},{default:a(()=>[t(V,{value:x.systemType,"onUpdate:value":e[2]||(e[2]=n=>x.systemType=n),placeholder:"请选择系统类型"},{default:a(()=>[t(w,{value:"erp"},{default:a(()=>e[35]||(e[35]=[i("ERP系统")])),_:1}),t(w,{value:"crm"},{default:a(()=>e[36]||(e[36]=[i("CRM系统")])),_:1}),t(w,{value:"other"},{default:a(()=>e[37]||(e[37]=[i("其他系统")])),_:1})]),_:1},8,["value"])]),_:1}),t(c,{label:"API链接",name:"apiUrl"},{default:a(()=>[t(k,{value:x.apiUrl,"onUpdate:value":e[3]||(e[3]=n=>x.apiUrl=n),placeholder:"请输入API链接"},null,8,["value"])]),_:1}),t(c,{label:"认证类型",name:"authType"},{default:a(()=>[t(Fe,{value:x.authType,"onUpdate:value":e[4]||(e[4]=n=>x.authType=n)},{default:a(()=>[t(Z,{value:"token"},{default:a(()=>e[38]||(e[38]=[i("Token")])),_:1}),t(Z,{value:"oauth"},{default:a(()=>e[39]||(e[39]=[i("OAuth")])),_:1}),t(Z,{value:"basic"},{default:a(()=>e[40]||(e[40]=[i("Basic Auth")])),_:1})]),_:1},8,["value"])]),_:1}),t(c,{label:"认证凭证",name:"authCredential"},{default:a(()=>[t(k,{value:x.authCredential,"onUpdate:value":e[5]||(e[5]=n=>x.authCredential=n),placeholder:"请输入认证凭证"},null,8,["value"])]),_:1})]),_:1},8,["model"])])]),_:1})]),_:1},8,["activeKey"]),d("div",st,[t(F),e[43]||(e[43]=d("h4",null,"通知设置",-1)),t(re,{value:ie.value,"onUpdate:value":e[7]||(e[7]=n=>ie.value=n)},{default:a(()=>[t(C,{value:"email"},{default:a(()=>e[41]||(e[41]=[i("邮件通知")])),_:1}),t(C,{value:"sms"},{default:a(()=>e[42]||(e[42]=[i("短信通知")])),_:1})]),_:1},8,["value"])])]),_:1},8,["visible","confirmLoading"]),t(B,{visible:N.value,"onUpdate:visible":e[12]||(e[12]=n=>N.value=n),title:"上传供应商历史合作数据",onOk:Te,confirmLoading:Y.value,width:"700px"},{default:a(()=>[t(q,{model:S,layout:"vertical"},{default:a(()=>[t(c,{label:"选择供应商",name:"supplierId"},{default:a(()=>[t(V,{value:S.supplierId,"onUpdate:value":e[9]||(e[9]=n=>S.supplierId=n),placeholder:"请选择供应商","show-search":"",options:ye.value,"filter-option":be},null,8,["value","options"])]),_:1}),t(c,{label:"数据类型",name:"dataType"},{default:a(()=>[t(re,{value:S.dataTypes,"onUpdate:value":e[10]||(e[10]=n=>S.dataTypes=n)},{default:a(()=>[t(C,{value:"deliveryRecords"},{default:a(()=>e[44]||(e[44]=[i("交货记录")])),_:1}),t(C,{value:"contracts"},{default:a(()=>e[45]||(e[45]=[i("合同数据")])),_:1}),t(C,{value:"qualityRecords"},{default:a(()=>e[46]||(e[46]=[i("质量记录")])),_:1}),t(C,{value:"contactInfo"},{default:a(()=>e[47]||(e[47]=[i("联系人信息")])),_:1})]),_:1},8,["value"])]),_:1}),t(c,{label:"选择文件",name:"fileList"},{default:a(()=>[t(X,{fileList:G.value,"onUpdate:fileList":e[11]||(e[11]=n=>G.value=n),beforeUpload:ke,multiple:!0,showUploadList:!0},{default:a(()=>[d("p",it,[t(U(te))]),e[48]||(e[48]=d("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),e[49]||(e[49]=d("p",{class:"ant-upload-hint"}," 支持Excel、CSV、PDF等格式文件，文件大小不超过20MB ",-1))]),_:1},8,["fileList"])]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirmLoading"]),t(B,{visible:h.value,"onUpdate:visible":e[21]||(e[21]=n=>h.value=n),title:"添加供应商",onOk:Me,confirmLoading:J.value,width:"700px"},{default:a(()=>[t(W,{activeKey:D.value,"onUpdate:activeKey":e[20]||(e[20]=n=>D.value=n)},{default:a(()=>[t(I,{key:"manual",tab:"手动添加"},{default:a(()=>[t(q,{model:m,layout:"vertical"},{default:a(()=>[t(c,{label:"供应商名称",name:"name",rules:[{required:!0,message:"请输入供应商名称"}]},{default:a(()=>[t(k,{value:m.name,"onUpdate:value":e[13]||(e[13]=n=>m.name=n),placeholder:"请输入供应商名称"},null,8,["value"])]),_:1}),t(c,{label:"联系人",name:"contact",rules:[{required:!0,message:"请输入联系人姓名"}]},{default:a(()=>[t(k,{value:m.contact,"onUpdate:value":e[14]||(e[14]=n=>m.contact=n),placeholder:"请输入联系人姓名"},null,8,["value"])]),_:1}),t(c,{label:"联系电话",name:"phone",rules:[{required:!0,message:"请输入联系电话"}]},{default:a(()=>[t(k,{value:m.phone,"onUpdate:value":e[15]||(e[15]=n=>m.phone=n),placeholder:"请输入联系电话"},null,8,["value"])]),_:1}),t(c,{label:"电子邮箱",name:"email",rules:[{required:!0,message:"请输入电子邮箱"}]},{default:a(()=>[t(k,{value:m.email,"onUpdate:value":e[16]||(e[16]=n=>m.email=n),placeholder:"请输入电子邮箱"},null,8,["value"])]),_:1}),t(c,{label:"供应商类型",name:"type"},{default:a(()=>[t(V,{value:m.type,"onUpdate:value":e[17]||(e[17]=n=>m.type=n),placeholder:"请选择供应商类型"},{default:a(()=>[t(w,{value:"component"},{default:a(()=>e[50]||(e[50]=[i("零部件供应商")])),_:1}),t(w,{value:"material"},{default:a(()=>e[51]||(e[51]=[i("原材料供应商")])),_:1}),t(w,{value:"service"},{default:a(()=>e[52]||(e[52]=[i("服务供应商")])),_:1}),t(w,{value:"other"},{default:a(()=>e[53]||(e[53]=[i("其他供应商")])),_:1})]),_:1},8,["value"])]),_:1}),t(c,{label:"备注",name:"remark"},{default:a(()=>[t(de,{value:m.remark,"onUpdate:value":e[18]||(e[18]=n=>m.remark=n),placeholder:"请输入备注信息",rows:3},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1}),t(I,{key:"excel",tab:"Excel导入"},{default:a(()=>[d("div",ut,[t(X,{fileList:R.value,"onUpdate:fileList":e[19]||(e[19]=n=>R.value=n),beforeUpload:Ce,multiple:!1,showUploadList:!0,accept:".xlsx,.xls"},{default:a(()=>[d("p",rt,[t(U(te))]),e[54]||(e[54]=d("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),e[55]||(e[55]=d("p",{class:"ant-upload-hint"}," 支持Excel格式(.xlsx, .xls)，请确保文件包含供应商名称、联系人、联系电话、电子邮箱等字段 ",-1))]),_:1},8,["fileList"]),d("div",{class:"template-download"},[d("a",{onClick:Oe},"下载导入模板")])])]),_:1})]),_:1},8,["activeKey"])]),_:1},8,["visible","confirmLoading"]),t(B,{visible:z.value,"onUpdate:visible":e[28]||(e[28]=n=>z.value=n),title:"编辑供应商",onOk:he,confirmLoading:Q.value},{default:a(()=>[t(q,{model:s,layout:"vertical"},{default:a(()=>[t(c,{label:"供应商名称",name:"name",rules:[{required:!0,message:"请输入供应商名称"}]},{default:a(()=>[t(k,{value:s.name,"onUpdate:value":e[22]||(e[22]=n=>s.name=n),placeholder:"请输入供应商名称"},null,8,["value"])]),_:1}),t(c,{label:"联系人",name:"contact",rules:[{required:!0,message:"请输入联系人姓名"}]},{default:a(()=>[t(k,{value:s.contact,"onUpdate:value":e[23]||(e[23]=n=>s.contact=n),placeholder:"请输入联系人姓名"},null,8,["value"])]),_:1}),t(c,{label:"联系电话",name:"phone",rules:[{required:!0,message:"请输入联系电话"}]},{default:a(()=>[t(k,{value:s.phone,"onUpdate:value":e[24]||(e[24]=n=>s.phone=n),placeholder:"请输入联系电话"},null,8,["value"])]),_:1}),t(c,{label:"电子邮箱",name:"email",rules:[{required:!0,message:"请输入电子邮箱"}]},{default:a(()=>[t(k,{value:s.email,"onUpdate:value":e[25]||(e[25]=n=>s.email=n),placeholder:"请输入电子邮箱"},null,8,["value"])]),_:1}),t(c,{label:"供应商类型",name:"type"},{default:a(()=>[t(V,{value:s.type,"onUpdate:value":e[26]||(e[26]=n=>s.type=n),placeholder:"请选择供应商类型"},{default:a(()=>[t(w,{value:"component"},{default:a(()=>e[56]||(e[56]=[i("零部件供应商")])),_:1}),t(w,{value:"material"},{default:a(()=>e[57]||(e[57]=[i("原材料供应商")])),_:1}),t(w,{value:"service"},{default:a(()=>e[58]||(e[58]=[i("服务供应商")])),_:1}),t(w,{value:"other"},{default:a(()=>e[59]||(e[59]=[i("其他供应商")])),_:1})]),_:1},8,["value"])]),_:1}),t(c,{label:"备注",name:"remark"},{default:a(()=>[t(de,{value:s.remark,"onUpdate:value":e[27]||(e[27]=n=>s.remark=n),placeholder:"请输入备注信息",rows:3},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirmLoading"])])}}},ft=Ve(dt,[["__scopeId","data-v-810c164f"]]);export{ft as default};
