import{_ as je}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{I as Se}from"./InboxOutlined-C00g7rQT.js";import{z as Pe,c as C,d as s,w as n,e as v,D as $e,L as Re,E as Te,A as qe,p as Ue,P as he,r as u,y as _e,h as r,i as y,j as m,F as T,t as g,b as w,a as p,q as Le,B as Ae}from"./index-DB3jM_eC.js";import{U as Ne}from"./UploadOutlined-U0JvOBVa.js";const De=Pe({name:"BomList",components:{PlusOutlined:he,UploadOutlined:Ne,DownloadOutlined:Ue,DeleteOutlined:qe,ExclamationCircleOutlined:Te,LoadingOutlined:Re,DownOutlined:$e,InboxOutlined:Se},setup(){const e=u([{id:"1",name:"AGV控制系统BOM",code:"BOM-2024-001",projects:[{id:"1",name:"智能AGV物流系统",stage:"进行中"}],status:"completed",standard:42,custom:18,assembly:5,matchRate:98,needConfirm:0,uploadTime:"2024-05-15 14:30",uploader:"李工",version:"v1.0",isLatest:!0,remark:"",materials:[{id:"m1",name:"PLC控制器",model:"CP1E-N40DR-A",brand:"欧姆龙",category:"电子元器件",quantity:2,remark:"",inquiryNo:"RFQ-2024-001"},{id:"m2",name:"交流伺服电机",model:"R88M-K2K030H",brand:"欧姆龙",category:"电气设备",quantity:4,remark:"",inquiryNo:"无"}]},{id:"2",name:"数控机床主轴组件BOM",code:"BOM-2024-002",projects:[{id:"2",name:"高精度数控机床升级",stage:"进行中"}],status:"parsed",standard:28,custom:35,assembly:2,matchRate:85,needConfirm:10,uploadTime:"2024-05-13 09:45",uploader:"张工",version:"v2.1",isLatest:!0,remark:"",materials:[{id:"m3",name:"伺服驱动器",model:"MR-J4-200A",brand:"三菱",category:"电气设备",quantity:2,remark:"",inquiryNo:"无"},{id:"m4",name:"齿轮箱",model:"GR63SMT16",brand:"SEW",category:"机械部件",quantity:2,remark:"高精度",inquiryNo:"无"}]},{id:"3",name:"包装线电气系统BOM",code:"BOM-2024-003",projects:[],status:"analyzing",standard:56,custom:12,assembly:8,matchRate:32,needConfirm:0,uploadTime:"2024-05-12 16:20",uploader:"王工",version:"v1.0",isLatest:!0,remark:"",materials:[]},{id:"4",name:"测试设备传感器BOM",code:"BOM-2024-004",projects:[{id:"4",name:"新型材料测试设备",stage:"进行中"}],status:"completed",standard:24,custom:5,assembly:15,matchRate:100,needConfirm:0,uploadTime:"2024-05-10 11:15",uploader:"赵工",version:"v3.0",isLatest:!0,remark:"",materials:[{id:"m5",name:"钣金外壳",model:"CNC-SHT-001",brand:"定制",category:"机械部件",quantity:1,remark:"304不锈钢",inquiryNo:"RFQ-2024-003"}]},{id:"5",name:"机械臂一期驱动系统BOM",code:"BOM-2024-005",projects:[{id:"5",name:"机械臂一期项目",stage:"已关闭"},{id:"8",name:"机械臂二期项目",stage:"进行中"}],status:"completed",standard:32,custom:14,assembly:6,matchRate:95,needConfirm:3,uploadTime:"2024-05-08 15:40",uploader:"张工",version:"v2.0",isLatest:!0,remark:"",materials:[{id:"m6",name:"观察窗",model:"GL-500x300",brand:"安全玻璃",category:"结构件",quantity:2,remark:"钢化玻璃",inquiryNo:"RFQ-2024-003"}]},{id:"6",name:"半导体设备控制板BOM",code:"BOM-2024-006",projects:[{id:"6",name:"半导体自动化设备",stage:"已关闭"}],status:"completed",standard:65,custom:23,assembly:12,matchRate:99,needConfirm:0,uploadTime:"2024-05-06 10:25",uploader:"刘工",version:"v1.2",isLatest:!0,remark:"",materials:[]},{id:"7",name:"工业机器人控制系统BOM",code:"BOM-2024-007",projects:[{id:"3",name:"工业4.0自动化生产线",stage:"未开始"},{id:"7",name:"智能传感器网络",stage:"进行中"}],status:"parsed",standard:48,custom:27,assembly:9,matchRate:78,needConfirm:15,uploadTime:"2024-05-05 13:50",uploader:"李工",version:"v1.1",isLatest:!0,remark:"",materials:[]}]),t=[{title:"BOM名称/编号",dataIndex:"name",key:"name"},{title:"活跃版本",dataIndex:"version",key:"version",width:100},{title:"更新时间",dataIndex:"uploadTime",key:"uploadTime"},{title:"备注",dataIndex:"remark",key:"remark"},{title:"操作",dataIndex:"action",key:"action",width:250}],ee=u(15),te=u(1),ae=u(10),ne=a=>({parsed:"blue",analyzing:"orange",completed:"green"})[a]||"default",j=a=>({parsed:"已解析",analyzing:"分析中",completed:"已完成"})[a]||"未知",E=a=>(a.standard||0)+(a.custom||0)+(a.assembly||0),b=a=>a.matchRate>=90?"success":a.matchRate>=60?"normal":"exception",J=a=>({未开始:"status-not-started",进行中:"status-in-progress",已关闭:"status-closed"})[a]||"",q=u(!1),M=u(null),V=u(""),O=u([]),H=[{title:"项目名称",dataIndex:"name",key:"name"},{title:"项目编号",dataIndex:"code",key:"code"},{title:"负责人",dataIndex:"manager",key:"manager"},{title:"当前阶段",dataIndex:"stage",key:"stage"},{title:"截止日期",dataIndex:"deadline",key:"deadline"}],U=u([{id:"1",name:"智能AGV物流系统",code:"PRJ-2024-001",manager:"李工",stage:"进行中",deadline:"2024-08-30"},{id:"2",name:"高精度数控机床升级",code:"PRJ-2024-002",manager:"张工",stage:"进行中",deadline:"2024-09-15"},{id:"3",name:"工业4.0自动化生产线",code:"PRJ-2024-003",manager:"李工",stage:"未开始",deadline:"2024-11-20"},{id:"4",name:"新型材料测试设备",code:"PRJ-2024-004",manager:"赵工",stage:"进行中",deadline:"2024-12-30"},{id:"5",name:"机械臂一期项目",code:"PRJ-2024-005",manager:"张工",stage:"已关闭",deadline:"2024-10-25"},{id:"6",name:"半导体自动化设备",code:"PRJ-2024-006",manager:"刘工",stage:"已关闭",deadline:"2024-07-15"},{id:"7",name:"智能传感器网络",code:"PRJ-2024-007",manager:"王工",stage:"进行中",deadline:"2025-01-15"},{id:"8",name:"机械臂二期项目",code:"PRJ-2024-008",manager:"张工",stage:"进行中",deadline:"2025-03-20"}]),G=_e(()=>{if(!V.value)return U.value.filter(i=>i.stage==="进行中");const a=V.value.toLowerCase();return U.value.filter(i=>i.stage==="进行中"&&(i.name.toLowerCase().includes(a)||i.code.toLowerCase().includes(a)))}),N=u(!1),x=u([]),W=[{title:"版本号",dataIndex:"version",key:"version"},{title:"更新时间",dataIndex:"createTime",key:"createTime"},{title:"更新人",dataIndex:"creator",key:"creator"},{title:"操作",dataIndex:"action",key:"action",width:180}],h=u(!1),S=u("create"),k=u({name:"",version:"v1.0",remark:""}),Q=[{title:"序号",dataIndex:"index",width:60,align:"center"},{title:"产品名称",dataIndex:"productName"},{title:"型号 *",dataIndex:"model"},{title:"品牌 *",dataIndex:"brand"},{title:"产品分类 *",dataIndex:"category"},{title:"数量 *",dataIndex:"quantity",width:100},{title:"备注",dataIndex:"remark"},{title:"操作",dataIndex:"action",width:60,align:"center"}],$=u([{value:"Siemens"},{value:"ABB"},{value:"Schneider"},{value:"三菱"},{value:"欧姆龙"}]),X=u([{value:"电气元件"},{value:"机械传动"},{value:"传感器"},{value:"气动元件"},{value:"液压元件"}]),D=u(!1),P=u(0),R=u("初始化"),_=u("准备解析BOM文件"),Y=[{title:"项目名称/编号",dataIndex:"name",key:"name",width:"70%"},{title:"阶段",dataIndex:"stage",key:"stage",width:"30%"}],L=u(!1),F=u(!1),o=u(null),l=u(null),c=u({items:[],version:"",updateContent:""}),f=[{title:"产品名称",dataIndex:"name",key:"name"},{title:"型号",dataIndex:"model",key:"model"},{title:"品牌",dataIndex:"brand",key:"brand"},{title:"产品分类",dataIndex:"category",key:"category"},{title:"数量",dataIndex:"quantity",key:"quantity"},{title:"备注",dataIndex:"remark",key:"remark"}],A=u([]),se=(a,i)=>{a?A.value=[...A.value,i.id]:A.value=A.value.filter(d=>d!==i.id)},le=()=>{S.value="create",k.value={name:"",version:"v1.0",remark:""},h.value=!0},ie=()=>{D.value=!0,P.value=0,R.value="初始化",_.value="准备解析BOM文件";const a=setInterval(()=>{P.value+=5,P.value>30&&P.value<=60?(R.value="物料解析中",_.value="正在识别BOM中的物料信息"):P.value>60&&(R.value="物料匹配中",_.value="正在匹配数据库中已有商品"),P.value>=100&&(clearInterval(a),setTimeout(()=>{D.value=!1},1e3))},300)},de=a=>{M.value=a,O.value=a.projects?a.projects.map(i=>i.id):[],V.value="",q.value=!0},re=a=>{O.value=a},ue=()=>{if(!M.value)return;const a=e.value.findIndex(i=>i.id===M.value.id);if(a!==-1){const i=G.value.filter(d=>O.value.includes(d.id));e.value[a].projects=i.map(d=>({id:d.id,name:d.name,stage:d.stage}))}q.value=!1},me=a=>{M.value=a;const i=a.version;if(x.value=[{id:"1",version:i,createTime:a.uploadTime,creator:a.uploader,description:"当前版本",isActive:!0},{id:"2",version:i,createTime:a.uploadTime,creator:a.uploader,description:"旧版本",isActive:!1}],i!=="v1.0"){const d=parseInt(i.substring(1,2)),B=parseInt(i.substring(3));for(let I=B-1;I>=0;I--)x.value.push({id:`${x.value.length+1}`,version:`v${d}.${I}`,createTime:"2024-05-01 10:00",creator:a.uploader,description:"历史版本",isActive:!1});d>1&&x.value.push({id:`${x.value.length+1}`,version:"v1.0",createTime:"2024-04-15 09:30",creator:a.uploader,description:"初始版本",isActive:!1})}N.value=!0},ve=()=>{if(!M.value)return;const a=M.value.version,i=parseInt(a.substring(1,2)),d=parseInt(a.substring(3)),B=`v${i}.${d+1}`;x.value.forEach(K=>K.isActive=!1),x.value.unshift({id:`${x.value.length+1}`,version:B,createTime:new Date().toLocaleString(),creator:"当前用户",description:"新版本",isActive:!0});const I=e.value.findIndex(K=>K.id===M.value.id);I!==-1&&(e.value[I].version=B)},pe=a=>{var i;z.value={version:a.version,updateContent:a.description||"",items:((i=M.value)==null?void 0:i.materials)||[]},L.value=!0,c.value={items:[...z.value.items],version:z.value.version,updateContent:z.value.updateContent},Z.value=!0},ce=a=>{x.value.forEach(d=>d.isActive=!1),a.isActive=!0;const i=e.value.findIndex(d=>d.id===M.value.id);i!==-1&&(e.value[i].version=a.version)},ye=()=>{const a=`m${Date.now()}`;k.value.items.push({id:a,name:"",model:"",brand:"",category:"",quantity:1,remark:""})},fe=a=>{const i=k.value.items.findIndex(d=>d.id===a.id);i!==-1&&k.value.items.splice(i,1)},ge=()=>{o.value=null,F.value=!0},be=a=>{a.file.status==="done"?o.value=a.file:o.value=null},ke=()=>{if(!o.value)return;const a=[{id:`m${Date.now()}-1`,name:"伺服电机",model:"MR-J4-10B",brand:"三菱",category:"电气元件",quantity:2,remark:""},{id:`m${Date.now()}-2`,name:"减速箱",model:"R87F40",brand:"SEW",category:"机械传动",quantity:1,remark:""},{id:`m${Date.now()}-3`,name:"传感器",model:"E2E-X10M",brand:"欧姆龙",category:"传感器",quantity:5,remark:""}];k.value.items=[...k.value.items,...a],F.value=!1},Ie=a=>{S.value="edit",l.value=a.id,k.value={name:a.name,version:a.version,remark:a.remark||""},h.value=!0},Ce=a=>{console.log("Delete BOM",a.id)},we=a=>{console.log("Navigate to project",a)},Me=a=>{switch(a){case"未开始":return"status-tag status-not-started";case"进行中":return"status-tag status-in-progress";case"已关闭":return"status-tag status-closed";default:return"status-tag"}},oe=a=>{l.value=a.id,Z.value=!1;const i=e.value.findIndex(d=>d.id===a.id);if(i!==-1){const d=e.value[i].version,B=parseInt(d.substring(1,2)),I=parseInt(d.substring(3)),K=`v${B}.${I+1}`;c.value={items:e.value[i].materials?[...e.value[i].materials]:[],version:K,updateContent:""}}L.value=!0},Be=a=>{let i=null;for(const d of e.value)if(d.materials&&d.materials.some(B=>B.id===a.id)){i=d.id;break}i&&oe(e.value.find(d=>d.id===i))},xe=(a,i)=>{const d=e.value.findIndex(B=>B.id===a);d!==-1&&e.value[d].materials&&(e.value[d].materials=e.value[d].materials.filter(B=>B.id!==i.id))},Ve=()=>{const a=e.value.findIndex(i=>i.id===l.value);if(a!==-1){e.value[a].materials=[...c.value.items],e.value[a].version=c.value.version;const i=c.value.items.filter(I=>I.category==="电子元器件"||I.category==="标准件").length,d=c.value.items.filter(I=>I.category==="机械部件"||I.category==="非标件").length,B=c.value.items.filter(I=>I.category==="结构件"||I.category==="外购组件").length;e.value[a].standard=i,e.value[a].custom=d,e.value[a].assembly=B}L.value=!1},Oe=()=>{if(k.value.name){if(S.value==="create"){const i={id:Date.now().toString(),name:k.value.name,code:`BOM-${new Date().getFullYear()}-${String(e.value.length+1).padStart(3,"0")}`,projects:[],status:"parsed",standard:0,custom:0,assembly:0,matchRate:0,needConfirm:0,uploadTime:new Date().toLocaleString(),uploader:"当前用户",version:k.value.version,isLatest:!0,remark:k.value.remark,materials:[]};e.value.unshift(i)}else{const a=e.value.findIndex(i=>i.id===l.value);a!==-1&&(e.value[a].name=k.value.name,e.value[a].remark=k.value.remark)}h.value=!1}},Z=u(!1),z=u({version:"",updateContent:"",items:[]});return{bomList:e,columns:t,totalItems:ee,currentPage:te,pageSize:ae,linkProjectModalVisible:q,currentBom:M,projectSearchValue:V,projects:U,filteredInProgressProjects:G,selectedProjectKeys:O,projectColumns:H,projectPopoverColumns:Y,bomModalVisible:h,modalMode:S,bomForm:k,bomItemColumns:Q,materialsColumns:f,brandOptions:$,categoryOptions:X,analysisModalVisible:D,analysisProgress:P,currentAnalysisOperation:R,currentAnalysisDetail:_,versionModalVisible:N,versions:x,versionColumns:W,bomItemsModalVisible:L,importBomItemsModalVisible:F,uploadedFile:o,currentBomId:l,bomItemsForm:c,expandedRowKeys:A,isViewOnly:Z,viewingVersionDetails:z,getStatusColor:ne,getStatusText:j,getTotalMaterials:E,getMatchStatus:b,getStageClass:J,getProjectStageClass:Me,showCreateModal:le,showUploadModal:ie,showLinkProjectModal:de,onSelectProject:re,confirmLinkProject:ue,showVersionModal:me,createNewVersion:ve,viewVersion:pe,activateVersion:ce,addBomItem:ye,removeBomItem:fe,saveBom:Oe,showImportBomItemsModal:ge,handleUploadChange:be,confirmImportBomItems:ke,saveBomItems:Ve,editBom:Ie,deleteBom:Ce,goToProjectDetail:we,onExpand:se,showMaterialManagement:oe,editMaterial:Be,deleteMaterial:xe}}}),Fe={class:"bom-list-container"},ze={class:"font-bold"},Ke={class:"text-xs text-gray-500"},Ee={key:0},Je={key:0},Ge=["onClick"],Qe={class:"text-xs text-gray-500"},He={key:1,class:"text-gray-500 text-xs"},We={class:"text-xs text-gray-500"},Xe={key:0,class:"text-error text-xs"},Ye={key:1,class:"text-xs text-gray-500"},Ze=["onClick"],et=["onClick"],tt={class:"text-primary"},at={key:0},nt={key:1,class:"text-gray-500 text-xs"},ot=["onClick"],st=["onClick"],lt={class:"text-center mb-5"},it={class:"mb-4"},dt={class:"flex justify-between mb-1"},rt={class:"mb-4"},ut={class:"text-xs text-gray-500 mt-1"},mt={key:0,class:"mb-4 mt-4"},vt={class:"ant-upload-drag-icon"};function pt(e,t,ee,te,ae,ne){const j=v("a-input"),E=v("a-col"),b=v("a-button"),J=v("a-row"),q=v("plus-outlined"),M=v("a-space"),V=v("a-tag"),O=v("a-table"),H=v("a-popover"),U=v("a-progress"),G=v("exclamation-circle-outlined"),N=v("a-menu-item"),x=v("a-menu"),W=v("down-outlined"),h=v("a-dropdown"),S=v("a-form-item"),k=v("a-textarea"),Q=v("a-form"),$=v("a-modal"),X=v("loading-outlined"),D=v("upload-outlined"),P=v("a-button-group"),R=v("a-auto-complete"),_=v("a-input-number"),Y=v("delete-outlined"),L=v("inbox-outlined"),F=v("a-upload-dragger");return r(),C("div",Fe,[s(J,{gutter:16,class:"search-filter-area"},{default:n(()=>[s(E,{span:6},{default:n(()=>[s(j,{placeholder:"搜索BOM名称/编号",allowClear:""})]),_:1}),s(E,{span:4},{default:n(()=>[s(b,{type:"primary"},{default:n(()=>t[21]||(t[21]=[y("搜索")])),_:1}),s(b,{class:"ml-2"},{default:n(()=>t[22]||(t[22]=[y("重置")])),_:1})]),_:1})]),_:1}),s(J,{class:"action-buttons my-4"},{default:n(()=>[s(M,null,{default:n(()=>[s(b,{type:"primary",onClick:e.showCreateModal},{icon:n(()=>[s(q)]),default:n(()=>[t[23]||(t[23]=y(" 新建 "))]),_:1},8,["onClick"])]),_:1})]),_:1}),s(O,{dataSource:e.bomList,columns:e.columns,rowKey:"id",pagination:{total:e.totalItems,current:e.currentPage,pageSize:e.pageSize,showSizeChanger:!0,pageSizeOptions:["10","20","50"],showTotal:o=>`共 ${o} 条`},expandable:{expandedRowKeys:e.expandedRowKeys,onExpand:e.onExpand,expandRowByClick:!0},onChange:e.handleTableChange},{bodyCell:n(({column:o,record:l})=>[o.dataIndex==="name"?(r(),C(T,{key:0},[p("div",ze,g(l.name),1),p("div",Ke,g(l.code),1)],64)):m("",!0),o.dataIndex==="project"?(r(),C(T,{key:1},[l.projects&&l.projects.length>0?(r(),C("div",Ee,[s(H,{placement:"right"},{content:n(()=>[s(O,{dataSource:l.projects,columns:e.projectPopoverColumns,pagination:!1,size:"small",rowKey:c=>c.id,style:{width:"350px"}},{bodyCell:n(({column:c,record:f})=>[c.dataIndex==="name"?(r(),C("div",Je,[p("a",{onClick:A=>e.goToProjectDetail(f.id),class:"text-primary"},g(f.name),9,Ge),p("div",Qe,g(f.code||"-"),1)])):m("",!0),c.dataIndex==="stage"?(r(),w(V,{key:1,class:Le(e.getProjectStageClass(f.stage))},{default:n(()=>[y(g(f.stage),1)]),_:2},1032,["class"])):m("",!0)]),_:2},1032,["dataSource","columns","rowKey"])]),default:n(()=>[t[24]||(t[24]=p("a",{class:"text-primary"},"查看",-1))]),_:2},1024)])):(r(),C("div",He,"未关联项目"))],64)):m("",!0),o.dataIndex==="status"?(r(),w(V,{key:2,color:e.getStatusColor(l.status),class:"status-badge"},{default:n(()=>[y(g(e.getStatusText(l.status)),1)]),_:2},1032,["color"])):m("",!0),o.dataIndex==="materialTypes"?(r(),C(T,{key:3},[p("div",null,[l.standard?(r(),w(V,{key:0,color:"success"},{default:n(()=>[y("标准件: "+g(l.standard),1)]),_:2},1024)):m("",!0),l.custom?(r(),w(V,{key:1,color:"warning"},{default:n(()=>[y("非标件: "+g(l.custom),1)]),_:2},1024)):m("",!0),l.assembly?(r(),w(V,{key:2,color:"purple"},{default:n(()=>[y("外购组件: "+g(l.assembly),1)]),_:2},1024)):m("",!0)]),p("div",We,"共"+g(e.getTotalMaterials(l))+"个物料",1)],64)):m("",!0),o.dataIndex==="matchRate"?(r(),C(T,{key:4},[p("div",null,"匹配率: "+g(l.matchRate)+"%",1),s(U,{percent:l.matchRate,size:"small",status:e.getMatchStatus(l)},null,8,["percent","status"]),l.needConfirm?(r(),C("div",Xe,[s(G),y(" "+g(l.needConfirm)+"个物料需确认",1)])):m("",!0),l.status==="analyzing"?(r(),C("div",Ye,"正在分析中...")):m("",!0)],64)):m("",!0),o.dataIndex==="action"?(r(),w(M,{key:5},{default:n(()=>[p("a",{onClick:c=>e.showMaterialManagement(l),class:"text-primary"},"更新物料",8,Ze),p("a",{onClick:c=>e.editBom(l),class:"text-primary"},"编辑",8,et),s(h,null,{overlay:n(()=>[s(x,null,{default:n(()=>[s(N,{onClick:c=>e.showVersionModal(l)},{default:n(()=>t[25]||(t[25]=[y("版本管理")])),_:2},1032,["onClick"]),s(N,{onClick:c=>e.deleteBom(l)},{default:n(()=>t[26]||(t[26]=[y("删除")])),_:2},1032,["onClick"])]),_:2},1024)]),default:n(()=>[p("a",tt,[t[27]||(t[27]=y(" 更多 ")),s(W)])]),_:2},1024)]),_:2},1024)):m("",!0)]),expandedRowRender:n(({record:o})=>[s(O,{dataSource:o.materials||[],columns:e.materialsColumns,pagination:!1,size:"small",rowKey:l=>l.id},{bodyCell:n(({column:l,record:c})=>[l.dataIndex==="inquiryNo"?(r(),C(T,{key:0},[c.inquiryNo&&c.inquiryNo!=="无"?(r(),C("span",at,g(c.inquiryNo),1)):(r(),C("span",nt,"未关联询价单"))],64)):m("",!0)]),_:2},1032,["dataSource","columns","rowKey"])]),_:1},8,["dataSource","columns","pagination","expandable","onChange"]),s($,{visible:e.bomModalVisible,"onUpdate:visible":t[3]||(t[3]=o=>e.bomModalVisible=o),title:e.modalMode==="create"?"BOM新建":"BOM编辑",width:"600px",onCancel:t[4]||(t[4]=o=>e.bomModalVisible=!1)},{footer:n(()=>[s(b,{onClick:t[2]||(t[2]=o=>e.bomModalVisible=!1)},{default:n(()=>t[28]||(t[28]=[y("取消")])),_:1}),s(b,{type:"primary",onClick:e.saveBom},{default:n(()=>t[29]||(t[29]=[y("确定")])),_:1},8,["onClick"])]),default:n(()=>[s(Q,{layout:"horizontal","label-col":{span:4},"wrapper-col":{span:18}},{default:n(()=>[s(S,{label:"BOM名称",required:"",name:"bomName"},{default:n(()=>[s(j,{placeholder:"请输入BOM名称",value:e.bomForm.name,"onUpdate:value":t[0]||(t[0]=o=>e.bomForm.name=o)},null,8,["value"])]),_:1}),s(S,{label:"备注",name:"remark"},{default:n(()=>[s(k,{placeholder:"请输入备注信息",value:e.bomForm.remark,"onUpdate:value":t[1]||(t[1]=o=>e.bomForm.remark=o),rows:"3"},null,8,["value"])]),_:1})]),_:1})]),_:1},8,["visible","title"]),s($,{visible:e.linkProjectModalVisible,"onUpdate:visible":t[7]||(t[7]=o=>e.linkProjectModalVisible=o),title:"关联项目",width:"1200px",onCancel:t[8]||(t[8]=o=>e.linkProjectModalVisible=!1)},{footer:n(()=>[t[32]||(t[32]=p("div",{class:"text-xs text-gray-500 mb-2"},'* 只能关联状态为"进行中"的项目',-1)),s(b,{onClick:t[6]||(t[6]=o=>e.linkProjectModalVisible=!1)},{default:n(()=>t[30]||(t[30]=[y("取消")])),_:1}),s(b,{type:"primary",onClick:e.confirmLinkProject},{default:n(()=>t[31]||(t[31]=[y("确定")])),_:1},8,["onClick"])]),default:n(()=>[s(j,{placeholder:"搜索项目名称/编号",value:e.projectSearchValue,"onUpdate:value":t[5]||(t[5]=o=>e.projectSearchValue=o),class:"mb-4"},null,8,["value"]),s(O,{dataSource:e.filteredInProgressProjects,columns:e.projectColumns,pagination:!1,rowKey:"id",rowSelection:{type:"checkbox",selectedRowKeys:e.selectedProjectKeys,onChange:e.onSelectProject}},null,8,["dataSource","columns","rowSelection"])]),_:1},8,["visible"]),s($,{visible:e.versionModalVisible,"onUpdate:visible":t[10]||(t[10]=o=>e.versionModalVisible=o),title:"版本管理",width:"900px",onCancel:t[11]||(t[11]=o=>e.versionModalVisible=!1)},{footer:n(()=>[s(b,{onClick:t[9]||(t[9]=o=>e.versionModalVisible=!1)},{default:n(()=>t[34]||(t[34]=[y("关闭")])),_:1})]),default:n(()=>[s(O,{dataSource:e.versions,columns:e.versionColumns,rowKey:"id",pagination:!1},{bodyCell:n(({column:o,record:l})=>[o.dataIndex==="version"?(r(),C(T,{key:0},[p("span",null,g(l.version),1),l.isActive?(r(),w(V,{key:0,color:"green",class:"ml-2"},{default:n(()=>t[33]||(t[33]=[y("当前活跃")])),_:1})):m("",!0)],64)):m("",!0),o.dataIndex==="action"?(r(),w(M,{key:1},{default:n(()=>[p("a",{onClick:c=>e.viewVersion(l),class:"text-primary"},"查看",8,ot),l.isActive?m("",!0):(r(),C("a",{key:0,onClick:c=>e.activateVersion(l),class:"text-primary"},"设为活跃",8,st))]),_:2},1024)):m("",!0)]),_:1},8,["dataSource","columns"])]),_:1},8,["visible"]),s($,{visible:e.analysisModalVisible,"onUpdate:visible":t[12]||(t[12]=o=>e.analysisModalVisible=o),title:"BOM解析进度",footer:null,closable:!1,maskClosable:!1},{default:n(()=>[p("div",lt,[s(X,{style:{"font-size":"48px",color:"#1890ff"},spin:""})]),p("div",it,[p("div",dt,[t[35]||(t[35]=p("span",null,"解析进度",-1)),p("span",null,g(e.analysisProgress)+"%",1)]),s(U,{percent:e.analysisProgress},null,8,["percent"])]),p("div",rt,[p("div",null,"当前操作: "+g(e.currentAnalysisOperation),1),p("div",ut,g(e.currentAnalysisDetail),1)]),t[36]||(t[36]=p("div",{class:"mt-6 text-xs text-gray-500"},"解析完成后将自动为您生成BOM分析报告",-1))]),_:1},8,["visible"]),s($,{visible:e.bomItemsModalVisible,"onUpdate:visible":t[16]||(t[16]=o=>e.bomItemsModalVisible=o),title:e.isViewOnly?"版本详情":"更新物料",width:"1200px",onCancel:t[17]||(t[17]=o=>e.bomItemsModalVisible=!1)},{footer:n(()=>[s(b,{onClick:t[15]||(t[15]=o=>e.bomItemsModalVisible=!1)},{default:n(()=>[y(g(e.isViewOnly?"关闭":"取消"),1)]),_:1}),e.isViewOnly?m("",!0):(r(),w(b,{key:0,type:"primary",onClick:e.saveBomItems},{default:n(()=>t[39]||(t[39]=[y("保存")])),_:1},8,["onClick"]))]),default:n(()=>[s(Q,{layout:"horizontal","label-col":{span:2},"wrapper-col":{span:10}},{default:n(()=>[s(S,{label:"版本号",required:"",name:"version"},{default:n(()=>[s(j,{placeholder:"请输入版本号",value:e.bomItemsForm.version,"onUpdate:value":t[13]||(t[13]=o=>e.bomItemsForm.version=o),disabled:!0},null,8,["value"])]),_:1}),s(S,{label:"更新内容",name:"updateContent"},{default:n(()=>[s(k,{placeholder:"请输入更新内容",value:e.bomItemsForm.updateContent,"onUpdate:value":t[14]||(t[14]=o=>e.bomItemsForm.updateContent=o),disabled:e.isViewOnly,rows:"3"},null,8,["value","disabled"])]),_:1})]),_:1}),e.isViewOnly?m("",!0):(r(),C("div",mt,[s(P,null,{default:n(()=>[s(b,{class:"mr-8",type:"primary",onClick:e.addBomItem},{default:n(()=>[s(q),t[37]||(t[37]=y(" 手动添加 "))]),_:1},8,["onClick"]),s(b,{type:"primary",onClick:e.showImportBomItemsModal},{default:n(()=>[s(D),t[38]||(t[38]=y(" 导入物料 "))]),_:1},8,["onClick"])]),_:1})])),t[40]||(t[40]=p("div",{class:"text-primary mb-4"}," ❗️以下表格使用询价宝导入excel的UI交互，本页面仅作字段参考",-1)),s(O,{dataSource:e.bomItemsForm.items,columns:e.isViewOnly?e.materialsColumns:e.bomItemColumns,pagination:!1,rowKey:"id"},Ae({_:2},[e.isViewOnly?void 0:{name:"bodyCell",fn:n(({column:o,record:l,index:c})=>[o.dataIndex==="index"?(r(),C(T,{key:0},[y(g(c+1),1)],64)):m("",!0),o.dataIndex==="productName"?(r(),w(j,{key:1,value:l.name,"onUpdate:value":f=>l.name=f},null,8,["value","onUpdate:value"])):m("",!0),o.dataIndex==="model"?(r(),w(j,{key:2,value:l.model,"onUpdate:value":f=>l.model=f},null,8,["value","onUpdate:value"])):m("",!0),o.dataIndex==="brand"?(r(),w(R,{key:3,value:l.brand,"onUpdate:value":f=>l.brand=f,options:e.brandOptions,placeholder:"搜索品牌",style:{width:"100%"}},null,8,["value","onUpdate:value","options"])):m("",!0),o.dataIndex==="category"?(r(),w(R,{key:4,value:l.category,"onUpdate:value":f=>l.category=f,options:e.categoryOptions,placeholder:"搜索分类",style:{width:"100%"}},null,8,["value","onUpdate:value","options"])):m("",!0),o.dataIndex==="quantity"?(r(),w(_,{key:5,value:l.quantity,"onUpdate:value":f=>l.quantity=f,min:1,style:{width:"100%"}},null,8,["value","onUpdate:value"])):m("",!0),o.dataIndex==="remark"?(r(),w(j,{key:6,value:l.remark,"onUpdate:value":f=>l.remark=f},null,8,["value","onUpdate:value"])):m("",!0),o.dataIndex==="action"?(r(),w(b,{key:7,type:"link",danger:"",onClick:f=>e.removeBomItem(l)},{default:n(()=>[s(Y)]),_:2},1032,["onClick"])):m("",!0)]),key:"0"}]),1032,["dataSource","columns"])]),_:1},8,["visible","title"]),s($,{visible:e.importBomItemsModalVisible,"onUpdate:visible":t[19]||(t[19]=o=>e.importBomItemsModalVisible=o),title:"导入BOM物料",onCancel:t[20]||(t[20]=o=>e.importBomItemsModalVisible=!1)},{footer:n(()=>[s(b,{onClick:t[18]||(t[18]=o=>e.importBomItemsModalVisible=!1)},{default:n(()=>t[43]||(t[43]=[y("取消")])),_:1}),s(b,{type:"primary",onClick:e.confirmImportBomItems,disabled:!e.uploadedFile},{default:n(()=>t[44]||(t[44]=[y("导入")])),_:1},8,["onClick","disabled"])]),default:n(()=>[s(F,{name:"file",multiple:!1,action:"/api/upload",onChange:e.handleUploadChange},{default:n(()=>[p("p",vt,[s(L)]),t[41]||(t[41]=p("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),t[42]||(t[42]=p("p",{class:"ant-upload-hint"},"支持Excel、CSV格式文件",-1))]),_:1},8,["onChange"])]),_:1},8,["visible"])])}const bt=je(De,[["render",pt],["__scopeId","data-v-93eb93c0"]]);export{bt as default};
