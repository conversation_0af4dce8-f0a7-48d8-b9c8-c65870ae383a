import{z as W,H as Z,r as k,n as _,o as G,c as N,a as D,d as n,w as a,l as i,J as R,K as ee,N as te,f as d,g as ie,h as l,O as ae,Q as C,R as S,T as ne,U as oe,V as b,i as r,W as h,D as re,X as ue,Y as P,j as m,b as M,t as v,Z as x,F as A,$ as se}from"./index-DB3jM_eC.js";import{_ as le}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ce={class:"reconciliation-container"},de={class:"search-area"},me={class:"table-area"},pe={style:{"margin-bottom":"16px"}},Ne=["onClick"],ve=["onClick"],fe=["onClick"],ge=["onClick"],ye=["onClick"],Ce=["onClick"],Se=W({__name:"index",setup(be){const O=Z.RangePicker,q=ie(),g=k(!1),T=k([]),u=k([]),c=_({reconciliationNumber:"",customerName:"",status:void 0,periodRange:void 0}),y=_({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0}),$=_({onChange:(e,t)=>{u.value=e},selectedRowKeys:u}),V=[{title:"对账单号",dataIndex:"reconciliationNumber",key:"reconciliationNumber",width:150},{title:"创建时间",dataIndex:"createTime",key:"createTime",width:160},{title:"对账周期",key:"period",width:180,customRender:({record:e})=>`${e.periodStart} 至 ${e.periodEnd}`},{title:"总金额",dataIndex:"totalAmount",key:"totalAmount",width:120},{title:"状态",dataIndex:"status",key:"status",width:100},{title:"开票状态",dataIndex:"invoiceStatus",key:"invoiceStatus",width:100},{title:"操作",key:"action",width:220}],E=e=>({generating:"orange",pending:"blue",confirmed:"green",settled:"purple"})[e]||"default",w=e=>({generating:"生成中",pending:"待对账",confirmed:"已锁定",settled:"已结算"})[e]||"未知状态",U=e=>({not_invoiced:"default",invoicing:"orange",invoiced:"green"})[e]||"default",B=e=>({not_invoiced:"未开票",invoicing:"开票中",invoiced:"已开票"})[e]||"未知状态",z=e=>e.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}),F=()=>{c.reconciliationNumber="",c.customerName="",c.status=void 0,c.periodRange=void 0,p()},p=async()=>{g.value=!0,u.value=[];try{setTimeout(()=>{T.value=[{id:"1",reconciliationNumber:"INV202505001",customerName:"上海智能科技有限公司",customerCode:"CUST001",createTime:"2025-05-01 10:30:00",periodStart:"2025-04-01",periodEnd:"2025-04-30",totalAmount:15e4,status:"pending",invoiceStatus:"not_invoiced",materials:[{id:"101",materialCode:"M001",materialName:"高性能芯片",specification:"A100 型号",unit:"个",quantity:100,unitPrice:1e3,totalPrice:1e5,receiptDate:"2025-04-15",receiptNumber:"DN20250415001",poNumber:"SO20250401001"},{id:"102",materialCode:"M002",materialName:"电路板",specification:"PCB-0023",unit:"片",quantity:200,unitPrice:250,totalPrice:5e4,receiptDate:"2025-04-20",receiptNumber:"DN20250420002",poNumber:"SO20250405002"}]},{id:"2",reconciliationNumber:"INV202504002",customerName:"北京科创企业有限公司",customerCode:"CUST002",createTime:"2025-04-01 09:15:00",periodStart:"2025-03-01",periodEnd:"2025-03-31",totalAmount:86500,status:"confirmed",invoiceStatus:"invoiced",materials:[{id:"201",materialCode:"M003",materialName:"传感器",specification:"S2000",unit:"个",quantity:50,unitPrice:800,totalPrice:4e4,receiptDate:"2025-03-18",receiptNumber:"DN20250318001",poNumber:"SO20250301001"},{id:"202",materialCode:"M004",materialName:"连接器",specification:"CN-100",unit:"套",quantity:150,unitPrice:310,totalPrice:46500,receiptDate:"2025-03-25",receiptNumber:"DN20250325002",poNumber:"SO20250310002"}]},{id:"3",reconciliationNumber:"INV202504003",customerName:"北京科创企业有限公司",customerCode:"CUST002",createTime:"2025-04-01 09:15:00",periodStart:"2025-03-01",periodEnd:"2025-03-31",totalAmount:86500,status:"confirmed",invoiceStatus:"not_invoiced",materials:[{id:"201",materialCode:"M003",materialName:"传感器",specification:"S2000",unit:"个",quantity:50,unitPrice:800,totalPrice:4e4,receiptDate:"2025-03-18",receiptNumber:"DN20250318001",poNumber:"SO20250301001"},{id:"202",materialCode:"M004",materialName:"连接器",specification:"CN-100",unit:"套",quantity:150,unitPrice:310,totalPrice:46500,receiptDate:"2025-03-25",receiptNumber:"DN20250325002",poNumber:"SO20250310002"}]},{id:"4",reconciliationNumber:"INV202505004",customerName:"深圳创新科技有限公司",customerCode:"CUST003",createTime:"2025-05-02 14:20:00",periodStart:"2025-04-01",periodEnd:"2025-04-30",totalAmount:95e3,status:"generating",invoiceStatus:"not_invoiced",materials:[{id:"301",materialCode:"M005",materialName:"显示屏",specification:"LCD-2024",unit:"个",quantity:30,unitPrice:2500,totalPrice:75e3,receiptDate:"2025-04-12",receiptNumber:"DN20250412001",poNumber:"SO20250402001"},{id:"302",materialCode:"M006",materialName:"控制器",specification:"CTRL-500",unit:"个",quantity:40,unitPrice:500,totalPrice:2e4,receiptDate:"2025-04-18",receiptNumber:"DN20250418002",poNumber:"SO20250408002"}]},{id:"5",reconciliationNumber:"INV202503005",customerName:"广州智造有限公司",customerCode:"CUST004",createTime:"2025-03-15 11:45:00",periodStart:"2025-02-01",periodEnd:"2025-02-28",totalAmount:128e3,status:"settled",invoiceStatus:"not_invoiced",materials:[{id:"401",materialCode:"M007",materialName:"电机",specification:"MOTOR-X1",unit:"台",quantity:20,unitPrice:4e3,totalPrice:8e4,receiptDate:"2025-02-10",receiptNumber:"DN20250210001",poNumber:"SO20250201001"},{id:"402",materialCode:"M008",materialName:"变频器",specification:"INV-200",unit:"台",quantity:16,unitPrice:3e3,totalPrice:48e3,receiptDate:"2025-02-20",receiptNumber:"DN20250220002",poNumber:"SO20250205002"}]},{id:"6",reconciliationNumber:"INV202503006",customerName:"杭州精密制造有限公司",customerCode:"CUST005",createTime:"2025-03-20 16:30:00",periodStart:"2025-02-01",periodEnd:"2025-02-28",totalAmount:75600,status:"settled",invoiceStatus:"invoiced",materials:[{id:"501",materialCode:"M009",materialName:"精密轴承",specification:"BEAR-2024",unit:"个",quantity:120,unitPrice:450,totalPrice:54e3,receiptDate:"2025-02-15",receiptNumber:"DN20250215001",poNumber:"SO20250203001"},{id:"502",materialCode:"M010",materialName:"密封圈",specification:"SEAL-100",unit:"个",quantity:360,unitPrice:60,totalPrice:21600,receiptDate:"2025-02-25",receiptNumber:"DN20250225002",poNumber:"SO20250208002"}]},{id:"7",reconciliationNumber:"INV202505007",customerName:"成都智能装备有限公司",customerCode:"CUST006",createTime:"2025-05-03 09:10:00",periodStart:"2025-04-01",periodEnd:"2025-04-30",totalAmount:112e3,status:"generating",invoiceStatus:"not_invoiced",materials:[{id:"601",materialCode:"M011",materialName:"伺服电机",specification:"SERVO-A1",unit:"台",quantity:14,unitPrice:6e3,totalPrice:84e3,receiptDate:"2025-04-08",receiptNumber:"DN20250408001",poNumber:"SO20250401003"},{id:"602",materialCode:"M012",materialName:"编码器",specification:"ENC-500",unit:"个",quantity:28,unitPrice:1e3,totalPrice:28e3,receiptDate:"2025-04-22",receiptNumber:"DN20250422002",poNumber:"SO20250410003"}]}],y.total=7,g.value=!1},500)}catch(e){console.error("获取对账单数据失败:",e),d.error("获取对账单数据失败"),g.value=!1}},Y=e=>{y.current=e.current||1,y.pageSize=e.pageSize||10,p()},L=e=>{d.success(`对账单 ${e.reconciliationNumber} 已确认`),p()},J=e=>{q.push({path:`/purchase/inv/detail/${e.id}`,query:{id:e.id}})},K=()=>{if(u.value.length===0){d.warning("请至少选择一条对账单");return}d.success(`已批量确认选中的 ${u.value.length} 条对账单`),u.value=[],p()},Q=()=>{if(u.value.length===0){d.warning("请至少选择一条对账单");return}d.success(`已批量申请开票 ${u.value.length} 条对账单`),u.value=[],p()},X=()=>{if(u.value.length===0){d.warning("请至少选择一条对账单");return}d.success(`已批量下载 ${u.value.length} 条发票`),u.value=[],p()},j=e=>{d.success(`对账单 ${e.reconciliationNumber} 已付款`),p()},I=e=>{d.success(`已申请开票: ${e.reconciliationNumber}`),p()},H=e=>{d.success(`正在下载发票: ${e.reconciliationNumber}`)};return G(()=>{p()}),(e,t)=>(l(),N("div",ce,[D("div",de,[n(i(R),{layout:"horizontal",model:c,onFinish:p},{default:a(()=>[n(i(ae),null,{default:a(()=>[n(i(C),{span:5},{default:a(()=>[n(i(S),{label:"对账单号","label-col":{span:8},"wrapper-col":{span:16}},{default:a(()=>[n(i(ne),{value:c.reconciliationNumber,"onUpdate:value":t[0]||(t[0]=s=>c.reconciliationNumber=s),placeholder:"请输入对账单号","allow-clear":""},null,8,["value"])]),_:1})]),_:1}),n(i(C),{span:5},{default:a(()=>[n(i(S),{label:"对账状态","label-col":{span:8},"wrapper-col":{span:16}},{default:a(()=>[n(i(oe),{value:c.status,"onUpdate:value":t[1]||(t[1]=s=>c.status=s),placeholder:"请选择状态",style:{width:"100%"},"allow-clear":""},{default:a(()=>[n(i(b),{value:"generating"},{default:a(()=>t[3]||(t[3]=[r("生成中")])),_:1}),n(i(b),{value:"pending"},{default:a(()=>t[4]||(t[4]=[r("待对账")])),_:1}),n(i(b),{value:"confirmed"},{default:a(()=>t[5]||(t[5]=[r("已锁定")])),_:1}),n(i(b),{value:"settled"},{default:a(()=>t[6]||(t[6]=[r("已结算")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),n(i(C),{span:5},{default:a(()=>[n(i(S),{label:"对账周期","label-col":{span:8},"wrapper-col":{span:16}},{default:a(()=>[n(i(O),{value:c.periodRange,"onUpdate:value":t[2]||(t[2]=s=>c.periodRange=s),format:"YYYY-MM-DD",style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),n(i(C),{span:4,style:{display:"flex","align-items":"flex-end"}},{default:a(()=>[n(i(S),null,{default:a(()=>[n(i(h),{type:"primary","html-type":"submit",style:{"margin-right":"8px"}},{default:a(()=>t[7]||(t[7]=[r("查询")])),_:1}),n(i(h),{onClick:F},{default:a(()=>t[8]||(t[8]=[r("重置")])),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),D("div",me,[D("div",pe,[n(i(ee),null,{overlay:a(()=>[n(i(ue),null,{default:a(()=>[n(i(P),{key:"1",onClick:K},{default:a(()=>t[10]||(t[10]=[r("对账")])),_:1}),n(i(P),{key:"2",onClick:Q},{default:a(()=>t[11]||(t[11]=[r("申请开票")])),_:1}),n(i(P),{key:"3",onClick:X},{default:a(()=>t[12]||(t[12]=[r("发票下载")])),_:1})]),_:1})]),default:a(()=>[n(i(h),{type:"primary",style:{"margin-right":"8px"}},{default:a(()=>[t[9]||(t[9]=r(" 批量操作 ")),n(i(re))]),_:1})]),_:1})]),n(i(te),{columns:V,"data-source":T.value,loading:g.value,pagination:y,"row-key":"id",onChange:Y,style:{"margin-top":"16px"},"row-selection":$},{bodyCell:a(({column:s,record:o})=>[s.key==="reconciliationNumber"?(l(),N("a",{key:0,onClick:f=>J(o)},v(o.reconciliationNumber),9,Ne)):m("",!0),s.key==="status"?(l(),M(i(x),{key:1,color:E(o.status)},{default:a(()=>[r(v(w(o.status)),1)]),_:2},1032,["color"])):m("",!0),s.key==="invoiceStatus"?(l(),M(i(x),{key:2,color:U(o.invoiceStatus)},{default:a(()=>[r(v(B(o.invoiceStatus)),1)]),_:2},1032,["color"])):m("",!0),s.key==="totalAmount"?(l(),N(A,{key:3},[r(v(z(o.totalAmount)),1)],64)):m("",!0),s.key==="period"?(l(),N(A,{key:4},[r(v(o.periodStart)+" 至 "+v(o.periodEnd),1)],64)):m("",!0),s.key==="action"?(l(),M(i(se),{key:5},{default:a(()=>[o.status==="pending"?(l(),N("a",{key:0,onClick:f=>L(o)},"对账",8,ve)):m("",!0),o.status==="confirmed"?(l(),N("a",{key:1,onClick:f=>j(o)},"付款",8,fe)):m("",!0),o.status==="confirmed"&&o.invoiceStatus==="not_invoiced"?(l(),N("a",{key:2,onClick:f=>I(o)},"申请开票",8,ge)):m("",!0),o.status==="settled"&&o.invoiceStatus==="not_invoiced"?(l(),N("a",{key:3,onClick:f=>I(o)},"申请开票",8,ye)):m("",!0),o.invoiceStatus==="invoiced"?(l(),N("a",{key:4,onClick:f=>H(o)},"下载发票",8,Ce)):m("",!0)]),_:2},1024)):m("",!0)]),_:1},8,["data-source","loading","pagination","row-selection"])])]))}}),De=le(Se,[["__scopeId","data-v-2c15dfd2"]]);export{De as default};
