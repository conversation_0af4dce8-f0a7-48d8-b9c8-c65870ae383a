import{r as T,o as et,c as i,a as t,_ as at,b as A,F as k,d as s,w as a,e as y,f as M,u as st,g as nt,h as o,i as m,t as l,j as _}from"./index-DB3jM_eC.js";import{_ as ot}from"./_plugin-vue_export-helper-DlAUqK2U.js";const lt={class:"shared-rfq-page"},it={class:"page-header"},dt={class:"header-content"},rt={class:"header-actions"},ut={class:"main-content"},pt={class:"enterprise-info"},ct={class:"enterprise-header"},mt={class:"enterprise-avatar"},_t={class:"enterprise-details"},ft={class:"rfq-info"},vt={class:"info-item"},yt={class:"value"},gt={class:"info-item"},ht={class:"value"},kt={class:"info-item"},xt={class:"value"},bt={class:"info-item"},qt={class:"value"},wt={class:"info-item"},It={class:"value"},Ct={class:"info-item"},Pt={class:"value"},Tt={class:"info-item"},Mt={class:"value"},St={class:"info-item"},At={class:"value"},Dt={class:"info-item"},Nt={class:"value"},Ft={class:"info-item"},Ot={class:"value"},$t={class:"info-item"},Et={class:"value important"},Qt={class:"info-item"},zt={class:"value"},Bt={class:"material-info"},jt={class:"table-toolbar",style:{"margin-bottom":"16px"}},Lt={key:0},Vt={key:1,class:"text-muted"},Kt={key:2},Rt={style:{margin:"12px 0"}},Jt={key:0,class:"supplier-blur-overlay"},Gt={class:"blurred-supplier-table"},Ut={class:"login-prompt-overlay"},Wt={class:"prompt-content"},Ht={key:0},Xt={key:1,class:"text-muted"},Yt={key:0},Zt={key:1,class:"text-muted"},te={key:0},ee={key:1,class:"text-muted"},ae={key:0},se={key:1,class:"text-muted"},ne={key:0},oe={key:1,class:"text-muted"},le={key:0},ie={key:1,class:"text-muted"},de={class:"participation-info"},re={class:"page-footer"},ue={class:"footer-content"},pe={class:"footer-section"},ce={__name:"shared",setup(me){const B=st(),j=nt(),w=T(!1),x=T([]),I=T(!1),N=T({name:"大疆科技",fullName:"深圳市大疆科技有限公司"}),L=T({name:"张三"}),u=T({id:"",rfqNo:"",status:"inProgress",createTime:"",inquiryTime:"",deadline:"",endTime:"",creator:"",contactPhone:"",materialModelCount:0,remark:"",materials:[]}),V=[{title:"物料名称",dataIndex:"name",key:"name",width:180,fixed:"left"},{title:"型号",dataIndex:"model",key:"model",width:180},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"数量",dataIndex:"quantity",key:"quantity",width:80,align:"center"},{title:"询价状态",dataIndex:"status",key:"status",width:100,align:"center"},{title:"期望交期",dataIndex:"expectedDelivery",key:"expectedDelivery",width:100},{title:"接受平替",dataIndex:"acceptAlternative",key:"acceptAlternative",width:100,align:"center"},{title:"供应商数量",dataIndex:"supplierCount",key:"supplierCount",width:120,align:"center"},{title:"备注",dataIndex:"remark",key:"remark",width:150}],F=[{title:"供应商名称",dataIndex:"name",key:"name",width:150},{title:"报价 (¥)",dataIndex:"price",key:"price",width:100,align:"right"},{title:"总价 (¥)",dataIndex:"totalPrice",key:"totalPrice",width:100,align:"right"},{title:"最小起订量",dataIndex:"minOrderQuantity",key:"minOrderQuantity",width:120,align:"center"},{title:"承诺交期",dataIndex:"promisedDelivery",key:"promisedDelivery",width:100},{title:"有效期",dataIndex:"validityPeriod",key:"validityPeriod",width:100},{title:"报价时间",dataIndex:"quoteTime",key:"quoteTime",width:150},{title:"报价状态",dataIndex:"status",key:"status",width:100,align:"center"},{title:"备注",dataIndex:"remark",key:"remark",width:150}],K=(n,e)=>{if(n)x.value.push(e.id);else{const d=x.value.indexOf(e.id);d>-1&&x.value.splice(d,1)}R()},R=()=>{I.value=x.value.length===u.value.materials.length},J=()=>{I.value?(x.value=[],I.value=!1):(x.value=u.value.materials.map(n=>n.id),I.value=!0)},S=n=>n||"",O=n=>({notStarted:"未开始",inProgress:"询价中",accepted:"已采纳",expired:"已过期",invalid:"已失效",cancelled:"已取消"})[n]||n,$=n=>({notStarted:"default",inProgress:"blue",accepted:"green",expired:"orange",invalid:"red",cancelled:"red"})[n]||"default",G=n=>({pending:"blue",quoted:"green",rejected:"red",expired:"orange"})[n]||"default",U=n=>({pending:"待报价",quoted:"已报价",rejected:"已拒绝",expired:"已过期"})[n]||"未知",W=()=>u.value.materials.reduce((n,e)=>n+e.quantity,0),H=()=>{const n=u.value.materials.reduce((e,d)=>{const b=d.suppliers.find(g=>g.status==="accepted");return b?e+b.totalPrice:e},0);return n>0?`¥${n.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})}`:"-"},E=()=>{M.info("跳转到注册页面")},Q=()=>{w.value=!0,M.success("登录成功！")},X=()=>{const n=z();if(n){const e=btoa(JSON.stringify(n));j.push({path:"/supplier/temp-quote",query:{param:e}})}else M.error("分享链接参数错误")},Y=()=>{window.open("https://www.yanxuan.cloud/","_blank")},Z=()=>{M.info("跳转到平台采纳报价页面")},z=()=>{try{const n=B.query.param;if(!n)throw new Error("无效的分享链接");return JSON.parse(atob(n))}catch{return M.error("分享链接格式错误"),null}},tt=async()=>{try{const n=z();if(!n)return;console.log("获取分享的询价单详情，询价单号：",n.rfqNo),await new Promise(e=>setTimeout(e,500)),u.value={id:"1",rfqNo:n.rfqNo||"RFQ-2023-0001",status:"inProgress",createTime:"2023-10-15 09:30:00",inquiryTime:"2023-10-15 10:00:00",deadline:"2023-10-25 18:00:00",endTime:"2023-10-26 18:00:00",creator:"张三",contactPhone:"138****8888",materialModelCount:4,remark:"紧急采购项目，欢迎供应商报价",materials:Array.from({length:4}).map((e,d)=>{const b=Math.floor(Math.random()*100+10),g=Array.from({length:3+Math.floor(Math.random()*2)}).map((f,h)=>{const P=["pending","quoted","rejected","expired"],v=h===0?"quoted":P[h%P.length],D=Math.floor(Math.random()*1e3+100);return{id:`supplier-${d}-${h}`,name:`供应商${String.fromCharCode(65+h)}`,price:v==="quoted"?D:0,totalPrice:v==="quoted"?D*b:0,minOrderQuantity:v==="quoted"?Math.floor(Math.random()*100+10):0,promisedDelivery:v==="quoted"?`${Math.floor(Math.random()*30+15)}天`:"",validityPeriod:v==="quoted"?"2023-11-05":"",quoteTime:v==="quoted"?`2023-10-${16+h} 10:30:00`:"",status:v,remark:v==="quoted"&&h%2===0?"含税价格":""}}),r=g.filter(f=>f.status==="quoted"&&f.price>0),C=r.length>0?{min:Math.min(...r.map(f=>f.price)),max:Math.max(...r.map(f=>f.price))}:null;return{id:`material-${d}`,name:`电子元件 ${d+1}`,model:`MODEL-${100+d}`,brand:d%3===0?"品牌A":d%3===1?"品牌B":"品牌C",quantity:b,expectedDelivery:"30天",status:"inProgress",remark:d%2===0?"紧急采购":"",acceptAlternative:d%3===0,suppliers:g,priceRange:C}})}}catch{M.error("获取询价单详情失败")}};return et(()=>{tt()}),(n,e)=>{const d=y("a-button"),b=y("a-avatar"),g=y("a-card"),r=y("a-col"),C=y("a-tag"),f=y("a-row"),h=y("a-alert"),P=y("a-table"),v=y("a-space"),D=y("a-divider");return o(),i("div",lt,[t("div",it,[t("div",dt,[e[3]||(e[3]=t("div",{class:"logo-section"},[t("img",{src:at,alt:"研选工场",class:"logo-image"})],-1)),t("div",rt,[w.value?(o(),A(d,{key:1,type:"primary",onClick:Z},{default:a(()=>e[2]||(e[2]=[m("前往平台，采纳报价")])),_:1})):(o(),i(k,{key:0},[s(d,{onClick:Q},{default:a(()=>e[0]||(e[0]=[m("登录")])),_:1}),s(d,{type:"primary",onClick:E},{default:a(()=>e[1]||(e[1]=[m("立即注册")])),_:1})],64))])])]),t("div",ut,[t("div",pt,[s(g,null,{default:a(()=>[t("div",ct,[t("div",mt,[s(b,{size:"large",style:{"background-color":"#1890ff"}},{default:a(()=>[m(l(N.value.name.charAt(0)),1)]),_:1})]),t("div",_t,[t("h2",null,l(N.value.fullName),1),t("p",null,l(L.value.name)+"邀请您查看询价单",1)])])]),_:1})]),t("div",ft,[s(g,{title:"询价单详情",class:"detail-card"},{default:a(()=>[s(f,{gutter:24},{default:a(()=>[s(r,{span:8},{default:a(()=>[t("div",vt,[e[4]||(e[4]=t("span",{class:"label"},"询价单号：",-1)),t("span",yt,l(u.value.rfqNo),1)])]),_:1}),s(r,{span:8},{default:a(()=>[t("div",gt,[e[5]||(e[5]=t("span",{class:"label"},"询价状态：",-1)),t("span",ht,[s(C,{color:$(u.value.status)},{default:a(()=>[m(l(O(u.value.status)),1)]),_:1},8,["color"])])])]),_:1}),s(r,{span:8},{default:a(()=>[t("div",kt,[e[6]||(e[6]=t("span",{class:"label"},"创建时间：",-1)),t("span",xt,l(S(u.value.createTime)),1)])]),_:1}),s(r,{span:8},{default:a(()=>[t("div",bt,[e[7]||(e[7]=t("span",{class:"label"},"询价时间：",-1)),t("span",qt,l(S(u.value.inquiryTime)),1)])]),_:1}),s(r,{span:8},{default:a(()=>[t("div",wt,[e[8]||(e[8]=t("span",{class:"label"},"截止时间：",-1)),t("span",It,l(S(u.value.deadline)),1)])]),_:1}),s(r,{span:8},{default:a(()=>[t("div",Ct,[e[9]||(e[9]=t("span",{class:"label"},"结束时间：",-1)),t("span",Pt,l(S(u.value.endTime)),1)])]),_:1}),s(r,{span:8},{default:a(()=>[t("div",Tt,[e[10]||(e[10]=t("span",{class:"label"},"创建人：",-1)),t("span",Mt,l(u.value.creator),1)])]),_:1}),s(r,{span:8},{default:a(()=>[t("div",St,[e[11]||(e[11]=t("span",{class:"label"},"联系电话：",-1)),t("span",At,l(u.value.contactPhone),1)])]),_:1}),s(r,{span:8},{default:a(()=>[t("div",Dt,[e[12]||(e[12]=t("span",{class:"label"},"物料型号数：",-1)),t("span",Nt,l(u.value.materialModelCount)+" 种",1)])]),_:1}),s(r,{span:8},{default:a(()=>[t("div",Ft,[e[13]||(e[13]=t("span",{class:"label"},"物料总数：",-1)),t("span",Ot,l(W())+" 件",1)])]),_:1}),s(r,{span:8},{default:a(()=>[t("div",$t,[e[14]||(e[14]=t("span",{class:"label"},"已采纳总价：",-1)),t("span",Et,l(H()),1)])]),_:1}),s(r,{span:24},{default:a(()=>[t("div",Qt,[e[15]||(e[15]=t("span",{class:"label"},"备注：",-1)),t("span",zt,l(u.value.remark||"-"),1)])]),_:1})]),_:1})]),_:1})]),t("div",Bt,[s(g,{title:"物料清单"},{default:a(()=>[s(h,{message:w.value?"以下为物料明细及供应商报价情况，点击展开查看各供应商的详细报价。":"以下为物料明细，登录后可查看供应商报价详情。",type:"info","show-icon":"",style:{"margin-bottom":"16px"}},null,8,["message"]),t("div",jt,[s(d,{type:"primary",onClick:J,icon:I.value?"up":"down"},{default:a(()=>[m(l(I.value?"全部收起":"全部展开"),1)]),_:1},8,["icon"])]),s(P,{columns:V,"data-source":u.value.materials,size:"middle",pagination:{pageSize:10},"row-key":"id",bordered:"",scroll:{x:1200},expandedRowKeys:x.value,onExpand:K},{bodyCell:a(({column:q,record:p})=>[q.dataIndex==="acceptAlternative"?(o(),i(k,{key:0},[m(l(p.acceptAlternative?"是":"否"),1)],64)):_("",!0),q.dataIndex==="status"?(o(),A(C,{key:1,color:$(p.status)},{default:a(()=>[m(l(O(p.status)),1)]),_:2},1032,["color"])):_("",!0),q.dataIndex==="supplierCount"?(o(),A(C,{key:2,color:"blue"},{default:a(()=>[m(l(p.suppliers.length)+" 家",1)]),_:2},1024)):_("",!0),q.dataIndex==="priceRange"?(o(),i(k,{key:3},[w.value&&p.priceRange?(o(),i("span",Lt," ¥"+l(p.priceRange.min.toFixed(2))+" - ¥"+l(p.priceRange.max.toFixed(2)),1)):w.value?(o(),i("span",Kt,"-")):(o(),i("span",Vt,"需登录查看"))],64)):_("",!0)]),expandedRowRender:a(({record:q})=>[t("div",Rt,[e[21]||(e[21]=t("h4",{style:{"margin-bottom":"12px",color:"#262626"}},"供应商报价详情",-1)),w.value?(o(),A(P,{key:1,size:"small",columns:F,"data-source":q.suppliers,pagination:!1,"row-key":"id",bordered:""},{bodyCell:a(({column:p,record:c})=>[p.dataIndex==="price"?(o(),i(k,{key:0},[c.status==="quoted"?(o(),i("span",Ht," ¥"+l(c.price.toFixed(2)),1)):(o(),i("span",Xt,"-"))],64)):_("",!0),p.dataIndex==="totalPrice"?(o(),i(k,{key:1},[c.status==="quoted"?(o(),i("span",Yt," ¥"+l(c.totalPrice.toFixed(2)),1)):(o(),i("span",Zt,"-"))],64)):_("",!0),p.dataIndex==="minOrderQuantity"?(o(),i(k,{key:2},[c.status==="quoted"?(o(),i("span",te,l(c.minOrderQuantity),1)):(o(),i("span",ee,"-"))],64)):_("",!0),p.dataIndex==="promisedDelivery"?(o(),i(k,{key:3},[c.status==="quoted"?(o(),i("span",ae,l(c.promisedDelivery),1)):(o(),i("span",se,"-"))],64)):_("",!0),p.dataIndex==="validityPeriod"?(o(),i(k,{key:4},[c.status==="quoted"?(o(),i("span",ne,l(c.validityPeriod),1)):(o(),i("span",oe,"-"))],64)):_("",!0),p.dataIndex==="quoteTime"?(o(),i(k,{key:5},[c.status==="quoted"?(o(),i("span",le,l(S(c.quoteTime)),1)):(o(),i("span",ie,"-"))],64)):_("",!0),p.dataIndex==="status"?(o(),A(C,{key:6,color:G(c.status)},{default:a(()=>[m(l(U(c.status)),1)]),_:2},1032,["color"])):_("",!0)]),_:2},1032,["data-source"])):(o(),i("div",Jt,[t("div",Gt,[s(P,{size:"small",columns:F,"data-source":q.suppliers,pagination:!1,"row-key":"id",bordered:"",class:"blurred-table"},{bodyCell:a(({column:p})=>e[16]||(e[16]=[t("span",{class:"blurred-text"},"***",-1)])),_:2},1032,["data-source"])]),t("div",Ut,[t("div",Wt,[e[19]||(e[19]=t("h4",null,"查看供应商报价需要登录",-1)),e[20]||(e[20]=t("p",null,"登录后可以查看详细的供应商报价信息",-1)),s(v,null,{default:a(()=>[s(d,{onClick:Q},{default:a(()=>e[17]||(e[17]=[m("登录")])),_:1}),s(d,{type:"primary",onClick:E},{default:a(()=>e[18]||(e[18]=[m("立即注册")])),_:1})]),_:1})])])]))])]),_:1},8,["data-source","expandedRowKeys"])]),_:1})]),t("div",de,[s(g,null,{default:a(()=>[s(h,{message:"想要参与此询价单？",description:"研选工场诚邀优质供应商参与报价，无需注册即可快速参与询价，获得更多商机！",type:"info","show-icon":"",action:""},{action:a(()=>[s(d,{type:"primary",onClick:X},{default:a(()=>e[22]||(e[22]=[m(" 参与报价 ")])),_:1})]),_:1})]),_:1})])]),t("div",re,[t("div",ue,[s(f,{gutter:48},{default:a(()=>[s(r,{span:8},{default:a(()=>[t("div",pe,[e[24]||(e[24]=t("h3",null,"研选工场",-1)),e[25]||(e[25]=t("p",null,"自动化装备制造行业AI供应链服务平台",-1)),e[26]||(e[26]=t("p",null,"用AI技术重构供应链，为装备制造商研发采购全流程提供高效解决方案",-1)),s(d,{type:"link",onClick:Y,style:{"padding-left":"0"}},{default:a(()=>e[23]||(e[23]=[m(" 访问官网 → ")])),_:1})])]),_:1}),s(r,{span:8},{default:a(()=>e[27]||(e[27]=[t("div",{class:"footer-section"},[t("h4",null,"平台优势"),t("ul",null,[t("li",null,"AI智能匹配"),t("li",null,"成本优化 - 平均降低5%采购成本"),t("li",null,"响应迅速 - 响应时间缩短70%"),t("li",null,"全流程透明")])],-1)])),_:1}),s(r,{span:8},{default:a(()=>e[28]||(e[28]=[t("div",{class:"footer-section"},[t("h4",null,"联系我们"),t("p",null,"400-888-9999"),t("p",null,"<EMAIL>"),t("p",null,"江苏省苏州市工业园区启泰路66号")],-1)])),_:1})]),_:1}),s(D),e[29]||(e[29]=t("div",{class:"footer-bottom"},[t("p",null,"© 2025 研选工场（苏州）网络有限公司 | 苏ICP备2024149956号"),t("p",null,"专业的工业品采购平台 · 智能化供应链解决方案")],-1))])])])}}},ve=ot(ce,[["__scopeId","data-v-48b0d7c1"]]);export{ve as default};
