import{z as ee,H as te,r as C,n as z,y as ae,o as ne,c as y,a as f,d as n,w as o,l as a,J as le,W as w,N as se,a1 as oe,R as c,T as R,U as re,V as v,i as d,j as g,b as V,t as h,Z as ie,F,$ as de,a2 as ue,m as me,g as pe,f as P,h as m,a3 as ye}from"./index-DB3jM_eC.js";import{_ as ce}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ge={class:"payment-list-container"},fe={bordered:!1},ve={style:{display:"flex","justify-content":"space-between","align-items":"center"}},he=["onClick"],be=["onClick"],ke=["onClick"],Se={style:{display:"flex","flex-direction":"column",gap:"12px"}},Ne=ee({__name:"index",setup(Ce){const D=te.RangePicker,U=pe(),x=C(!1),$=C([]),l=z({paymentNumber:"",statementNumber:"",status:void 0,createTimeRange:void 0,paymentDeadlineRange:void 0}),u=z({current:1,pageSize:10,total:0,showSizeChanger:!0,showTotal:s=>`共 ${s} 条`}),b=C(!1),T=C(["paymentNumber","status","totalPayable","totalPaid","totalPending","statementNumber","createTime","paymentCompletionTime","paymentDeadline","paymentMethod","paymentTerms","action"]),_=[{title:"付款单号",dataIndex:"paymentNumber",key:"paymentNumber",width:150},{title:"状态",dataIndex:"status",key:"status",width:100},{title:"应付总额",dataIndex:"totalPayable",key:"totalPayable",align:"right",width:120},{title:"实付总额",dataIndex:"totalPaid",key:"totalPaid",align:"right",width:120},{title:"待付总额",dataIndex:"totalPending",key:"totalPending",align:"right",width:120},{title:"所属对账单",dataIndex:"statementNumber",key:"statementNumber",width:150},{title:"创建时间",dataIndex:"createTime",key:"createTime",width:160},{title:"付款完成时间",dataIndex:"paymentCompletionTime",key:"paymentCompletionTime",width:160},{title:"付款逾期时间",dataIndex:"paymentDeadline",key:"paymentDeadline",width:160},{title:"付款方式",dataIndex:"paymentMethod",key:"paymentMethod",width:120},{title:"付款条件",dataIndex:"paymentTerms",key:"paymentTerms",width:120},{title:"操作",key:"action",width:100,fixed:"right"}],A=ae(()=>_.filter(s=>T.value.includes(s.key))),L=Array.from({length:55},(s,t)=>{const e=new Date(2024,Math.floor(t/10),t%28+1,Math.floor(Math.random()*24),Math.floor(Math.random()*60),Math.floor(Math.random()*60)),r=`PAY${e.getFullYear()}${String(e.getMonth()+1).padStart(2,"0")}${String(e.getDate()).padStart(2,"0")}${String(e.getHours()).padStart(2,"0")}${String(e.getMinutes()).padStart(2,"0")}${String(e.getSeconds()).padStart(2,"0")}`,i=new Date(e.getTime()-Math.random()*30*24*60*60*1e3),M=`INV${i.getFullYear()}${String(i.getMonth()+1).padStart(2,"0")}${String(i.getDate()).padStart(2,"0")}${String(i.getHours()).padStart(2,"0")}${String(i.getMinutes()).padStart(2,"0")}${String(i.getSeconds()).padStart(2,"0")}`,p=Math.random()*5e4+1e4,S=Math.random()*p,K=p-S,N=["pending","paid","overdue","cancelled","partial"][t%5],Q=["现金/电汇","银行承兑"],X=["预付款","账期结算，30天","账期结算，60天","账期结算，90天"],I=Q[t%2];return{id:r,paymentNumber:r,status:N,totalPayable:p,totalPaid:N==="paid"?p:S,totalPending:N==="paid"?0:K,statementNumber:M,createTime:e.toLocaleString("zh-CN"),paymentCompletionTime:N==="paid"?new Date(e.getTime()+Math.random()*7*24*60*60*1e3).toLocaleString("zh-CN"):"",paymentDeadline:I==="现金/电汇"?"":new Date(e.getTime()+30*24*60*60*1e3).toLocaleString("zh-CN"),paymentMethod:I,paymentTerms:X[t%4]}}),k=async(s={})=>{x.value=!0,console.log("Fetching data with params:",{page:u.current,pageSize:u.pageSize,search:l,...s}),await new Promise(i=>setTimeout(i,500));const t=L.filter(i=>{const M=!l.paymentNumber||i.paymentNumber.includes(l.paymentNumber),p=!l.statementNumber||i.statementNumber.includes(l.statementNumber),S=!l.status||i.status===l.status;return M&&p&&S}),e=((u.current??1)-1)*(u.pageSize??10),r=e+(u.pageSize??10);$.value=t.slice(e,r),u.total=t.length,x.value=!1},B=(s,t,e)=>{console.log("Table change:",s,t,e),u.current=s.current,u.pageSize=s.pageSize,k({})},j=()=>{l.paymentNumber="",l.statementNumber="",l.status=void 0,l.createTimeRange=void 0,l.paymentDeadlineRange=void 0,u.current=1,k()},Y=s=>{switch(s){case"pending":return"待支付";case"partial":return"部分支付";case"paid":return"已支付";case"overdue":return"已逾期";case"cancelled":return"已取消";default:return"未知"}},E=s=>{switch(s){case"pending":return"orange";case"partial":return"blue";case"paid":return"green";case"overdue":return"red";case"cancelled":return"default";default:return"default"}},H=s=>s.toLocaleString("zh-CN",{style:"currency",currency:"CNY"}),G=s=>{U.push(`/purchase/payDetail?id=${s.id}`)},J=s=>{P.info(`查看对账单：${s}`)},O=()=>{P.info("导出功能待实现"),console.log("Exporting data with current filters:",l)},W=s=>{P.info(`处理付款：${s.paymentNumber}`)},Z=()=>{b.value=!0},q=()=>{b.value=!1};return ne(()=>{k()}),(s,t)=>(m(),y("div",ge,[f("div",fe,[n(a(le),{layout:"inline",model:l,onFinish:k},{default:o(()=>[n(a(c),{label:"付款单号"},{default:o(()=>[n(a(R),{value:l.paymentNumber,"onUpdate:value":t[0]||(t[0]=e=>l.paymentNumber=e),placeholder:"请输入付款单号","allow-clear":""},null,8,["value"])]),_:1}),n(a(c),{label:"状态"},{default:o(()=>[n(a(re),{value:l.status,"onUpdate:value":t[1]||(t[1]=e=>l.status=e),placeholder:"请选择状态",style:{width:"150px"},"allow-clear":""},{default:o(()=>[n(a(v),{value:"pending"},{default:o(()=>t[7]||(t[7]=[d("待支付")])),_:1}),n(a(v),{value:"partial"},{default:o(()=>t[8]||(t[8]=[d("部分支付")])),_:1}),n(a(v),{value:"paid"},{default:o(()=>t[9]||(t[9]=[d("已支付")])),_:1}),n(a(v),{value:"overdue"},{default:o(()=>t[10]||(t[10]=[d("已逾期")])),_:1}),n(a(v),{value:"cancelled"},{default:o(()=>t[11]||(t[11]=[d("已取消")])),_:1})]),_:1},8,["value"])]),_:1}),n(a(c),{label:"所属对账单"},{default:o(()=>[n(a(R),{value:l.statementNumber,"onUpdate:value":t[2]||(t[2]=e=>l.statementNumber=e),placeholder:"请输入对账单号","allow-clear":""},null,8,["value"])]),_:1}),n(a(c),{label:"创建时间"},{default:o(()=>[n(a(D),{value:l.createTimeRange,"onUpdate:value":t[3]||(t[3]=e=>l.createTimeRange=e)},null,8,["value"])]),_:1}),n(a(c),{label:"付款逾期时间"},{default:o(()=>[n(a(D),{value:l.paymentDeadlineRange,"onUpdate:value":t[4]||(t[4]=e=>l.paymentDeadlineRange=e)},null,8,["value"])]),_:1}),n(a(c),null,{default:o(()=>[n(a(w),{type:"primary","html-type":"submit"},{default:o(()=>t[12]||(t[12]=[d("查询")])),_:1}),n(a(w),{style:{"margin-left":"8px"},onClick:j},{default:o(()=>t[13]||(t[13]=[d("重置")])),_:1})]),_:1})]),_:1},8,["model"]),f("div",ve,[f("div",null,[n(a(w),{onClick:O},{default:o(()=>t[14]||(t[14]=[d("导出")])),_:1})]),f("div",null,[n(a(w),{onClick:Z},{default:o(()=>t[15]||(t[15]=[d("列配置")])),_:1})])]),n(a(se),{columns:A.value,"data-source":$.value,loading:x.value,pagination:u,"row-key":"id",onChange:B,style:{"margin-top":"16px"}},{bodyCell:o(({column:e,record:r})=>[e.key==="paymentNumber"?(m(),y("a",{key:0,href:"javascript:void(0)",onClick:i=>G(r)},h(r.paymentNumber),9,he)):g("",!0),e.key==="status"?(m(),V(a(ie),{key:1,color:E(r.status)},{default:o(()=>[d(h(Y(r.status)),1)]),_:2},1032,["color"])):g("",!0),e.key==="statementNumber"?(m(),y("a",{key:2,href:"javascript:void(0)",onClick:i=>J(r.statementNumber)},h(r.statementNumber),9,be)):g("",!0),e.key==="totalPayable"||e.key==="totalPaid"||e.key==="totalPending"?(m(),y(F,{key:3},[d(h(H(r[e.key])),1)],64)):g("",!0),e.key==="action"?(m(),V(a(de),{key:4},{default:o(()=>[r.status==="pending"||r.status==="partial"?(m(),y("a",{key:0,onClick:i=>W(r)},"付款",8,ke)):g("",!0)]),_:2},1024)):g("",!0)]),_:1},8,["columns","data-source","loading","pagination"])]),n(a(oe),{open:b.value,"onUpdate:open":t[6]||(t[6]=e=>b.value=e),title:"列配置",placement:"right",width:"400",onClose:q},{default:o(()=>[n(a(ue),{value:T.value,"onUpdate:value":t[5]||(t[5]=e=>T.value=e),style:{width:"100%"}},{default:o(()=>[f("div",Se,[(m(),y(F,null,me(_,e=>n(a(ye),{key:e.key,value:e.key,disabled:e.key==="paymentNumber"||e.key==="action"},{default:o(()=>[d(h(e.title),1)]),_:2},1032,["value","disabled"])),64))])]),_:1},8,["value"])]),_:1},8,["open"])]))}}),Te=ce(Ne,[["__scopeId","data-v-5fad0765"]]);export{Te as default};
