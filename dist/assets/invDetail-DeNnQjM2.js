import{r as B,u as Ie,n as Ae,y as k,a0 as I,o as H,f as b,c as _,d as o,a as f,w as t,e as s,b as y,j as c,i as u,F as g,m as Se,t as i,g as Oe,h as d}from"./index-DB3jM_eC.js";import{_ as Me}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ze={class:"invoice-detail-container"},Be={class:"invoice-progress-section"},Le={key:0},$e={class:"amount-text"},Fe={class:"search-section"},Ue={class:"summary-total"},Ye={class:"summary-total"},qe={class:"log-user"},Re={class:"log-time"},Ve={key:0,class:"log-detail"},Ee={__name:"invDetail",setup(je){const U=Ie(),Q=Oe(),Y=B(U.params.id||U.query.id),L=B(!1),r=Ae({materialName:"",model:"",receiptNo:"",poNo:"",receiptDateFrom:null,receiptDateTo:null}),l=B({id:"",invoiceNo:"INV-2023-0001",reconciliationPeriod:"2023-10-01 ~ 2023-10-31",totalAmount:125e3,status:"pending",billingStatus:"invoicing",paymentTerms:"account_settlement",createTime:"2023-10-25 10:00:00",confirmTime:"2023-10-26 14:30:00",settlementTime:null,supplierName:"ABC供应商",creatorName:"张三",confirmer:"李四",remarks:"本期对账单包含10月份全部收货物料，请核对无误后确认。",materials:[{id:"1",key:"1",materialName:"伺服电机",model:"SM2000",quantity:5,unitPrice:8e3,totalPrice:4e4,receiptDate:"2023-10-15",receiptNo:"****************",poNo:"****************"},{id:"2",key:"2",materialName:"工业控制器",model:"IC5000",quantity:3,unitPrice:15e3,totalPrice:45e3,receiptDate:"2023-10-18",receiptNo:"****************",poNo:"****************"},{id:"3",key:"3",materialName:"传感器",model:"SE100",quantity:20,unitPrice:500,totalPrice:1e4,receiptDate:"2023-10-20",receiptNo:"****************",poNo:"****************"},{id:"4",key:"4",materialName:"电机驱动器",model:"DR200",quantity:8,unitPrice:3750,totalPrice:3e4,receiptDate:"2023-10-22",receiptNo:"****************",poNo:"****************"}],attachments:[{id:"att-1",name:"对账单明细.xlsx",type:"xlsx",size:"2.5MB",uploadTime:"2023-10-25 11:00:00",uploadUser:"张三"}],operationLogs:[{id:"log-1",userName:"张三",time:"2023-10-25 10:00:00",action:"生成对账单"},{id:"log-2",userName:"李四",time:"2023-10-26 14:30:00",action:"确认对账单",detail:"已核对物料清单，确认无误"}],relatedPurchaseOrders:[{id:"po-1",orderNo:"SO20231001100000",orderDate:"2023-10-01",endDate:"2023-10-15",totalAmount:4e4,reconciliationAmount:35e3,status:"已完成"},{id:"po-2",orderNo:"SO20231005143000",orderDate:"2023-10-05",endDate:"2023-10-18",totalAmount:45e3,reconciliationAmount:45e3,status:"已完成"},{id:"po-3",orderNo:"SO20231008090000",orderDate:"2023-10-08",endDate:"2023-10-20",totalAmount:1e4,reconciliationAmount:8e3,status:"执行中"},{id:"po-4",orderNo:"SO20231012160000",orderDate:"2023-10-12",endDate:"2023-10-22",totalAmount:3e4,reconciliationAmount:3e4,status:"已完成"}],relatedPaymentOrder:{id:"pay-1",paymentNo:"PAY-2023-001",paymentAmount:125e3,paymentDate:"2023-10-27",status:"已付款"},stepTimes:{generation:"2023-10-25 10:00:00",confirmation:"2023-10-26 14:30:00",settlement:null}}),J=k(()=>{var e,a,p;return[{title:"待对账",time:(e=l.value.stepTimes)==null?void 0:e.generation},{title:"已锁定",time:(a=l.value.stepTimes)==null?void 0:a.confirmation},{title:"已结算",time:(p=l.value.stepTimes)==null?void 0:p.settlement}]}),G=k(()=>({pending:0,confirmed:1,settled:2})[l.value.status]??0),K=k(()=>l.value.paymentTerms==="account_settlement"?l.value.reconciliationPeriod:""),h=k(()=>{let e=l.value.materials;if(r.materialName&&(e=e.filter(a=>a.materialName.toLowerCase().includes(r.materialName.toLowerCase()))),r.model&&(e=e.filter(a=>a.model.toLowerCase().includes(r.model.toLowerCase()))),r.receiptNo&&(e=e.filter(a=>a.receiptNo.toLowerCase().includes(r.receiptNo.toLowerCase()))),r.poNo&&(e=e.filter(a=>a.poNo.toLowerCase().includes(r.poNo.toLowerCase()))),r.receiptDateFrom&&r.receiptDateTo){const a=I(r.receiptDateFrom),p=I(r.receiptDateTo);e=e.filter(S=>{const O=I(S.receiptDate);return O.isAfter(a.subtract(1,"day"))&&O.isBefore(p.add(1,"day"))})}return e}),x=B({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,a)=>`第 ${a[0]}-${a[1]} 条，共 ${e} 条`});H(()=>{x.value.total=h.value.length});const W=[{title:"物料名称",dataIndex:"materialName",key:"materialName",fixed:"left"},{title:"型号",dataIndex:"model",key:"model"},{title:"数量",dataIndex:"quantity",key:"quantity",width:80,align:"right"},{title:"单价",dataIndex:"unitPrice",key:"unitPrice",width:120,align:"right"},{title:"总价",dataIndex:"totalPrice",key:"totalPrice",width:120,align:"right"},{title:"收货日期",dataIndex:"receiptDate",key:"receiptDate",width:120},{title:"收货单号",dataIndex:"receiptNo",key:"receiptNo"},{title:"采购单号",dataIndex:"poNo",key:"poNo"}],X=[{title:"采购单号",dataIndex:"orderNo",key:"orderNo"},{title:"状态",dataIndex:"status",key:"status"},{title:"下单时间",dataIndex:"orderDate",key:"orderDate"},{title:"结束时间",dataIndex:"endDate",key:"endDate"},{title:"总金额",dataIndex:"totalAmount",key:"totalAmount",align:"right"},{title:"本次对账金额",dataIndex:"reconciliationAmount",key:"reconciliationAmount",align:"right"}],Z=[{title:"付款单号",dataIndex:"paymentNo",key:"paymentNo"},{title:"付款金额",dataIndex:"paymentAmount",key:"paymentAmount",align:"right"},{title:"付款日期",dataIndex:"paymentDate",key:"paymentDate"},{title:"状态",dataIndex:"status",key:"status"},{title:"操作",key:"action",width:120}],ee=[{title:"文件名",dataIndex:"name",key:"name"},{title:"类型",dataIndex:"type",key:"type",width:80},{title:"大小",dataIndex:"size",key:"size",width:100},{title:"上传时间",dataIndex:"uploadTime",key:"uploadTime",width:150},{title:"上传人",dataIndex:"uploadUser",key:"uploadUser",width:100},{title:"操作",key:"action",width:150,fixed:"right"}],q=k(()=>l.value.status==="pending"),te=k(()=>l.value.status==="pending"),ae=k(()=>l.value.status==="confirmed");k(()=>!0);const oe=()=>{Q.go(-1)},R=e=>({pending:"待对账",confirmed:"已锁定",settled:"已结算"})[e]||e,ne=e=>({pending:"orange",confirmed:"blue",settled:"green"})[e]||"default",le=e=>({not_invoiced:"未开票",invoicing:"开票中",invoiced:"已开票"})[e]||e,ie=e=>({not_invoiced:"red",invoicing:"orange",invoiced:"green"})[e]||"default",re=e=>({prepayment:"预付款",account_settlement:"账期结算"})[e]||e,se=e=>({prepayment:"volcano",account_settlement:"geekblue"})[e]||"default",V=e=>e?I(e).format("YYYY-MM-DD"):"",A=e=>e?I(e).format("YYYY-MM-DD HH:mm:ss"):"",N=e=>!e&&e!==0?"0.00":Number(e).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}),ue=()=>h.value.reduce((e,a)=>e+a.quantity,0),de=()=>h.value.reduce((e,a)=>e+a.totalPrice,0),D=()=>{x.value.current=1,x.value.total=h.value.length},ce=()=>{Object.keys(r).forEach(e=>{r[e]=e.includes("Date")?null:""}),D()},me=e=>{x.value={...x.value,...e}},pe=e=>{b.info(`查看采购单详情: ${e}`)},ye=e=>{b.info(`查看采购单详情: ${e}`)},_e=e=>{b.info(`查看收货单详情: ${e}`)},fe=e=>{b.info(`查看付款单详情: ${e}`)},ve=async()=>{L.value=!0;try{await new Promise(e=>setTimeout(e,500)),console.log("Fetching invoice details for id:",Y.value),L.value=!1}catch{b.error("获取对账单详情失败"),L.value=!1}};return H(()=>{Y.value?ve():b.warn("未指定对账单ID")}),(e,a)=>{const p=s("a-button"),S=s("a-space"),O=s("a-page-header"),ke=s("a-step"),ge=s("a-steps"),w=s("a-card"),v=s("a-descriptions-item"),$=s("a-tag"),Ne=s("a-descriptions"),M=s("a-input"),C=s("a-col"),De=s("a-range-picker"),xe=s("a-row"),P=s("a-table-summary-cell"),Ce=s("a-table-summary-row"),z=s("a-table"),E=s("a-empty"),T=s("a-tab-pane"),j=s("a-tabs"),be=s("a-popconfirm"),he=s("a-textarea"),we=s("a-list-item-meta"),Pe=s("a-list-item"),Te=s("a-list");return d(),_("div",ze,[o(O,{title:"对账单详情 - "+l.value.invoiceNo,"sub-title":"状态："+R(l.value.status),onBack:oe},{extra:t(()=>[o(S,null,{default:t(()=>[te.value?(d(),y(p,{key:0,type:"primary"},{default:t(()=>a[6]||(a[6]=[u("确认对账单")])),_:1})):c("",!0),ae.value?(d(),y(p,{key:1,type:"primary"},{default:t(()=>a[7]||(a[7]=[u("全部结算")])),_:1})):c("",!0),q.value?(d(),y(p,{key:2},{default:t(()=>a[8]||(a[8]=[u("编辑")])),_:1})):c("",!0)]),_:1})]),_:1},8,["title","sub-title"]),f("div",Be,[o(w,{title:"对账流程"},{default:t(()=>[o(ge,{current:G.value,size:"small"},{default:t(()=>[(d(!0),_(g,null,Se(J.value,(n,m)=>(d(),y(ke,{key:m,title:n.title},{description:t(()=>[n.time?(d(),_("div",Le,i(A(n.time)),1)):c("",!0)]),_:2},1032,["title"]))),128))]),_:1},8,["current"])]),_:1})]),o(w,{title:"基本信息",class:"detail-card"},{default:t(()=>[o(Ne,{bordered:"",column:3,size:"middle"},{default:t(()=>[o(v,{label:"对账单号"},{default:t(()=>[u(i(l.value.invoiceNo),1)]),_:1}),o(v,{label:"付款条件"},{default:t(()=>[o($,{color:se(l.value.paymentTerms)},{default:t(()=>[u(i(re(l.value.paymentTerms)),1)]),_:1},8,["color"])]),_:1}),l.value.paymentTerms==="account_settlement"?(d(),y(v,{key:0,label:"对账周期"},{default:t(()=>[u(i(K.value),1)]),_:1})):c("",!0),o(v,{label:"总金额"},{default:t(()=>[f("span",$e,"¥"+i(N(l.value.totalAmount)),1)]),_:1}),o(v,{label:"状态"},{default:t(()=>[o($,{color:ne(l.value.status)},{default:t(()=>[u(i(R(l.value.status)),1)]),_:1},8,["color"])]),_:1}),o(v,{label:"开票状态"},{default:t(()=>[o($,{color:ie(l.value.billingStatus)},{default:t(()=>[u(i(le(l.value.billingStatus)),1)]),_:1},8,["color"])]),_:1}),o(v,{label:"创建时间"},{default:t(()=>[u(i(A(l.value.createTime)),1)]),_:1}),l.value.confirmTime?(d(),y(v,{key:1,label:"对账时间"},{default:t(()=>[u(i(A(l.value.confirmTime)),1)]),_:1})):c("",!0)]),_:1})]),_:1}),o(w,{title:"物料信息",class:"detail-card"},{default:t(()=>[f("div",Fe,[o(xe,{gutter:16,style:{"margin-bottom":"16px"}},{default:t(()=>[o(C,{span:4},{default:t(()=>[o(M,{value:r.materialName,"onUpdate:value":a[0]||(a[0]=n=>r.materialName=n),placeholder:"物料名称",onChange:D,allowClear:""},null,8,["value"])]),_:1}),o(C,{span:4},{default:t(()=>[o(M,{value:r.model,"onUpdate:value":a[1]||(a[1]=n=>r.model=n),placeholder:"型号",onChange:D,allowClear:""},null,8,["value"])]),_:1}),o(C,{span:4},{default:t(()=>[o(M,{value:r.receiptNo,"onUpdate:value":a[2]||(a[2]=n=>r.receiptNo=n),placeholder:"收货单号",onChange:D,allowClear:""},null,8,["value"])]),_:1}),o(C,{span:4},{default:t(()=>[o(M,{value:r.poNo,"onUpdate:value":a[3]||(a[3]=n=>r.poNo=n),placeholder:"采购单号",onChange:D,allowClear:""},null,8,["value"])]),_:1}),o(C,{span:4},{default:t(()=>[o(De,{value:r.receiptDateFrom,"onUpdate:value":a[4]||(a[4]=n=>r.receiptDateFrom=n),placeholder:["收货日期起","收货日期至"],onChange:D,style:{width:"100%"}},null,8,["value"])]),_:1}),o(C,{span:4},{default:t(()=>[o(p,{type:"primary",onClick:D},{default:t(()=>a[9]||(a[9]=[u("查询")])),_:1}),o(p,{onClick:ce,style:{"margin-left":"8px"}},{default:t(()=>a[10]||(a[10]=[u("重置")])),_:1})]),_:1})]),_:1})]),o(z,{columns:W,"data-source":h.value,pagination:x.value,size:"middle",onChange:me,style:{width:"100%"}},{bodyCell:t(({column:n,record:m})=>[n.key==="unitPrice"?(d(),_(g,{key:0},[u(" ¥"+i(N(m.unitPrice)),1)],64)):c("",!0),n.key==="totalPrice"?(d(),_(g,{key:1},[u(" ¥"+i(N(m.totalPrice)),1)],64)):c("",!0),n.key==="receiptDate"?(d(),_(g,{key:2},[u(i(V(m.receiptDate)),1)],64)):c("",!0),n.key==="receiptNo"?(d(),y(p,{key:3,type:"link",onClick:F=>_e(m.receiptNo),style:{padding:"0"}},{default:t(()=>[u(i(m.receiptNo),1)]),_:2},1032,["onClick"])):c("",!0),n.key==="poNo"?(d(),y(p,{key:4,type:"link",onClick:F=>ye(m.poNo),style:{padding:"0"}},{default:t(()=>[u(i(m.poNo),1)]),_:2},1032,["onClick"])):c("",!0)]),summary:t(()=>[o(Ce,null,{default:t(()=>[o(P,{index:0,"col-span":5},{default:t(()=>a[11]||(a[11]=[f("strong",null,"合计",-1)])),_:1}),o(P,{index:5},{default:t(()=>[f("strong",Ue,i(ue()),1)]),_:1}),o(P,{index:6}),o(P,{index:7},{default:t(()=>[f("strong",Ye,"¥"+i(N(de())),1)]),_:1}),o(P,{index:8,"col-span":3})]),_:1})]),_:1},8,["data-source","pagination"])]),_:1}),o(w,{title:"相关单据",class:"detail-card"},{default:t(()=>[o(j,{"default-active-key":"po"},{default:t(()=>[o(T,{key:"po",tab:"采购单"},{default:t(()=>[o(z,{columns:X,"data-source":l.value.relatedPurchaseOrders,pagination:!1,size:"small"},{bodyCell:t(({column:n,record:m})=>[n.key==="orderNo"?(d(),y(p,{key:0,type:"link",onClick:F=>pe(m.id),style:{padding:"0"}},{default:t(()=>[u(i(m.orderNo),1)]),_:2},1032,["onClick"])):c("",!0),n.key==="totalAmount"?(d(),_(g,{key:1},[u(" ¥"+i(N(m.totalAmount)),1)],64)):c("",!0),n.key==="reconciliationAmount"?(d(),_(g,{key:2},[u(" ¥"+i(N(m.reconciliationAmount)),1)],64)):c("",!0)]),_:1},8,["data-source"]),!l.value.relatedPurchaseOrders||l.value.relatedPurchaseOrders.length===0?(d(),y(E,{key:0,description:"暂无关联采购单"})):c("",!0)]),_:1}),o(T,{key:"payment",tab:"付款单"},{default:t(()=>[o(z,{columns:Z,"data-source":l.value.relatedPaymentOrder?[l.value.relatedPaymentOrder]:[],pagination:!1,size:"small"},{bodyCell:t(({column:n,record:m})=>[n.key==="paymentAmount"?(d(),_(g,{key:0},[u(" ¥"+i(N(m.paymentAmount)),1)],64)):c("",!0),n.key==="paymentDate"?(d(),_(g,{key:1},[u(i(V(m.paymentDate)),1)],64)):c("",!0),n.key==="action"?(d(),y(p,{key:2,type:"link",onClick:F=>fe(m.id)},{default:t(()=>a[12]||(a[12]=[u("查看详情")])),_:2},1032,["onClick"])):c("",!0)]),_:1},8,["data-source"]),l.value.relatedPaymentOrder?c("",!0):(d(),y(E,{key:0,description:"暂无关联付款单"}))]),_:1})]),_:1})]),_:1}),o(w,{title:"附件与备注",class:"detail-card"},{default:t(()=>[o(j,{"default-active-key":"1"},{default:t(()=>[o(T,{key:"1",tab:"附件资料"},{default:t(()=>[o(z,{columns:ee,"data-source":l.value.attachments,pagination:!1,size:"small",style:{"margin-top":"16px"}},{bodyCell:t(({column:n})=>[n.key==="action"?(d(),y(S,{key:0},{default:t(()=>[a[14]||(a[14]=f("a",null,"预览",-1)),a[15]||(a[15]=f("a",null,"下载",-1)),o(be,{title:"确定删除吗?",onConfirm:()=>{}},{default:t(()=>a[13]||(a[13]=[f("a",{style:{color:"red"}},"删除",-1)])),_:1})]),_:1})):c("",!0)]),_:1},8,["data-source"])]),_:1}),o(T,{key:"2",tab:"备注信息"},{default:t(()=>[o(he,{value:l.value.remarks,"onUpdate:value":a[5]||(a[5]=n=>l.value.remarks=n),placeholder:"请输入备注信息",rows:4,disabled:!q.value},null,8,["value","disabled"])]),_:1}),o(T,{key:"3",tab:"操作历史"},{default:t(()=>[o(Te,{"item-layout":"horizontal","data-source":l.value.operationLogs,size:"small"},{renderItem:t(({item:n})=>[o(Pe,null,{default:t(()=>[o(we,null,{title:t(()=>[f("span",qe,i(n.userName),1),u(" "+i(n.action),1)]),description:t(()=>[f("div",Re,i(A(n.time)),1),n.detail?(d(),_("div",Ve,i(n.detail),1)):c("",!0)]),_:2},1024)]),_:2},1024)]),_:1},8,["data-source"])]),_:1})]),_:1})]),_:1})])}}},Je=Me(Ee,[["__scopeId","data-v-e91186c8"]]);export{Je as default};
