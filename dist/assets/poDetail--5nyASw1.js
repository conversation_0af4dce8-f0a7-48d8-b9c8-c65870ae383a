import{r as I,u as we,y as w,o as Pe,c as x,d as n,a as i,w as a,e as d,f as h,g as Ce,h as u,b as y,j as g,i as r,F as D,m as B,t as l,x as v,l as Qe}from"./index-DB3jM_eC.js";import{_ as Ae}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{I as qe}from"./InboxOutlined-C00g7rQT.js";const Te={class:"po-detail-container"},Se={class:"po-progress-section"},Me={class:"info-item"},ze={class:"value"},Be={class:"info-item"},Fe={class:"value"},$e={class:"info-item"},Ue={class:"value"},Le={class:"info-item"},Ve={class:"value"},Xe={class:"info-item"},Ee={class:"value"},je={class:"info-item"},He={class:"value"},Ke={class:"info-item"},Oe={class:"value"},Ge={class:"info-item"},We={class:"value important"},Ye={class:"info-item"},Ze={class:"value important"},Je=["onClick"],et=["onClick"],tt={class:"summary-section"},at={class:"summary-item"},nt={class:"value"},st={class:"summary-item"},it={class:"value important"},lt={class:"summary-item"},ot={class:"value"},dt={class:"summary-item total"},ut={class:"value important"},rt={key:0,style:{"margin-bottom":"16px"}},ct={class:"ant-upload-drag-icon"},mt={class:"comment-header"},pt={class:"comment-user"},vt={class:"comment-time"},yt={class:"comment-content"},_t={class:"log-time"},ft={class:"log-content"},gt={class:"log-user"},kt={key:0,class:"log-detail"},xt={class:"bottom-actions"},ht={__name:"poDetail",setup(bt){const F=we(),O=Ce();I(F.params.id||F.query.id);const M=I(!1),P=I(""),C=I(!1),Q=I([]),$=I([{id:"1",name:"王五",phone:"***********",region:"北京市海淀区",address:"科技园区888号智能制造中心3号楼",isDefault:!0},{id:"2",name:"李四",phone:"13800138000",region:"上海市浦东新区",address:"张江高科技园区999号创新大厦5楼",isDefault:!1},{id:"3",name:"张三",phone:"13700137000",region:"深圳市南山区",address:"科技园南区软件园A栋2楼",isDefault:!1}]),G=[{title:"联系人",dataIndex:"name",key:"name"},{title:"联系电话",dataIndex:"phone",key:"phone"},{title:"地区",dataIndex:"region",key:"region"},{title:"详细地址",dataIndex:"address",key:"address"},{title:"默认",dataIndex:"isDefault",key:"isDefault",customRender:({text:e})=>e?"✓":""}],s=I({id:"",poNo:"",status:"draft",invoicingStatus:"invoicing",paymentStatus:"partially_paid",createTime:"",expectedDeliveryDate:"2023-11-15",purchaser:"张三",purchaseGroup:"机械装备组",contactPerson:"李四",contactPhone:"13800138000",paymentMethod:"现金（电汇）",paymentTerms:"月结，账期15日",subtotalAmount:185e3,tax:16650,shippingFee:2500,totalAmount:204150,residue:5e5,receiverName:"王五",receiverPhone:"***********",receiverRegion:"北京市海淀区",receiverAddress:"科技园区888号智能制造中心3号楼",receivingTimeRequirement:"工作日9:00-17:00，提前2小时电话通知",remark:"物品易碎，请妥善包装",deliveryMethod:"快递",deliveryService:"包含安装调试服务",packagingRequirements:"防潮、防震包装",invoiceTitle:"XX科技有限公司",taxId:"91110108MA01R1XNXX",invoiceRegisteredAddress:"北京市海淀区中关村软件园XX号",invoicePhone:"010-********",invoiceBankName:"中国工商银行北京海淀支行",invoiceBankAccount:"0200049619200088888",items:[{id:"1",key:"1",name:"伺服电机",model:"SM2000",brand:"ABB",category:"传动系统",rfqNo:"RFQ-2023-0001",quantity:5,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,expectedArrivalDate:"2023-11-10",unitPrice:12e3,totalPrice:6e4,status:"draft"},{id:"2",key:"2",name:"工业控制器",model:"IC5000",brand:"Siemens",category:"控制系统",rfqNo:"RFQ-2023-0001",quantity:2,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,expectedArrivalDate:"2023-11-12",unitPrice:35e3,totalPrice:7e4,status:"draft"},{id:"3",key:"3",name:"传感器",model:"S3000",brand:"Honeywell",category:"传感设备",rfqNo:"RFQ-2023-0001",quantity:20,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,expectedArrivalDate:"2023-10-30",unitPrice:2500,totalPrice:5e4,status:"draft"},{id:"4",key:"4",name:"液压阀门",model:"HV200",brand:"Parker",category:"液压系统",rfqNo:"RFQ-2023-0001",quantity:5,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,expectedArrivalDate:"2023-11-05",unitPrice:1e3,totalPrice:5e3,status:"draft"}],logisticsRecords:[],attachments:[{id:"1",name:"合同文档.pdf",type:"pdf",size:"2.5MB",uploadTime:"2023-10-15 10:30:00",uploadUser:"张三"},{id:"2",name:"技术规格说明.docx",type:"docx",size:"1.8MB",uploadTime:"2023-10-15 11:20:00",uploadUser:"张三"}],comments:[{id:"1",userName:"张三",time:"2023-10-15 10:45:00",content:"已与供应商确认交货时间"},{id:"2",userName:"李四",time:"2023-10-16 14:30:00",content:"请注意控制器的型号需要与之前的设备兼容"}],operationLogs:[{id:"1",userName:"张三",time:"2023-10-15 09:30:00",action:"创建了采购单"},{id:"2",userName:"王经理",time:"2023-10-15 14:20:00",action:"审批通过了采购单"},{id:"3",userName:"张三",time:"2023-10-16 09:15:00",action:"发送采购单给供应商"},{id:"4",userName:"系统",time:"2023-10-18 14:30:00",action:"更新了物流状态",detail:"供应商已发货"}]}),W=[{title:"对账单号",dataIndex:"statementNo",key:"statementNo"},{title:"账期",dataIndex:"period",key:"period"},{title:"应付金额(元)",dataIndex:"amountDue",key:"amountDue",customRender:({text:e})=>_(e)},{title:"已付金额(元)",dataIndex:"amountPaid",key:"amountPaid",customRender:({text:e})=>_(e)},{title:"状态",dataIndex:"status",key:"status",customRender:({text:e})=>{var c,m;const t={pending:{text:"待核对",color:"orange"},confirmed:{text:"已核对",color:"blue"},paid:{text:"已付款",color:"green"}};return v("a-tag",{color:(c=t[e])==null?void 0:c.color},((m=t[e])==null?void 0:m.text)||e)}},{title:"出单日期",dataIndex:"issueDate",key:"issueDate"},{title:"操作",key:"action",customRender:()=>v("a",{},"查看")}],q=I({statements:[{id:"stmt-1",key:"stmt-1",statementNo:"STMT-2023-001",period:"2023-10",amountDue:5e4,amountPaid:2e4,status:"pending",issueDate:"2023-11-01"}],payments:[{id:"1",key:"1",docNo:"PAY-2023-0001",type:"预付款",amount:59745,status:"paid",createTime:"2023-10-16 10:30:00",paymentTime:"2023-10-17 14:20:00"}],deliveries:[{id:"1",key:"1",docNo:"DEL-2023-0001",items:["传感器 x10","液压阀门 x5"],status:"delivered",shippingDate:"2023-10-18 09:30:00",logistics:[{provider:"顺丰速运",trackingNo:"SF********90"},{provider:"中通快递",trackingNo:"ZT0987654321"}]},{id:"2",key:"2",docNo:"DEL-2023-0002",items:["伺服电机 x2","工业控制器 x1"],status:"preparing",shippingDate:"2023-10-22 10:00:00",logistics:[]}],returns:[],invoices:[{id:"1",key:"1",invoiceNo:"INV-2023-0001",type:"增值税专用发票",amount:204150,status:"issued",issueDate:"2023-10-25 11:00:00",downloadUrl:"/api/download/invoice/INV-2023-0001"}]}),Y=I([{title:"创建草稿",time:"2023-10-15 09:30:00"},{title:"提交订单",time:null},{title:"订单确认",time:null},{title:"完成订单",time:null}]),Z=w(()=>({draft:0,pending:1,in_progress:2,completed:3,archived:4})[s.value.status]||0),J=[{title:"物料名称",dataIndex:"name",key:"name",width:180},{title:"型号",dataIndex:"model",key:"model",width:150},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"分类",dataIndex:"category",key:"category",width:120},{title:"来源询价单",dataIndex:"rfqNo",key:"rfqNo",width:150},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"已发货数量",dataIndex:"shippedQuantity",key:"shippedQuantity",width:110},{title:"已收货数量",dataIndex:"receivedQuantity",key:"receivedQuantity",width:110},{title:"已取消数量",dataIndex:"cancelledQuantity",key:"cancelledQuantity",width:110},{title:"物流状态",dataIndex:"logisticsStatus",key:"logisticsStatus",width:100},{title:"财务状态",dataIndex:"financialStatus",key:"financialStatus",width:100},{title:"单价（¥）",dataIndex:"unitPrice",key:"unitPrice",width:100,customRender:({text:e})=>_(e)},{title:"总价（¥）",dataIndex:"totalPrice",key:"totalPrice",width:120,customRender:({text:e})=>_(e)},{title:"预计到货日期",dataIndex:"expectedArrivalTime",key:"expectedArrivalTime",width:150},{title:"操作（⚠️看需求）",dataIndex:"actions",key:"actions",width:180,fixed:"right"}],ee=[{title:"单据编号",dataIndex:"docNo",key:"docNo"},{title:"付款条件",dataIndex:"type",key:"type"},{title:"金额(元)",dataIndex:"amount",key:"amount",customRender:({text:e})=>_(e)},{title:"状态",dataIndex:"status",key:"status",customRender:({text:e})=>{var c,m;const t={pending:{text:"待支付",color:"orange"},paid:{text:"已支付",color:"green"},cancelled:{text:"已取消",color:"red"}};return v("a-tag",{color:(c=t[e])==null?void 0:c.color},((m=t[e])==null?void 0:m.text)||e)}},{title:"创建时间",dataIndex:"createTime",key:"createTime"},{title:"支付时间",dataIndex:"paymentTime",key:"paymentTime"},{title:"操作",key:"action",customRender:()=>v("a",{},"查看")}],te=[{title:"单据编号",dataIndex:"docNo",key:"docNo"},{title:"物品",dataIndex:"items",key:"items",customRender:({record:e})=>e.items&&Array.isArray(e.items)&&e.items.length>0?v("div",{},e.items.map(t=>v("div",{style:"margin-bottom: 4px;"},t))):"无物品信息"},{title:"状态",dataIndex:"status",key:"status",customRender:({text:e})=>{var c,m;const t={preparing:{text:"准备中",color:"blue"},shipped:{text:"已发货",color:"purple"},in_transit:{text:"运输中",color:"orange"},delivered:{text:"已送达",color:"green"}};return v("a-tag",{color:(c=t[e])==null?void 0:c.color},((m=t[e])==null?void 0:m.text)||e)}},{title:"发货日期",dataIndex:"shippingDate",key:"shippingDate"},{title:"物流单号",key:"logistics",customRender:({record:e})=>e.logistics&&e.logistics.length>0?v("div",{},e.logistics.map(t=>v("div",`${t.provider||""}: ${t.trackingNo}`))):"暂无物流信息"},{title:"操作",key:"action",customRender:()=>v("a",{},"查看")}],ae=[{title:"单据编号",dataIndex:"docNo",key:"docNo"},{title:"物品",dataIndex:"items",key:"items"},{title:"退货原因",dataIndex:"reason",key:"reason"},{title:"退货数量",dataIndex:"quantity",key:"quantity"},{title:"退款金额",dataIndex:"amount",key:"amount",customRender:({text:e})=>_(e)},{title:"状态",dataIndex:"status",key:"status"},{title:"申请日期",dataIndex:"applyDate",key:"applyDate"},{title:"处理日期",dataIndex:"processDate",key:"processDate"},{title:"操作",key:"action",customRender:()=>v("a",{},"查看")}],ne=[{title:"文件名",dataIndex:"name",key:"name"},{title:"类型",dataIndex:"type",key:"type"},{title:"大小",dataIndex:"size",key:"size"},{title:"上传时间",dataIndex:"uploadTime",key:"uploadTime"},{title:"上传人",dataIndex:"uploadUser",key:"uploadUser"},{title:"操作",key:"action",customRender:()=>v("a-space",{},[v("a",{},"预览"),v("a-divider",{type:"vertical"}),v("a",{},"下载")])}];w(()=>["draft","pending"].includes(s.value.status)),w(()=>s.value.status==="draft");const U=w(()=>!["cancelled","completed","archived"].includes(s.value.status)),se=w(()=>!0),ie=w(()=>!0),le=()=>{O.go(-1)},L=e=>({draft:"草稿",pending:"待确认",in_progress:"执行中",completed:"已完成",cancelled:"已取消",cancelling:"取消中",archived:"已归档",exception:"异常待处理"})[e]||e,oe=e=>({draft:"blue",pending:"orange",in_progress:"purple",completed:"green",cancelled:"red",cancelling:"pink",archived:"gray",exception:"volcano"})[e]||"default",T=e=>e||"",_=e=>e==null?"0.00":e.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}),de=()=>s.value.items.reduce((e,t)=>e+t.quantity,0),ue=e=>{e.file.status==="done"?h.success(`${e.file.name} 上传成功`):e.file.status==="error"&&h.error(`${e.file.name} 上传失败`)},re=()=>{if(!P.value.trim()){h.warning("请输入评论内容");return}s.value.comments.push({id:Date.now().toString(),userName:"当前用户",time:new Date().toLocaleString("zh-CN"),content:P.value}),h.success("评论已添加"),P.value=""},ce=(e,t)=>{e.totalPrice=e.quantity*e.unitPrice;const c=s.value.items.reduce((m,A)=>m+A.totalPrice,0);s.value.subtotalAmount=c,s.value.totalAmount=c+s.value.shippingFee,h.success(`已更新${e.name}的数量为${e.quantity}`)},me=(e,t)=>{s.value.items.splice(t,1);const c=s.value.items.reduce((m,A)=>m+A.totalPrice,0);s.value.subtotalAmount=c,s.value.totalAmount=c+s.value.shippingFee,h.success(`已删除物料: ${e.name}`)},pe=e=>{s.value.status!=="draft"&&(e.status="cancelled",e.cancelledQuantity=e.quantity,h.success(`已取消物料: ${e.name}`))},ve=()=>{C.value=!0},ye=e=>{Q.value=e},_e=()=>{if(Q.value.length===0){h.warning("请选择收货信息");return}const e=$.value.find(t=>t.id===Q.value[0]);e&&(s.value.receiverName=e.name,s.value.receiverPhone=e.phone,s.value.receiverRegion=e.region,s.value.receiverAddress=e.address,h.success(`已选择收货信息: ${e.name}`)),C.value=!1,Q.value=[]},fe=async()=>{M.value=!0;try{await new Promise(e=>setTimeout(e,500)),M.value=!1}catch{h.error("获取订单详情失败"),M.value=!1}};return Pe(()=>{fe()}),(e,t)=>{const c=d("a-button"),m=d("a-space"),A=d("a-page-header"),ge=d("a-step"),ke=d("a-steps"),R=d("a-card"),k=d("a-col"),xe=d("a-tag"),V=d("a-row"),he=d("a-input-number"),N=d("a-table"),f=d("a-descriptions-item"),X=d("a-descriptions"),b=d("a-tab-pane"),z=d("a-tabs"),be=d("a-upload-dragger"),Ie=d("a-empty"),E=d("a-timeline-item"),j=d("a-timeline"),Ne=d("a-textarea"),H=d("a-form-item"),De=d("a-form"),Re=d("a-modal");return u(),x("div",Te,[n(A,{title:"订单详情 - "+s.value.poNo,"sub-title":"状态："+L(s.value.status),onBack:le},{extra:a(()=>[n(m,null,{default:a(()=>[U.value?(u(),y(c,{key:0},{default:a(()=>t[3]||(t[3]=[r("取消订单（⚠️见需求）")])),_:1})):g("",!0)]),_:1})]),_:1},8,["title","sub-title"]),i("div",Se,[n(R,{title:"订单流程"},{default:a(()=>[n(ke,{current:Z.value,size:"small"},{default:a(()=>[(u(!0),x(D,null,B(Y.value,(o,p)=>(u(),y(ge,{key:p,title:o.title},{description:a(()=>[i("div",null,l(o.time?T(o.time):"未开始"),1),i("div",null,l(o.operator||""),1)]),_:2},1032,["title"]))),128))]),_:1},8,["current"])]),_:1})]),n(R,{title:"基本信息",class:"detail-card"},{default:a(()=>[n(V,{gutter:24},{default:a(()=>[n(k,{span:8},{default:a(()=>[i("div",Me,[t[4]||(t[4]=i("span",{class:"label"},"订单号：",-1)),i("span",ze,l(s.value.poNo),1)])]),_:1}),n(k,{span:8},{default:a(()=>[i("div",Be,[t[5]||(t[5]=i("span",{class:"label"},"订单状态：",-1)),i("span",Fe,[n(xe,{color:oe(s.value.status)},{default:a(()=>[r(l(L(s.value.status)),1)]),_:1},8,["color"])])])]),_:1}),n(k,{span:8},{default:a(()=>[i("div",$e,[t[6]||(t[6]=i("span",{class:"label"},"下单时间：",-1)),i("span",Ue,l(T(s.value.createTime)),1)])]),_:1}),n(k,{span:8},{default:a(()=>[i("div",Le,[t[7]||(t[7]=i("span",{class:"label"},"采购员：",-1)),i("span",Ve,l(s.value.contactPerson),1)])]),_:1}),n(k,{span:8},{default:a(()=>[i("div",Xe,[t[8]||(t[8]=i("span",{class:"label"},"联系电话：",-1)),i("span",Ee,l(s.value.contactPhone),1)])]),_:1}),n(k,{span:8},{default:a(()=>[i("div",je,[t[9]||(t[9]=i("span",{class:"label"},"付款方式：",-1)),i("span",He,l(s.value.paymentMethod),1)])]),_:1}),n(k,{span:8},{default:a(()=>[i("div",Ke,[t[10]||(t[10]=i("span",{class:"label"},"付款条件：",-1)),i("span",Oe,l(s.value.paymentTerms),1)])]),_:1}),n(k,{span:8},{default:a(()=>[i("div",Ge,[t[11]||(t[11]=i("span",{class:"label"},"总金额：",-1)),i("span",We,"¥"+l(_(s.value.totalAmount)),1)])]),_:1}),n(k,{span:8},{default:a(()=>[i("div",Ye,[t[12]||(t[12]=i("span",{class:"label"},"当前剩余账期额度：",-1)),i("span",Ze,"¥"+l(_(s.value.residue)),1)])]),_:1})]),_:1})]),_:1}),n(R,{title:"物料明细",class:"detail-card"},{default:a(()=>[n(N,{columns:J,"data-source":s.value.items,pagination:!1,size:"middle",scroll:{x:1500}},{bodyCell:a(({column:o,record:p,index:K})=>[o.dataIndex==="quantity"&&s.value.status==="draft"?(u(),y(he,{key:0,value:p.quantity,"onUpdate:value":S=>p.quantity=S,min:1,onChange:S=>ce(p,K),style:{width:"100%"}},null,8,["value","onUpdate:value","onChange"])):o.dataIndex==="quantity"?(u(),x(D,{key:1},[r(l(p.quantity),1)],64)):g("",!0),o.dataIndex==="shippedQuantity"?(u(),x(D,{key:2},[r(l(s.value.status==="draft"?"-":p.shippedQuantity),1)],64)):g("",!0),o.dataIndex==="receivedQuantity"?(u(),x(D,{key:3},[r(l(s.value.status==="draft"?"-":p.receivedQuantity),1)],64)):g("",!0),o.dataIndex==="cancelledQuantity"?(u(),x(D,{key:4},[r(l(s.value.status==="draft"?"-":p.cancelledQuantity),1)],64)):g("",!0),o.dataIndex==="actions"&&s.value.status==="draft"?(u(),y(m,{key:5},{default:a(()=>[i("a",{onClick:S=>me(p,K),style:{color:"#ff4d4f"}},"删除",8,Je)]),_:2},1024)):o.dataIndex==="actions"?(u(),y(m,{key:6},{default:a(()=>[i("a",{onClick:S=>pe(p)},"取消",8,et)]),_:2},1024)):g("",!0)]),_:1},8,["data-source"]),i("div",tt,[n(V,{justify:"end"},{default:a(()=>[n(k,{span:8},{default:a(()=>[i("div",at,[t[13]||(t[13]=i("span",{class:"label"},"物料总数：",-1)),i("span",nt,l(de())+" 件",1)]),i("div",st,[t[14]||(t[14]=i("span",{class:"label"},"物料总价：",-1)),i("span",it,"¥"+l(_(s.value.subtotalAmount)),1)]),i("div",lt,[t[15]||(t[15]=i("span",{class:"label"},"运费：",-1)),i("span",ot,"¥"+l(_(s.value.shippingFee)),1)]),i("div",dt,[t[16]||(t[16]=i("span",{class:"label"},"应付总额：",-1)),i("span",ut,"¥"+l(_(s.value.totalAmount)),1)])]),_:1})]),_:1})])]),_:1}),n(R,{title:"收货与开票信息",class:"detail-card"},{default:a(()=>[n(z,{"default-active-key":"1"},{default:a(()=>[n(b,{key:"1",tab:"收货信息"},{default:a(()=>[s.value.status==="draft"?(u(),x("div",rt,[n(c,{type:"primary",onClick:ve},{default:a(()=>t[17]||(t[17]=[r("选择收货信息")])),_:1})])):g("",!0),n(X,{column:2,bordered:""},{default:a(()=>[n(f,{label:"联系人"},{default:a(()=>[r(l(s.value.receiverName),1)]),_:1}),n(f,{label:"手机号"},{default:a(()=>[r(l(s.value.receiverPhone),1)]),_:1}),n(f,{label:"地区",span:2},{default:a(()=>[r(l(s.value.receiverRegion),1)]),_:1}),n(f,{label:"详细地址",span:2},{default:a(()=>[r(l(s.value.receiverAddress),1)]),_:1}),n(f,{label:"备注",span:2},{default:a(()=>[r(l(s.value.remark),1)]),_:1})]),_:1})]),_:1}),n(b,{key:"2",tab:"开票信息"},{default:a(()=>[n(X,{column:2,bordered:""},{default:a(()=>[n(f,{label:"发票抬头"},{default:a(()=>[r(l(s.value.invoiceTitle),1)]),_:1}),n(f,{label:"税号"},{default:a(()=>[r(l(s.value.taxId),1)]),_:1}),n(f,{label:"注册地址",span:2},{default:a(()=>[r(l(s.value.invoiceRegisteredAddress),1)]),_:1}),n(f,{label:"电话"},{default:a(()=>[r(l(s.value.invoicePhone),1)]),_:1}),n(f,{label:"开户行"},{default:a(()=>[r(l(s.value.invoiceBankName),1)]),_:1}),n(f,{label:"银行账户",span:2},{default:a(()=>[r(l(s.value.invoiceBankAccount),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),n(R,{title:"相关单据",class:"detail-card"},{default:a(()=>[n(z,{"default-active-key":"statement"},{default:a(()=>[n(b,{key:"2",tab:"送货单"},{default:a(()=>[n(N,{columns:te,"data-source":q.value.deliveries,pagination:{pageSize:5},size:"small"},null,8,["data-source"])]),_:1}),n(b,{key:"3",tab:"退货单"},{default:a(()=>[n(N,{columns:ae,"data-source":q.value.returns,pagination:{pageSize:5},size:"small"},null,8,["data-source"])]),_:1}),n(b,{key:"statement",tab:"对账单"},{default:a(()=>[n(N,{columns:W,"data-source":q.value.statements,pagination:{pageSize:5},size:"small"},null,8,["data-source"])]),_:1}),n(b,{key:"1",tab:"付款单"},{default:a(()=>[n(N,{columns:ee,"data-source":q.value.payments,pagination:{pageSize:5},size:"small"},null,8,["data-source"])]),_:1})]),_:1})]),_:1}),n(R,{title:"附件与备注",class:"detail-card"},{default:a(()=>[n(z,{"default-active-key":"1"},{default:a(()=>[n(b,{key:"1",tab:"附件资料"},{default:a(()=>[se.value?(u(),y(be,{key:0,name:"file",multiple:!0,action:"/api/upload",onChange:ue},{default:a(()=>[i("p",ct,[n(Qe(qe))]),t[18]||(t[18]=i("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),t[19]||(t[19]=i("p",{class:"ant-upload-hint"},"支持单个或批量上传。支持 PDF, Word, Excel, 图片等格式文件",-1))]),_:1})):g("",!0),s.value.attachments&&s.value.attachments.length>0?(u(),y(N,{key:1,columns:ne,"data-source":s.value.attachments,pagination:!1,size:"small"},null,8,["data-source"])):(u(),y(Ie,{key:2,description:"暂无附件"}))]),_:1}),n(b,{key:"2",tab:"沟通记录"},{default:a(()=>[n(j,null,{default:a(()=>[(u(!0),x(D,null,B(s.value.comments,(o,p)=>(u(),y(E,{key:p},{default:a(()=>[i("div",mt,[i("span",pt,l(o.userName),1),i("span",vt,l(T(o.time)),1)]),i("div",yt,l(o.content),1)]),_:2},1024))),128))]),_:1}),ie.value?(u(),y(De,{key:0,layout:"inline",class:"comment-form"},{default:a(()=>[n(H,{style:{flex:"1"}},{default:a(()=>[n(Ne,{value:P.value,"onUpdate:value":t[0]||(t[0]=o=>P.value=o),placeholder:"添加备注...",rows:2},null,8,["value"])]),_:1}),n(H,null,{default:a(()=>[n(c,{type:"primary",onClick:re},{default:a(()=>t[20]||(t[20]=[r("发送")])),_:1})]),_:1})]),_:1})):g("",!0)]),_:1}),n(b,{key:"3",tab:"操作历史"},{default:a(()=>[n(j,null,{default:a(()=>[(u(!0),x(D,null,B(s.value.operationLogs,(o,p)=>(u(),y(E,{key:p},{default:a(()=>[i("div",_t,l(T(o.time)),1),i("div",ft,[i("span",gt,l(o.userName),1),i("span",null,l(o.action),1)]),o.detail?(u(),x("div",kt,l(o.detail),1)):g("",!0)]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})]),_:1}),i("div",xt,[n(m,null,{default:a(()=>[U.value?(u(),y(c,{key:0},{default:a(()=>t[21]||(t[21]=[r("取消订单（⚠️见需求）")])),_:1})):g("",!0)]),_:1})]),n(Re,{visible:C.value,"onUpdate:visible":t[1]||(t[1]=o=>C.value=o),title:"选择收货信息",width:800,onOk:_e,onCancel:t[2]||(t[2]=o=>C.value=!1)},{default:a(()=>[n(N,{columns:G,"data-source":$.value,pagination:!1,"row-selection":{type:"radio",selectedRowKeys:Q.value,onChange:ye},size:"small"},null,8,["data-source","row-selection"])]),_:1},8,["visible"])])}}},Rt=Ae(ht,[["__scopeId","data-v-f65433ea"]]);export{Rt as default};
